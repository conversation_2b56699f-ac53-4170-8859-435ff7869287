import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Drawer,
  Descriptions,
  Steps,
  Timeline,
  Row,
  Col,
  Typography,
  Avatar,
  Divider,
  message,
  Modal,
  Form,
  DatePicker,
  Statistic
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  SearchOutlined,
  ExportOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  UserOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  customerAvatar?: string;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
  trackingNumber?: string;
  notes?: string;
}

interface OrderItem {
  id: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  total: number;
}

const OrderManagement: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [form] = Form.useForm();

  // Statistics
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    shippedOrders: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchOrders();
    fetchOrderStats();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const params: any = {
        limit: 100
      };

      if (searchText) params.search = searchText;
      if (selectedStatus) params.status = selectedStatus;
      if (selectedPaymentStatus) params.paymentStatus = selectedPaymentStatus;
      if (dateRange) {
        params.startDate = dateRange[0].toISOString();
        params.endDate = dateRange[1].toISOString();
      }

      const response = await axios.get('/api/v1/orders', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        params
      });
      
      if (response.data.success) {
        setOrders(response.data.data.orders || []);
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      message.error('Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const fetchOrderStats = async () => {
    try {
      const response = await axios.get('/api/v1/analytics/sales/overview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.data.success) {
        const data = response.data.data.overview;
        setStats({
          totalOrders: data.total_orders || 0,
          pendingOrders: data.pending_orders || 0,
          shippedOrders: data.shipped_orders || 0,
          totalRevenue: data.total_revenue || 0
        });
      }
    } catch (error) {
      console.error('Failed to fetch order stats:', error);
    }
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailDrawerVisible(true);
  };

  const handleUpdateOrderStatus = (order: Order) => {
    setSelectedOrder(order);
    form.setFieldsValue({
      status: order.status,
      trackingNumber: order.trackingNumber,
      notes: order.notes
    });
    setIsStatusModalVisible(true);
  };

  const handleStatusUpdate = async () => {
    try {
      const values = await form.validateFields();
      
      await axios.put(`/api/v1/orders/${selectedOrder?.id}/status`, values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      message.success('Order status updated successfully');
      setIsStatusModalVisible(false);
      fetchOrders();
    } catch (error) {
      console.error('Failed to update order status:', error);
      message.error('Failed to update order status');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'confirmed': return 'blue';
      case 'shipped': return 'purple';
      case 'delivered': return 'green';
      case 'cancelled': return 'red';
      case 'refunded': return 'gray';
      default: return 'default';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green';
      case 'pending': return 'orange';
      case 'failed': return 'red';
      case 'refunded': return 'gray';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <ClockCircleOutlined />;
      case 'confirmed': return <CheckCircleOutlined />;
      case 'shipped': return <TruckOutlined />;
      case 'delivered': return <CheckCircleOutlined />;
      case 'cancelled': return <ExclamationCircleOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  const columns: ColumnsType<Order> = [
    {
      title: 'Order #',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 120,
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Customer',
      key: 'customer',
      width: 200,
      render: (_, record: Order) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            src={record.customerAvatar} 
            icon={<UserOutlined />} 
            size="small"
            style={{ marginRight: '8px' }}
          />
          <div>
            <Text strong>{record.customerName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.customerEmail}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      render: (amount: number) => (
        <Text strong style={{ color: '#52c41a' }}>
          ${amount.toFixed(2)}
        </Text>
      ),
      sorter: (a, b) => a.totalAmount - b.totalAmount
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Payment',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status: string) => (
        <Tag color={getPaymentStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: Order) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record)}
            title="View Details"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleUpdateOrderStatus(record)}
            title="Update Status"
          />
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={stats.totalOrders}
              prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Pending Orders"
              value={stats.pendingOrders}
              prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Shipped Orders"
              value={stats.shippedOrders}
              prefix={<TruckOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={stats.totalRevenue}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                📦 Order Management
              </Title>
            </Col>
            <Col>
              <Space>
                <Button icon={<ExportOutlined />}>Export Orders</Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Filters */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Search
              placeholder="Search orders..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={fetchOrders}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Order Status"
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="pending">Pending</Option>
              <Option value="confirmed">Confirmed</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
              <Option value="cancelled">Cancelled</Option>
              <Option value="refunded">Refunded</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Payment Status"
              value={selectedPaymentStatus}
              onChange={setSelectedPaymentStatus}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="pending">Pending</Option>
              <Option value="paid">Paid</Option>
              <Option value="failed">Failed</Option>
              <Option value="refunded">Refunded</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Button type="primary" onClick={fetchOrders} block>
              Filter
            </Button>
          </Col>
        </Row>

        {/* Orders Table */}
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            total: orders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} orders`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Order Detail Drawer */}
      <Drawer
        title="Order Details"
        placement="right"
        onClose={() => setIsDetailDrawerVisible(false)}
        open={isDetailDrawerVisible}
        width={700}
      >
        {selectedOrder && (
          <div>
            {/* Order Header */}
            <div style={{ marginBottom: '24px' }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={4} style={{ margin: 0 }}>
                    Order #{selectedOrder.orderNumber}
                  </Title>
                  <Text type="secondary">
                    Created on {dayjs(selectedOrder.createdAt).format('MMMM DD, YYYY at HH:mm')}
                  </Text>
                </Col>
                <Col>
                  <Space direction="vertical" align="end">
                    <Tag color={getStatusColor(selectedOrder.status)} icon={getStatusIcon(selectedOrder.status)}>
                      {selectedOrder.status.toUpperCase()}
                    </Tag>
                    <Tag color={getPaymentStatusColor(selectedOrder.paymentStatus)}>
                      {selectedOrder.paymentStatus.toUpperCase()}
                    </Tag>
                  </Space>
                </Col>
              </Row>
            </div>

            <Divider />

            {/* Customer Information */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Customer Information</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Name">
                  <Space>
                    <Avatar src={selectedOrder.customerAvatar} icon={<UserOutlined />} size="small" />
                    {selectedOrder.customerName}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  {selectedOrder.customerEmail}
                </Descriptions.Item>
              </Descriptions>
            </div>

            {/* Shipping Address */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Shipping Address</Title>
              <Text>
                {selectedOrder.shippingAddress.street}<br />
                {selectedOrder.shippingAddress.city}, {selectedOrder.shippingAddress.state} {selectedOrder.shippingAddress.zipCode}<br />
                {selectedOrder.shippingAddress.country}
              </Text>
            </div>

            {/* Order Items */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Order Items</Title>
              {selectedOrder.items?.map((item, index) => (
                <div key={index} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px 0',
                  borderBottom: index < selectedOrder.items.length - 1 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      src={item.productImage}
                      shape="square"
                      size={50}
                      style={{ marginRight: '12px' }}
                    />
                    <div>
                      <Text strong>{item.productName}</Text>
                      <br />
                      <Text type="secondary">Qty: {item.quantity} × ${item.price.toFixed(2)}</Text>
                    </div>
                  </div>
                  <Text strong>${item.total.toFixed(2)}</Text>
                </div>
              ))}

              <div style={{
                marginTop: '16px',
                padding: '16px 0',
                borderTop: '2px solid #f0f0f0',
                textAlign: 'right'
              }}>
                <Text strong style={{ fontSize: '18px', color: '#52c41a' }}>
                  Total: ${selectedOrder.totalAmount.toFixed(2)}
                </Text>
              </div>
            </div>

            {/* Payment Information */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Payment Information</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Payment Method">
                  {selectedOrder.paymentMethod}
                </Descriptions.Item>
                <Descriptions.Item label="Payment Status">
                  <Tag color={getPaymentStatusColor(selectedOrder.paymentStatus)}>
                    {selectedOrder.paymentStatus.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                {selectedOrder.trackingNumber && (
                  <Descriptions.Item label="Tracking Number">
                    <Text copyable>{selectedOrder.trackingNumber}</Text>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>

            {/* Order Timeline */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Order Timeline</Title>
              <Timeline
                items={[
                  {
                    color: 'green',
                    children: `Order placed - ${dayjs(selectedOrder.createdAt).format('MMM DD, YYYY HH:mm')}`
                  },
                  {
                    color: selectedOrder.status === 'confirmed' ? 'green' : 'gray',
                    children: 'Order confirmed'
                  },
                  {
                    color: selectedOrder.status === 'shipped' ? 'green' : 'gray',
                    children: 'Order shipped'
                  },
                  {
                    color: selectedOrder.status === 'delivered' ? 'green' : 'gray',
                    children: 'Order delivered'
                  }
                ]}
              />
            </div>

            {/* Action Buttons */}
            <div style={{ textAlign: 'center' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setIsDetailDrawerVisible(false);
                    handleUpdateOrderStatus(selectedOrder);
                  }}
                >
                  Update Status
                </Button>
                <Button icon={<ExportOutlined />}>
                  Export Order
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>

      {/* Update Status Modal */}
      <Modal
        title="Update Order Status"
        open={isStatusModalVisible}
        onOk={handleStatusUpdate}
        onCancel={() => setIsStatusModalVisible(false)}
        okText="Update"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="status"
            label="Order Status"
            rules={[{ required: true, message: 'Please select order status' }]}
          >
            <Select>
              <Option value="pending">Pending</Option>
              <Option value="confirmed">Confirmed</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
              <Option value="cancelled">Cancelled</Option>
              <Option value="refunded">Refunded</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="trackingNumber"
            label="Tracking Number"
          >
            <Input placeholder="Enter tracking number" />
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={3} placeholder="Add notes..." />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OrderManagement;
