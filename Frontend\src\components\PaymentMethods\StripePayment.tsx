import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  message,
  Spin,
  Select,
} from 'antd';
import {
  CreditCardOutlined,
  LockOutlined,
  CalendarOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { createStripePaymentIntent, confirmStripePayment } from '../../store/slices/paymentSlice';

const { Option } = Select;

interface StripePaymentProps {
  orderId: string;
  amount: number;
  currency?: string;
  onSuccess?: (payment: any) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
}

const StripePayment: React.FC<StripePaymentProps> = ({
  orderId,
  amount,
  currency = 'USD',
  onSuccess,
  onError,
  disabled = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, processingPayment, stripeClientSecret, error } = useSelector(
    (state: RootState) => state.payments
  );

  const [form] = Form.useForm();
  const [isStripeLoaded, setIsStripeLoaded] = useState(false);
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);

  useEffect(() => {
    // In a real app, you would load Stripe SDK here
    // For demo purposes, we'll simulate Stripe being loaded
    const timer = setTimeout(() => {
      setIsStripeLoaded(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (error) {
      message.error(error);
      onError?.(error);
    }
  }, [error, onError]);

  const handleCreatePaymentIntent = async () => {
    try {
      const result = await dispatch(createStripePaymentIntent({
        orderId,
        amount,
        currency,
      })).unwrap();

      setPaymentIntentId(result.paymentIntentId);
      message.success('Payment intent created. Please enter your card details.');
    } catch (error: any) {
      message.error(error.message || 'Failed to create payment intent');
      onError?.(error);
    }
  };

  const handleSubmitPayment = async (values: any) => {
    if (!paymentIntentId) {
      await handleCreatePaymentIntent();
      return;
    }

    try {
      // In a real app, you would use Stripe Elements to handle card details
      // For demo purposes, we'll simulate the payment process
      
      // Validate card details (basic validation for demo)
      const { cardNumber, expiryDate, cvv, cardholderName } = values;
      
      if (!cardNumber || !expiryDate || !cvv || !cardholderName) {
        message.error('Please fill in all card details');
        return;
      }

      // Simulate Stripe payment confirmation
      message.loading('Processing payment...', 0);
      
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const payment = await dispatch(confirmStripePayment(paymentIntentId)).unwrap();
      
      message.destroy();
      message.success('Payment completed successfully!');
      onSuccess?.(payment);
      
    } catch (error: any) {
      message.destroy();
      message.error(error.message || 'Payment failed');
      onError?.(error);
    }
  };

  const formatCardNumber = (value: string) => {
    // Remove all non-digit characters
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // Add spaces every 4 digits
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  if (!isStripeLoaded) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <p style={{ marginTop: '16px' }}>Loading Stripe...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <CreditCardOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          Credit Card Payment
        </div>
      }
      extra={
        <div style={{ display: 'flex', alignItems: 'center', color: '#52c41a' }}>
          <LockOutlined style={{ marginRight: '4px' }} />
          Secure
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmitPayment}
        disabled={disabled}
      >
        <Form.Item
          name="cardholderName"
          label="Cardholder Name"
          rules={[{ required: true, message: 'Please enter cardholder name' }]}
        >
          <Input
            placeholder="John Doe"
            size="large"
            prefix={<CreditCardOutlined />}
          />
        </Form.Item>

        <Form.Item
          name="cardNumber"
          label="Card Number"
          rules={[
            { required: true, message: 'Please enter card number' },
            { min: 19, message: 'Please enter a valid card number' }
          ]}
        >
          <Input
            placeholder="1234 5678 9012 3456"
            size="large"
            maxLength={19}
            onChange={(e) => {
              const formatted = formatCardNumber(e.target.value);
              form.setFieldsValue({ cardNumber: formatted });
            }}
            prefix={<CreditCardOutlined />}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="expiryDate"
              label="Expiry Date"
              rules={[
                { required: true, message: 'Please enter expiry date' },
                { len: 5, message: 'Please enter valid expiry date (MM/YY)' }
              ]}
            >
              <Input
                placeholder="MM/YY"
                size="large"
                maxLength={5}
                onChange={(e) => {
                  const formatted = formatExpiryDate(e.target.value);
                  form.setFieldsValue({ expiryDate: formatted });
                }}
                prefix={<CalendarOutlined />}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="cvv"
              label="CVV"
              rules={[
                { required: true, message: 'Please enter CVV' },
                { min: 3, max: 4, message: 'CVV must be 3-4 digits' }
              ]}
            >
              <Input
                placeholder="123"
                size="large"
                maxLength={4}
                prefix={<SafetyOutlined />}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="billingCountry"
          label="Billing Country"
          rules={[{ required: true, message: 'Please select billing country' }]}
        >
          <Select placeholder="Select country" size="large">
            <Option value="US">United States</Option>
            <Option value="CA">Canada</Option>
            <Option value="GB">United Kingdom</Option>
            <Option value="DE">Germany</Option>
            <Option value="FR">France</Option>
            <Option value="AU">Australia</Option>
            <Option value="JP">Japan</Option>
          </Select>
        </Form.Item>

        <div style={{ 
          background: '#f6f8fa', 
          padding: '16px', 
          borderRadius: '8px', 
          marginBottom: '24px',
          border: '1px solid #e1e4e8'
        }}>
          <Row justify="space-between" align="middle">
            <Col>
              <strong>Total Amount:</strong>
            </Col>
            <Col>
              <span style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                ${amount.toFixed(2)} {currency}
              </span>
            </Col>
          </Row>
        </div>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            size="large"
            loading={loading || processingPayment}
            disabled={disabled}
            style={{
              width: '100%',
              height: '50px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 'bold',
            }}
          >
            {loading || processingPayment ? (
              'Processing...'
            ) : paymentIntentId ? (
              `Pay $${amount.toFixed(2)} ${currency}`
            ) : (
              'Create Payment Intent'
            )}
          </Button>
        </Form.Item>

        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          <p style={{ fontSize: '12px', color: '#8c8c8c' }}>
            <LockOutlined style={{ marginRight: '4px' }} />
            Your payment information is secure and encrypted
          </p>
        </div>
      </Form>
    </Card>
  );
};

export default StripePayment;
