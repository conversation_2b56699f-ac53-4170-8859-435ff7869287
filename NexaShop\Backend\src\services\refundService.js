const { Refund, RefundItem, RefundStatusHistory } = require('../models/Refund');
const { Order, OrderItem, Payment, User, Product } = require('../models');
const stripeService = require('./stripeService');
const paypalService = require('./paypalService');
const emailService = require('./emailService');
const notificationService = require('./notificationService');
const { v4: uuidv4 } = require('uuid');

class RefundService {
  constructor() {
    this.maxRefundDays = 30; // Maximum days to allow refund
  }

  /**
   * Create a new refund request
   * @param {Object} refundData - Refund request data
   * @returns {Object} Created refund
   */
  async createRefund(refundData) {
    try {
      const {
        orderId,
        paymentId,
        userId,
        amount,
        reason,
        description,
        refundType = 'full',
        items = [],
        requestedBy
      } = refundData;

      // Validate order and payment
      const order = await Order.findByPk(orderId, {
        include: [
          {
            model: Payment,
            as: 'payments'
          },
          {
            model: OrderItem,
            as: 'items',
            include: [{ model: Product, as: 'product' }]
          }
        ]
      });

      if (!order) {
        throw new Error('Order not found');
      }

      const payment = await Payment.findByPk(paymentId);
      if (!payment) {
        throw new Error('Payment not found');
      }

      // Validate refund eligibility
      await this.validateRefundEligibility(order, payment, amount);

      // Generate unique refund ID
      const refundId = `ref_${uuidv4().replace(/-/g, '').substring(0, 16)}`;

      // Create refund record
      const refund = await Refund.create({
        refund_id: refundId,
        order_id: orderId,
        payment_id: paymentId,
        user_id: userId,
        amount: amount,
        currency: payment.currency || 'USD',
        reason: reason,
        description: description,
        refund_type: refundType,
        payment_method: payment.payment_method,
        receipt_email: order.user?.email,
        receipt_number: `RCP-${Date.now()}`,
        metadata: {
          requested_by: requestedBy,
          order_number: order.order_number,
          original_amount: payment.amount
        }
      });

      // Create refund items for partial refunds
      if (refundType === 'partial' && items.length > 0) {
        const refundItems = items.map(item => ({
          refund_id: refund.id,
          order_item_id: item.orderItemId,
          product_id: item.productId,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total_amount: item.totalAmount,
          reason: item.reason,
          condition: item.condition,
          return_required: item.returnRequired !== false
        }));

        await RefundItem.bulkCreate(refundItems);
      }

      // Create status history
      await this.createStatusHistory(refund.id, null, 'pending', requestedBy, 'Refund request created');

      // Send notification
      await this.sendRefundNotification(refund, 'created');

      return {
        success: true,
        refund: await this.getRefundById(refund.id)
      };
    } catch (error) {
      console.error('Create refund error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process refund through payment provider
   * @param {string} refundId - Refund ID
   * @param {string} processedBy - Admin user ID
   * @returns {Object} Processing result
   */
  async processRefund(refundId, processedBy) {
    try {
      const refund = await Refund.findByPk(refundId, {
        include: [
          { model: Order, as: 'order' },
          { model: Payment, as: 'payment' },
          { model: User, as: 'user' }
        ]
      });

      if (!refund) {
        throw new Error('Refund not found');
      }

      if (refund.status !== 'pending') {
        throw new Error(`Cannot process refund with status: ${refund.status}`);
      }

      // Update status to processing
      await this.updateRefundStatus(refundId, 'processing', processedBy, 'Processing refund');

      let providerResult;
      
      // Process refund based on payment method
      switch (refund.payment_method) {
        case 'stripe':
          providerResult = await this.processStripeRefund(refund);
          break;
        case 'paypal':
          providerResult = await this.processPayPalRefund(refund);
          break;
        case 'manual':
          providerResult = await this.processManualRefund(refund);
          break;
        default:
          throw new Error(`Unsupported payment method: ${refund.payment_method}`);
      }

      // Update refund with provider response
      await refund.update({
        provider_refund_id: providerResult.refundId,
        provider_response: providerResult.response,
        processed_by: processedBy,
        processed_at: new Date(),
        status: providerResult.status,
        failure_reason: providerResult.error,
        expected_arrival: providerResult.expectedArrival
      });

      // Create status history
      await this.createStatusHistory(
        refundId, 
        'processing', 
        providerResult.status, 
        processedBy, 
        providerResult.error || 'Refund processed successfully'
      );

      // Send notification
      await this.sendRefundNotification(refund, providerResult.status);

      return {
        success: true,
        refund: await this.getRefundById(refundId),
        providerResult
      };
    } catch (error) {
      console.error('Process refund error:', error);
      
      // Update refund status to failed
      await this.updateRefundStatus(refundId, 'failed', processedBy, error.message);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process Stripe refund
   * @param {Object} refund - Refund object
   * @returns {Object} Stripe refund result
   */
  async processStripeRefund(refund) {
    try {
      const stripeResult = await stripeService.createRefund({
        paymentIntentId: refund.payment.provider_payment_id,
        amount: Math.round(refund.amount * 100), // Convert to cents
        reason: this.mapReasonToStripe(refund.reason),
        metadata: {
          refund_id: refund.refund_id,
          order_id: refund.order_id
        }
      });

      if (stripeResult.success) {
        return {
          refundId: stripeResult.refundId,
          status: this.mapStripeStatusToInternal(stripeResult.data.status),
          response: stripeResult.data,
          expectedArrival: this.calculateExpectedArrival('stripe')
        };
      } else {
        return {
          status: 'failed',
          error: stripeResult.error,
          response: stripeResult
        };
      }
    } catch (error) {
      return {
        status: 'failed',
        error: error.message,
        response: { error: error.message }
      };
    }
  }

  /**
   * Process PayPal refund
   * @param {Object} refund - Refund object
   * @returns {Object} PayPal refund result
   */
  async processPayPalRefund(refund) {
    try {
      const paypalResult = await paypalService.refundCapture(
        refund.payment.provider_payment_id,
        {
          amount: refund.amount.toString(),
          currency: refund.currency,
          note: refund.description || 'Refund processed'
        }
      );

      if (paypalResult.success) {
        return {
          refundId: paypalResult.refundId,
          status: this.mapPayPalStatusToInternal(paypalResult.status),
          response: paypalResult.data,
          expectedArrival: this.calculateExpectedArrival('paypal')
        };
      } else {
        return {
          status: 'failed',
          error: paypalResult.error,
          response: paypalResult
        };
      }
    } catch (error) {
      return {
        status: 'failed',
        error: error.message,
        response: { error: error.message }
      };
    }
  }

  /**
   * Process manual refund
   * @param {Object} refund - Refund object
   * @returns {Object} Manual refund result
   */
  async processManualRefund(refund) {
    // For manual refunds, mark as requires_action for admin to handle
    return {
      refundId: `manual_${refund.refund_id}`,
      status: 'requires_action',
      response: { type: 'manual', message: 'Manual refund requires admin action' },
      expectedArrival: this.calculateExpectedArrival('manual')
    };
  }

  /**
   * Validate refund eligibility
   * @param {Object} order - Order object
   * @param {Object} payment - Payment object
   * @param {number} amount - Refund amount
   */
  async validateRefundEligibility(order, payment, amount) {
    // Check if order is refundable
    if (!['delivered', 'shipped', 'completed'].includes(order.status)) {
      throw new Error('Order is not eligible for refund');
    }

    // Check refund time limit
    const daysSinceOrder = Math.floor((new Date() - new Date(order.created_at)) / (1000 * 60 * 60 * 24));
    if (daysSinceOrder > this.maxRefundDays) {
      throw new Error(`Refund period expired. Maximum ${this.maxRefundDays} days allowed.`);
    }

    // Check payment status
    if (payment.status !== 'completed') {
      throw new Error('Payment is not completed');
    }

    // Check refund amount
    if (amount > payment.amount) {
      throw new Error('Refund amount cannot exceed payment amount');
    }

    // Check existing refunds
    const existingRefunds = await Refund.sum('amount', {
      where: {
        payment_id: payment.id,
        status: ['succeeded', 'processing']
      }
    }) || 0;

    if (existingRefunds + amount > payment.amount) {
      throw new Error('Total refund amount would exceed payment amount');
    }
  }

  /**
   * Update refund status
   * @param {string} refundId - Refund ID
   * @param {string} status - New status
   * @param {string} changedBy - User who changed status
   * @param {string} reason - Reason for change
   */
  async updateRefundStatus(refundId, status, changedBy, reason) {
    const refund = await Refund.findByPk(refundId);
    if (!refund) {
      throw new Error('Refund not found');
    }

    const oldStatus = refund.status;
    await refund.update({ status });

    await this.createStatusHistory(refundId, oldStatus, status, changedBy, reason);
  }

  /**
   * Create status history record
   * @param {string} refundId - Refund ID
   * @param {string} fromStatus - Previous status
   * @param {string} toStatus - New status
   * @param {string} changedBy - User who changed status
   * @param {string} reason - Reason for change
   */
  async createStatusHistory(refundId, fromStatus, toStatus, changedBy, reason) {
    await RefundStatusHistory.create({
      refund_id: refundId,
      from_status: fromStatus,
      to_status: toStatus,
      changed_by: changedBy,
      reason: reason,
      metadata: {
        timestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Get refund by ID with full details
   * @param {string} refundId - Refund ID
   * @returns {Object} Refund details
   */
  async getRefundById(refundId) {
    return await Refund.findByPk(refundId, {
      include: [
        {
          model: Order,
          as: 'order',
          include: [{ model: User, as: 'user' }]
        },
        { model: Payment, as: 'payment' },
        {
          model: RefundItem,
          as: 'items',
          include: [{ model: Product, as: 'product' }]
        },
        {
          model: RefundStatusHistory,
          as: 'statusHistory',
          order: [['created_at', 'DESC']]
        }
      ]
    });
  }

  /**
   * Send refund notification
   * @param {Object} refund - Refund object
   * @param {string} event - Event type
   */
  async sendRefundNotification(refund, event) {
    try {
      const user = refund.user || refund.order?.user;
      if (!user) return;

      const emailData = {
        to: user.email,
        refund: refund,
        order: refund.order,
        event: event
      };

      switch (event) {
        case 'created':
          await emailService.sendRefundRequestEmail(emailData);
          break;
        case 'succeeded':
          await emailService.sendRefundCompletedEmail(emailData);
          break;
        case 'failed':
          await emailService.sendRefundFailedEmail(emailData);
          break;
      }

      // Send in-app notification
      await notificationService.createNotification({
        user_id: user.id,
        type: 'refund_update',
        title: `Refund ${event}`,
        message: `Your refund request ${refund.refund_id} has been ${event}`,
        data: { refund_id: refund.id, event }
      });
    } catch (error) {
      console.error('Send refund notification error:', error);
    }
  }

  /**
   * Map internal reason to Stripe reason
   * @param {string} reason - Internal reason
   * @returns {string} Stripe reason
   */
  mapReasonToStripe(reason) {
    const mapping = {
      'requested_by_customer': 'requested_by_customer',
      'duplicate': 'duplicate',
      'fraudulent': 'fraudulent',
      'subscription_canceled': 'requested_by_customer',
      'product_unacceptable': 'requested_by_customer',
      'no_longer_want': 'requested_by_customer',
      'damaged_product': 'requested_by_customer',
      'wrong_product': 'requested_by_customer',
      'not_received': 'requested_by_customer',
      'other': 'requested_by_customer'
    };
    return mapping[reason] || 'requested_by_customer';
  }

  /**
   * Map Stripe status to internal status
   * @param {string} stripeStatus - Stripe status
   * @returns {string} Internal status
   */
  mapStripeStatusToInternal(stripeStatus) {
    const mapping = {
      'pending': 'processing',
      'succeeded': 'succeeded',
      'failed': 'failed',
      'canceled': 'canceled',
      'requires_action': 'requires_action'
    };
    return mapping[stripeStatus] || 'processing';
  }

  /**
   * Map PayPal status to internal status
   * @param {string} paypalStatus - PayPal status
   * @returns {string} Internal status
   */
  mapPayPalStatusToInternal(paypalStatus) {
    const mapping = {
      'COMPLETED': 'succeeded',
      'PENDING': 'processing',
      'FAILED': 'failed',
      'CANCELLED': 'canceled'
    };
    return mapping[paypalStatus] || 'processing';
  }

  /**
   * Calculate expected arrival date for refund
   * @param {string} paymentMethod - Payment method
   * @returns {Date} Expected arrival date
   */
  calculateExpectedArrival(paymentMethod) {
    const now = new Date();
    const businessDays = {
      'stripe': 7,
      'paypal': 3,
      'manual': 14
    };
    
    const days = businessDays[paymentMethod] || 7;
    return new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
  }
}

module.exports = new RefundService();
