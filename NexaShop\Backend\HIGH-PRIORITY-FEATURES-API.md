# NexaShop High Priority Features API Documentation

## Overview

This document describes the API endpoints for the four high-priority features implemented in NexaShop:

1. **Refund System** - Complete refund management with Stripe/PayPal integration
2. **Coupon System** - Flexible coupon creation and validation system
3. **Product Variants** - Product variant management with inventory tracking
4. **Logistics System** - Multi-carrier shipping and tracking integration

## Base URL
```
http://localhost:3000/api/v1
```

## Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## 1. Refund System API

### Get All Refunds
```http
GET /refunds
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status
- `order_id` (optional): Filter by order ID

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "refund_id": "REF-123456",
      "order_id": "uuid",
      "amount": 99.99,
      "currency": "USD",
      "reason": "customer_request",
      "status": "succeeded",
      "refund_type": "full",
      "processed_at": "2024-12-29T10:00:00Z",
      "items": [...],
      "statusHistory": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Refund
```http
POST /refunds
```

**Request Body:**
```json
{
  "order_id": "uuid",
  "amount": 99.99,
  "reason": "customer_request",
  "refund_type": "full",
  "items": [
    {
      "order_item_id": "uuid",
      "quantity": 1,
      "amount": 99.99,
      "reason": "Defective product"
    }
  ],
  "notes": "Customer reported defective item"
}
```

### Get Refund by ID
```http
GET /refunds/:id
```

### Update Refund Status
```http
PUT /refunds/:id/status
```

**Request Body:**
```json
{
  "status": "processing",
  "notes": "Processing refund with payment provider"
}
```

### Process Refund
```http
POST /refunds/:id/process
```

---

## 2. Coupon System API

### Get All Coupons
```http
GET /coupons
```

**Query Parameters:**
- `page`, `limit`: Pagination
- `is_active`: Filter by active status
- `type`: Filter by coupon type

### Create Coupon
```http
POST /coupons
```

**Request Body:**
```json
{
  "code": "SAVE20",
  "name": "20% Off Everything",
  "description": "Get 20% off your entire order",
  "type": "percentage",
  "percentage": 20.00,
  "minimum_amount": 100.00,
  "maximum_discount": 50.00,
  "usage_limit": 1000,
  "usage_limit_per_user": 1,
  "applicable_to": "all",
  "starts_at": "2024-12-29T00:00:00Z",
  "expires_at": "2024-12-31T23:59:59Z",
  "is_active": true
}
```

**Coupon Types:**
- `percentage`: Percentage discount
- `fixed_amount`: Fixed amount discount
- `free_shipping`: Free shipping
- `buy_x_get_y`: Buy X get Y free

### Validate Coupon
```http
POST /coupons/validate
```

**Request Body:**
```json
{
  "code": "SAVE20",
  "user_id": "uuid",
  "cart_total": 150.00,
  "cart_items": [
    {
      "product_id": "uuid",
      "quantity": 2,
      "price": 75.00
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "discount_amount": 30.00,
    "final_total": 120.00,
    "coupon": {
      "id": "uuid",
      "code": "SAVE20",
      "type": "percentage",
      "value": 20.00
    }
  }
}
```

### Apply Coupon to Order
```http
POST /coupons/apply
```

### Get Coupon Usage History
```http
GET /coupons/:id/usage
```

---

## 3. Product Variants API

### Get Variant Attributes
```http
GET /product-variants/attributes
```

### Get Attribute Values
```http
GET /product-variants/attribute-values
```

**Query Parameters:**
- `attribute_id`: Filter by attribute ID

### Create Product Variant
```http
POST /product-variants
```

**Request Body:**
```json
{
  "product_id": "uuid",
  "sku": "PROD-001-RED-L",
  "price": 99.99,
  "compare_at_price": 129.99,
  "inventory_quantity": 50,
  "inventory_policy": "deny",
  "weight": 0.5,
  "dimensions": {
    "length": 10,
    "width": 8,
    "height": 2
  },
  "attributes": [
    {
      "attribute_value_id": "uuid"
    }
  ]
}
```

### Get Product Variants
```http
GET /product-variants
```

**Query Parameters:**
- `product_id`: Filter by product
- `sku`: Search by SKU
- `is_active`: Filter by active status

### Update Variant Inventory
```http
PUT /product-variants/:id/inventory
```

**Request Body:**
```json
{
  "quantity_change": -5,
  "change_type": "sale",
  "reference_type": "order",
  "reference_id": "uuid",
  "notes": "Sold 5 units"
}
```

### Check Variant Availability
```http
GET /product-variants/:id/availability
```

**Query Parameters:**
- `quantity`: Required quantity to check

---

## 4. Logistics System API

### Get Shipping Carriers
```http
GET /logistics/carriers
```

### Get Shipping Services
```http
GET /logistics/services
```

**Query Parameters:**
- `carrier_id`: Filter by carrier
- `service_type`: Filter by service type

### Calculate Shipping Rates
```http
POST /logistics/calculate-rates
```

**Request Body:**
```json
{
  "origin": {
    "country": "US",
    "state": "CA",
    "city": "Los Angeles",
    "postal_code": "90210",
    "address": "123 Main St"
  },
  "destination": {
    "country": "US",
    "state": "NY", 
    "city": "New York",
    "postal_code": "10001",
    "address": "456 Broadway"
  },
  "packages": [
    {
      "weight": 2.5,
      "dimensions": {
        "length": 12,
        "width": 8,
        "height": 6
      },
      "value": 99.99
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "carrier": {
        "id": "uuid",
        "name": "FedEx",
        "code": "fedex"
      },
      "service": {
        "id": "uuid",
        "name": "FedEx Ground",
        "service_type": "standard"
      },
      "rate": {
        "amount": 12.50,
        "currency": "USD",
        "estimated_delivery_date": "2024-12-31T17:00:00Z",
        "transit_time": "2-3 business days"
      }
    }
  ]
}
```

### Create Shipment
```http
POST /logistics/shipments
```

**Request Body:**
```json
{
  "order_id": "uuid",
  "carrier_id": "uuid",
  "service_id": "uuid",
  "origin_address": {
    "name": "NexaShop Warehouse",
    "address": "123 Warehouse St",
    "city": "Los Angeles",
    "state": "CA",
    "postal_code": "90210",
    "country": "US"
  },
  "destination_address": {
    "name": "John Doe",
    "address": "456 Customer Ave",
    "city": "New York", 
    "state": "NY",
    "postal_code": "10001",
    "country": "US"
  },
  "package_details": {
    "weight": 2.5,
    "dimensions": {
      "length": 12,
      "width": 8,
      "height": 6
    },
    "value": 99.99,
    "description": "Electronics"
  }
}
```

### Get Shipments
```http
GET /logistics/shipments
```

### Track Shipment
```http
GET /logistics/shipments/:id/tracking
```

### Generate Shipping Label
```http
POST /logistics/shipments/:id/label
```

---

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate limited to prevent abuse:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated requests

## Webhooks

The system supports webhooks for real-time updates:

### Refund Status Updates
```http
POST /webhooks/refunds
```

### Shipment Tracking Updates
```http
POST /webhooks/shipments
```

---

## Testing

Use the provided test script to verify all endpoints:

```bash
node test-high-priority-features.js
```

## Deployment

Deploy the features using the deployment script:

```bash
node deploy-high-priority-features.js
```

For rollback:
```bash
node deploy-high-priority-features.js rollback
```
