const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// Refund Model
const Refund = sequelize.define('Refund', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  refund_id: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'External refund ID from payment provider'
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    },
    index: true
  },
  payment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'payments',
      key: 'id'
    },
    index: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    index: true
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0.01
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  reason: {
    type: DataTypes.ENUM(
      'requested_by_customer',
      'duplicate',
      'fraudulent',
      'subscription_canceled',
      'product_unacceptable',
      'no_longer_want',
      'damaged_product',
      'wrong_product',
      'not_received',
      'other'
    ),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM(
      'pending',
      'processing',
      'succeeded',
      'failed',
      'canceled',
      'requires_action'
    ),
    defaultValue: 'pending',
    allowNull: false
  },
  refund_type: {
    type: DataTypes.ENUM('full', 'partial'),
    allowNull: false,
    defaultValue: 'full'
  },
  payment_method: {
    type: DataTypes.ENUM('stripe', 'paypal', 'manual'),
    allowNull: false
  },
  provider_refund_id: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Refund ID from payment provider (Stripe, PayPal)'
  },
  provider_response: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Full response from payment provider'
  },
  processed_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'Admin user who processed the refund'
  },
  processed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  expected_arrival: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Expected date when refund will arrive to customer'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  },
  failure_reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  receipt_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  receipt_number: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Internal notes for admin'
  }
}, {
  tableName: 'refunds',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['order_id']
    },
    {
      fields: ['payment_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['refund_type']
    },
    {
      fields: ['payment_method']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['processed_at']
    }
  ]
});

// Refund Item Model (for partial refunds)
const RefundItem = sequelize.define('RefundItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  refund_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'refunds',
      key: 'id'
    },
    index: true
  },
  order_item_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'order_items',
      key: 'id'
    },
    index: true
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 1
    }
  },
  unit_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  condition: {
    type: DataTypes.ENUM('new', 'used', 'damaged', 'defective'),
    allowNull: true
  },
  return_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  returned_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'refund_items',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['refund_id']
    },
    {
      fields: ['order_item_id']
    },
    {
      fields: ['product_id']
    }
  ]
});

// Refund Status History Model
const RefundStatusHistory = sequelize.define('RefundStatusHistory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  refund_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'refunds',
      key: 'id'
    },
    index: true
  },
  from_status: {
    type: DataTypes.STRING,
    allowNull: true
  },
  to_status: {
    type: DataTypes.STRING,
    allowNull: false
  },
  changed_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  reason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true
  }
}, {
  tableName: 'refund_status_history',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['refund_id']
    },
    {
      fields: ['created_at']
    }
  ],
  indexes: [
    {
      unique: true,
      fields: ['refund_id']
    }
  ]
});

module.exports = {
  Refund,
  RefundItem,
  RefundStatusHistory
};
