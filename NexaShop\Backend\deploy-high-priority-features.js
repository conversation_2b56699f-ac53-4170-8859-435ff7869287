const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

class HighPriorityFeaturesDeployment {
  constructor() {
    this.projectRoot = __dirname;
    this.migrationsPath = path.join(this.projectRoot, 'src', 'migrations');
    this.seedersPath = path.join(this.projectRoot, 'src', 'seeders');
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      step: '🔄'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async executeCommand(command, description) {
    this.log(`${description}...`, 'step');
    try {
      const output = execSync(command, { 
        cwd: this.projectRoot, 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      this.log(`${description} completed successfully`, 'success');
      return output;
    } catch (error) {
      this.log(`${description} failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async checkPrerequisites() {
    this.log('Checking deployment prerequisites...', 'step');

    // Check if package.json exists
    const packageJsonPath = path.join(this.projectRoot, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('package.json not found. Please ensure you are in the correct directory.');
    }

    // Check if node_modules exists
    const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      this.log('node_modules not found. Installing dependencies...', 'warning');
      await this.executeCommand('npm install', 'Installing dependencies');
    }

    // Check if .env file exists
    const envPath = path.join(this.projectRoot, '.env');
    if (!fs.existsSync(envPath)) {
      this.log('.env file not found. Please create one with database configuration.', 'warning');
    }

    // Check migration files
    const migrationFiles = [
      '20241229000001-create-high-priority-features.js',
      '20241229000002-create-variants-logistics.js',
      '20241229000003-create-shipping-tables.js'
    ];

    for (const file of migrationFiles) {
      const filePath = path.join(this.migrationsPath, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Migration file ${file} not found in ${this.migrationsPath}`);
      }
    }

    // Check seeder files
    const seederFiles = [
      '20241229000001-init-logistics-data.js'
    ];

    for (const file of seederFiles) {
      const filePath = path.join(this.seedersPath, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Seeder file ${file} not found in ${this.seedersPath}`);
      }
    }

    this.log('All prerequisites checked successfully', 'success');
  }

  async runMigrations() {
    this.log('Running database migrations...', 'step');
    
    try {
      // Check if sequelize-cli is available
      await this.executeCommand('npx sequelize-cli --version', 'Checking Sequelize CLI');
      
      // Run migrations
      await this.executeCommand(
        'npx sequelize-cli db:migrate',
        'Running database migrations'
      );
      
      this.log('Database migrations completed successfully', 'success');
    } catch (error) {
      this.log('Migration failed. Attempting to install sequelize-cli...', 'warning');
      
      try {
        await this.executeCommand('npm install --save-dev sequelize-cli', 'Installing Sequelize CLI');
        await this.executeCommand('npx sequelize-cli db:migrate', 'Running database migrations (retry)');
        this.log('Database migrations completed successfully', 'success');
      } catch (retryError) {
        throw new Error(`Migration failed even after installing sequelize-cli: ${retryError.message}`);
      }
    }
  }

  async runSeeders() {
    this.log('Running database seeders...', 'step');
    
    try {
      await this.executeCommand(
        'npx sequelize-cli db:seed --seed 20241229000001-init-logistics-data.js',
        'Running logistics data seeder'
      );
      
      this.log('Database seeders completed successfully', 'success');
    } catch (error) {
      this.log(`Seeder warning: ${error.message}`, 'warning');
      this.log('Seeders may have already been run or there might be data conflicts', 'info');
    }
  }

  async verifyDeployment() {
    this.log('Verifying deployment...', 'step');

    // Check if test file exists
    const testFilePath = path.join(this.projectRoot, 'test-high-priority-features.js');
    if (!fs.existsSync(testFilePath)) {
      this.log('Test file not found. Skipping verification tests.', 'warning');
      return;
    }

    try {
      // Install axios if not present (needed for tests)
      await this.executeCommand('npm list axios || npm install axios', 'Ensuring axios is installed');
      
      this.log('Deployment verification completed', 'success');
      this.log('You can now run "node test-high-priority-features.js" to test the features', 'info');
    } catch (error) {
      this.log(`Verification setup failed: ${error.message}`, 'warning');
    }
  }

  async deploy() {
    console.log('🚀 Starting High Priority Features Deployment');
    console.log('=' .repeat(60));

    try {
      await this.checkPrerequisites();
      await this.runMigrations();
      await this.runSeeders();
      await this.verifyDeployment();

      console.log('\n' + '=' .repeat(60));
      this.log('🎉 High Priority Features Deployment Completed Successfully!', 'success');
      console.log('\n📋 What was deployed:');
      console.log('   • Refund System (models, services, routes)');
      console.log('   • Coupon System (models, services, routes)');
      console.log('   • Product Variants (models, services, routes)');
      console.log('   • Logistics System (models, services, routes)');
      console.log('   • Database tables and initial data');
      
      console.log('\n🔧 Next steps:');
      console.log('   1. Start your server: npm start');
      console.log('   2. Test the features: node test-high-priority-features.js');
      console.log('   3. Check API documentation for endpoint usage');
      
      console.log('\n📚 API Endpoints added:');
      console.log('   • /api/v1/refunds - Refund management');
      console.log('   • /api/v1/coupons - Coupon management');
      console.log('   • /api/v1/product-variants - Product variant management');
      console.log('   • /api/v1/logistics - Logistics and shipping management');

    } catch (error) {
      console.log('\n' + '=' .repeat(60));
      this.log(`💥 Deployment failed: ${error.message}`, 'error');
      console.log('\n🔧 Troubleshooting:');
      console.log('   1. Ensure PostgreSQL is running');
      console.log('   2. Check database connection in .env file');
      console.log('   3. Verify all dependencies are installed');
      console.log('   4. Check server logs for detailed error messages');
      
      process.exit(1);
    }
  }

  async rollback() {
    this.log('Rolling back high priority features...', 'step');
    
    try {
      // Rollback migrations in reverse order
      const migrations = [
        '20241229000003-create-shipping-tables.js',
        '20241229000002-create-variants-logistics.js', 
        '20241229000001-create-high-priority-features.js'
      ];

      for (const migration of migrations) {
        try {
          await this.executeCommand(
            `npx sequelize-cli db:migrate:undo --name ${migration}`,
            `Rolling back ${migration}`
          );
        } catch (error) {
          this.log(`Rollback of ${migration} failed: ${error.message}`, 'warning');
        }
      }

      this.log('Rollback completed', 'success');
    } catch (error) {
      this.log(`Rollback failed: ${error.message}`, 'error');
      throw error;
    }
  }
}

// Command line interface
if (require.main === module) {
  const deployment = new HighPriorityFeaturesDeployment();
  
  const command = process.argv[2];
  
  if (command === 'rollback') {
    deployment.rollback().catch(console.error);
  } else {
    deployment.deploy().catch(console.error);
  }
}

module.exports = HighPriorityFeaturesDeployment;
