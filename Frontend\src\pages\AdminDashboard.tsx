import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Space,
  Tabs,
  Progress,
  Tag,
  Avatar,
  List,
  Alert,
  Spin,
  DatePicker,
  Select
} from 'antd';
import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  TruckOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import axios from 'axios';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  totalUsers: number;
  totalProducts: number;
  ordersGrowth: number;
  revenueGrowth: number;
  usersGrowth: number;
  productsGrowth: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  amount: number;
  status: string;
  createdAt: string;
}

interface TopProduct {
  id: string;
  name: string;
  sales: number;
  revenue: number;
  image: string;
}

const AdminDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalOrders: 0,
    totalRevenue: 0,
    totalUsers: 0,
    totalProducts: 0,
    ordersGrowth: 0,
    revenueGrowth: 0,
    usersGrowth: 0,
    productsGrowth: 0
  });
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [orderStatusData, setOrderStatusData] = useState<any[]>([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch analytics dashboard data
      const analyticsResponse = await axios.get('/api/v1/analytics/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Fetch sales overview
      const salesResponse = await axios.get('/api/v1/analytics/sales/overview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      // Mock data for demonstration (replace with real API calls)
      setStats({
        totalOrders: salesResponse.data.data.overview.total_orders || 150,
        totalRevenue: salesResponse.data.data.overview.total_revenue || 25000.50,
        totalUsers: salesResponse.data.data.overview.unique_customers || 85,
        totalProducts: 45,
        ordersGrowth: 12.5,
        revenueGrowth: 8.3,
        usersGrowth: 15.2,
        productsGrowth: 5.1
      });

      // Set sales data for charts
      setSalesData(salesResponse.data.data.salesOverTime || []);
      setOrderStatusData(salesResponse.data.data.orderStatuses || []);

      // Mock recent orders
      setRecentOrders([
        {
          id: 'ORD-001',
          customerName: 'John Doe',
          amount: 299.99,
          status: 'shipped',
          createdAt: '2024-06-29T10:30:00Z'
        },
        {
          id: 'ORD-002',
          customerName: 'Jane Smith',
          amount: 159.50,
          status: 'pending',
          createdAt: '2024-06-29T09:15:00Z'
        },
        {
          id: 'ORD-003',
          customerName: 'Mike Johnson',
          amount: 89.99,
          status: 'delivered',
          createdAt: '2024-06-29T08:45:00Z'
        }
      ]);

      // Mock top products
      setTopProducts([
        {
          id: 'PROD-001',
          name: 'Wireless Headphones',
          sales: 45,
          revenue: 2250.00,
          image: '/api/placeholder/60/60'
        },
        {
          id: 'PROD-002',
          name: 'Smart Watch',
          sales: 32,
          revenue: 1920.00,
          image: '/api/placeholder/60/60'
        },
        {
          id: 'PROD-003',
          name: 'Bluetooth Speaker',
          sales: 28,
          revenue: 1400.00,
          image: '/api/placeholder/60/60'
        }
      ]);

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange';
      case 'shipped': return 'blue';
      case 'delivered': return 'green';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  const recentOrdersColumns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `$${amount.toFixed(2)}`
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: RecentOrder) => (
        <Space>
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<EditOutlined />} size="small" />
        </Space>
      )
    }
  ];

  // Chart configurations
  const salesChartConfig = {
    data: salesData,
    xField: 'date',
    yField: 'revenue',
    point: {
      size: 5,
      shape: 'diamond',
    },
    color: '#1890ff',
    smooth: true,
  };

  const orderStatusChartConfig = {
    data: orderStatusData,
    angleField: 'count',
    colorField: 'status',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={2} style={{ margin: 0 }}>
            📊 Admin Dashboard
          </Title>
          <Space>
            <RangePicker />
            <Select defaultValue="7days" style={{ width: 120 }}>
              <Option value="7days">Last 7 days</Option>
              <Option value="30days">Last 30 days</Option>
              <Option value="90days">Last 90 days</Option>
            </Select>
            <Button type="primary" icon={<PlusOutlined />}>
              Quick Actions
            </Button>
          </Space>
        </div>

        {/* Key Metrics */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={stats.totalOrders}
                prefix={<ShoppingCartOutlined style={{ color: '#1890ff' }} />}
                suffix={
                  <span style={{ fontSize: '12px', color: stats.ordersGrowth > 0 ? '#52c41a' : '#ff4d4f' }}>
                    {stats.ordersGrowth > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    {Math.abs(stats.ordersGrowth)}%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Revenue"
                value={stats.totalRevenue}
                precision={2}
                prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
                suffix={
                  <span style={{ fontSize: '12px', color: stats.revenueGrowth > 0 ? '#52c41a' : '#ff4d4f' }}>
                    {stats.revenueGrowth > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    {Math.abs(stats.revenueGrowth)}%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={stats.totalUsers}
                prefix={<UserOutlined style={{ color: '#722ed1' }} />}
                suffix={
                  <span style={{ fontSize: '12px', color: stats.usersGrowth > 0 ? '#52c41a' : '#ff4d4f' }}>
                    {stats.usersGrowth > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    {Math.abs(stats.usersGrowth)}%
                  </span>
                }
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="Total Products"
                value={stats.totalProducts}
                prefix={<TruckOutlined style={{ color: '#fa8c16' }} />}
                suffix={
                  <span style={{ fontSize: '12px', color: stats.productsGrowth > 0 ? '#52c41a' : '#ff4d4f' }}>
                    {stats.productsGrowth > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                    {Math.abs(stats.productsGrowth)}%
                  </span>
                }
              />
            </Card>
          </Col>
        </Row>

        {/* Charts Row */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} lg={16}>
            <Card title="📈 Sales Trend" extra={<Button type="text">View Details</Button>}>
              {salesData.length > 0 ? (
                <Line {...salesChartConfig} height={300} />
              ) : (
                <div style={{ textAlign: 'center', padding: '60px' }}>
                  <Text type="secondary">No sales data available</Text>
                </div>
              )}
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="📊 Order Status Distribution">
              {orderStatusData.length > 0 ? (
                <Pie {...orderStatusChartConfig} height={300} />
              ) : (
                <div style={{ textAlign: 'center', padding: '60px' }}>
                  <Text type="secondary">No order data available</Text>
                </div>
              )}
            </Card>
          </Col>
        </Row>

        {/* Content Tabs */}
        <Tabs
          defaultActiveKey="orders"
          items={[
            {
              key: 'orders',
              label: '📋 Recent Orders',
              children: (
                <Card>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Title level={4} style={{ margin: 0 }}>Recent Orders</Title>
                    <Button type="primary">View All Orders</Button>
                  </div>
                  <Table
                    columns={recentOrdersColumns}
                    dataSource={recentOrders}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                </Card>
              )
            },
            {
              key: 'products',
              label: '🏆 Top Products',
              children: (
                <Card>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Title level={4} style={{ margin: 0 }}>Top Selling Products</Title>
                    <Button type="primary">Manage Products</Button>
                  </div>
                  <List
                    itemLayout="horizontal"
                    dataSource={topProducts}
                    renderItem={(item, index) => (
                      <List.Item
                        actions={[
                          <Button type="text" icon={<EyeOutlined />} />,
                          <Button type="text" icon={<EditOutlined />} />
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            <div style={{ position: 'relative' }}>
                              <Avatar
                                src={item.image}
                                size={60}
                                shape="square"
                                style={{ backgroundColor: '#f0f0f0' }}
                              />
                              <div style={{
                                position: 'absolute',
                                top: '-8px',
                                left: '-8px',
                                background: '#1890ff',
                                color: 'white',
                                borderRadius: '50%',
                                width: '24px',
                                height: '24px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '12px',
                                fontWeight: 'bold'
                              }}>
                                #{index + 1}
                              </div>
                            </div>
                          }
                          title={<Text strong>{item.name}</Text>}
                          description={
                            <Space direction="vertical" size={4}>
                              <Text type="secondary">Sales: {item.sales} units</Text>
                              <Text type="success" strong>Revenue: ${item.revenue.toFixed(2)}</Text>
                            </Space>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              )
            },
            {
              key: 'analytics',
              label: '📊 Analytics',
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <Card title="🎯 Conversion Funnel">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div>
                          <Text>Visitors</Text>
                          <Progress percent={100} showInfo={false} strokeColor="#1890ff" />
                          <Text style={{ float: 'right' }}>1,234</Text>
                        </div>
                        <div>
                          <Text>Product Views</Text>
                          <Progress percent={75} showInfo={false} strokeColor="#52c41a" />
                          <Text style={{ float: 'right' }}>925</Text>
                        </div>
                        <div>
                          <Text>Add to Cart</Text>
                          <Progress percent={45} showInfo={false} strokeColor="#fa8c16" />
                          <Text style={{ float: 'right' }}>555</Text>
                        </div>
                        <div>
                          <Text>Checkout</Text>
                          <Progress percent={25} showInfo={false} strokeColor="#722ed1" />
                          <Text style={{ float: 'right' }}>308</Text>
                        </div>
                        <div>
                          <Text>Purchase</Text>
                          <Progress percent={15} showInfo={false} strokeColor="#eb2f96" />
                          <Text style={{ float: 'right' }}>185</Text>
                        </div>
                      </Space>
                    </Card>
                  </Col>
                  <Col xs={24} lg={12}>
                    <Card title="⚡ Quick Stats">
                      <Row gutter={[16, 16]}>
                        <Col span={12}>
                          <Statistic
                            title="Avg Order Value"
                            value={stats.totalRevenue / stats.totalOrders}
                            precision={2}
                            prefix="$"
                          />
                        </Col>
                        <Col span={12}>
                          <Statistic
                            title="Conversion Rate"
                            value={15}
                            suffix="%"
                          />
                        </Col>
                        <Col span={12}>
                          <Statistic
                            title="Return Rate"
                            value={2.3}
                            suffix="%"
                          />
                        </Col>
                        <Col span={12}>
                          <Statistic
                            title="Customer Satisfaction"
                            value={4.8}
                            suffix="/5"
                          />
                        </Col>
                      </Row>
                    </Card>
                  </Col>
                </Row>
              )
            }
          ]}
        />
      </div>
    </div>
  );
};

export default AdminDashboard;
