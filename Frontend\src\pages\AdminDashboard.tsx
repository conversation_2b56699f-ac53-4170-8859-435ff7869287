import React from 'react';
import { <PERSON>, Typo<PERSON>, Button, Space } from 'antd';

const { Title } = Typography;

const AdminDashboard: React.FC = () => {
  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2}>Admin Dashboard</Title>
        <Card>
          <div style={{ textAlign: 'center', padding: '48px' }}>
            <Title level={4}>Admin Dashboard Feature Coming Soon</Title>
            <Space>
              <Button type="primary">Manage Products</Button>
              <Button>View Analytics</Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
