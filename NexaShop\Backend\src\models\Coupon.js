const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// Coupon Model
const Coupon = sequelize.define('Coupon', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      isAlphanumeric: true
    }
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y'),
    allowNull: false
  },
  value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: true,
    defaultValue: 'USD',
    comment: 'Required for fixed_amount type'
  },
  minimum_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    comment: 'Minimum order amount to apply coupon'
  },
  maximum_discount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    comment: 'Maximum discount amount for percentage coupons'
  },
  usage_limit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    },
    comment: 'Total usage limit for this coupon'
  },
  usage_limit_per_user: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    },
    comment: 'Usage limit per user'
  },
  used_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'expired', 'used_up'),
    defaultValue: 'active',
    allowNull: false
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  applicable_to: {
    type: DataTypes.ENUM('all', 'specific_products', 'specific_categories', 'specific_users'),
    defaultValue: 'all',
    allowNull: false
  },
  first_time_only: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Only for first-time customers'
  },
  stackable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Can be combined with other coupons'
  },
  auto_apply: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Automatically apply if conditions are met'
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  },
  // For buy_x_get_y type coupons
  buy_quantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Quantity to buy for buy_x_get_y type'
  },
  get_quantity: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Quantity to get for buy_x_get_y type'
  },
  get_discount_percentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: 'Discount percentage for get items'
  }
}, {
  tableName: 'coupons',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['code'],
      unique: true
    },
    {
      fields: ['status']
    },
    {
      fields: ['type']
    },
    {
      fields: ['start_date']
    },
    {
      fields: ['end_date']
    },
    {
      fields: ['applicable_to']
    },
    {
      fields: ['created_by']
    }
  ]
});

// Coupon Product Association (for specific_products)
const CouponProduct = sequelize.define('CouponProduct', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  coupon_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'coupons',
      key: 'id'
    }
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  }
}, {
  tableName: 'coupon_products',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['coupon_id']
    },
    {
      fields: ['product_id']
    },
    {
      fields: ['coupon_id', 'product_id'],
      unique: true
    }
  ]
});

// Coupon Category Association (for specific_categories)
const CouponCategory = sequelize.define('CouponCategory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  coupon_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'coupons',
      key: 'id'
    }
  },
  category_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  }
}, {
  tableName: 'coupon_categories',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['coupon_id']
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['coupon_id', 'category_id'],
      unique: true
    }
  ]
});

// Coupon User Association (for specific_users)
const CouponUser = sequelize.define('CouponUser', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  coupon_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'coupons',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  assigned_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'coupon_users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['coupon_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['coupon_id', 'user_id'],
      unique: true
    }
  ]
});

// Coupon Usage History
const CouponUsage = sequelize.define('CouponUsage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  coupon_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'coupons',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  original_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  final_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  used_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'coupon_usage',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['coupon_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['order_id']
    },
    {
      fields: ['used_at']
    }
  ]
});

module.exports = {
  Coupon,
  CouponProduct,
  CouponCategory,
  CouponUser,
  CouponUsage
};
