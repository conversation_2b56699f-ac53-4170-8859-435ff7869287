import React, { useState, useEffect, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  FloatButton, 
  message,
  Spin
} from 'antd';
import { 
  MessageOutlined, 
  CloseOutlined,
  CustomerServiceOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import ChatWindow from './ChatWindow';
import { chatService } from '../../services/chatService';
import { socketService } from '../../services/socketService';

interface ChatWidgetProps {
  position?: 'bottom-right' | 'bottom-left';
  theme?: 'light' | 'dark';
}

const ChatWidget: React.FC<ChatWidgetProps> = ({ 
  position = 'bottom-right',
  theme = 'light'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentChat, setCurrentChat] = useState<any>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);

  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Initialize socket connection
      socketService.connect(user.token);
      
      // Listen for connection status
      socketService.on('connected', () => {
        setIsConnected(true);
      });

      socketService.on('disconnect', () => {
        setIsConnected(false);
      });

      // Listen for chat notifications
      socketService.on('chat_message_notification', (data) => {
        if (!isOpen || currentChat?.id !== data.chat_id) {
          setUnreadCount(prev => prev + 1);
          message.info(`New message from ${data.sender_name}`);
        }
      });

      socketService.on('chat_agent_assigned', (data) => {
        message.success(`${data.agent.name} has joined the chat`);
        if (currentChat?.id === data.chat.id) {
          setCurrentChat(data.chat);
        }
      });

      return () => {
        socketService.disconnect();
      };
    }
  }, [isAuthenticated, user, isOpen, currentChat]);

  const handleOpenChat = async () => {
    if (!isAuthenticated) {
      message.warning('Please login to start a chat');
      return;
    }

    setIsOpen(true);
    setUnreadCount(0);

    if (!currentChat) {
      await loadOrCreateChat();
    }
  };

  const loadOrCreateChat = async () => {
    try {
      setIsLoading(true);

      // First, try to get existing active chat
      const response = await chatService.getMyChats({ 
        status: 'active',
        limit: 1 
      });

      if (response.data.chats.length > 0) {
        setCurrentChat(response.data.chats[0]);
      } else {
        // Check for pending chats
        const pendingResponse = await chatService.getMyChats({ 
          status: 'pending',
          limit: 1 
        });

        if (pendingResponse.data.chats.length > 0) {
          setCurrentChat(pendingResponse.data.chats[0]);
        } else {
          // Create new chat
          const createResponse = await chatService.createChat({
            category: 'general_inquiry',
            title: 'Customer Support Chat',
            description: 'General customer support inquiry'
          });
          setCurrentChat(createResponse.data.chat);
        }
      }
    } catch (error) {
      console.error('Load or create chat error:', error);
      message.error('Failed to start chat. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseChat = () => {
    setIsOpen(false);
  };

  const getStatusColor = () => {
    if (!isAuthenticated) return '#d9d9d9';
    if (!isConnected) return '#ff4d4f';
    return '#52c41a';
  };

  const getStatusText = () => {
    if (!isAuthenticated) return 'Login required';
    if (!isConnected) return 'Connecting...';
    return 'Online';
  };

  if (!isAuthenticated) {
    return null; // Don't show chat widget for unauthenticated users
  }

  return (
    <>
      {/* Chat Float Button */}
      <FloatButton
        icon={
          <Badge count={unreadCount} size="small">
            <CustomerServiceOutlined style={{ fontSize: '20px' }} />
          </Badge>
        }
        type="primary"
        style={{
          right: position === 'bottom-right' ? 24 : undefined,
          left: position === 'bottom-left' ? 24 : undefined,
          bottom: 24,
          width: 60,
          height: 60,
          backgroundColor: theme === 'dark' ? '#1f1f1f' : '#1890ff'
        }}
        onClick={handleOpenChat}
        tooltip="Customer Support"
      />

      {/* Chat Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <CustomerServiceOutlined />
              <span>Customer Support</span>
              <div 
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: getStatusColor(),
                  marginLeft: 4
                }}
                title={getStatusText()}
              />
            </div>
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={handleCloseChat}
              size="small"
            />
          </div>
        }
        placement={position === 'bottom-right' ? 'right' : 'left'}
        width={400}
        height="70vh"
        open={isOpen}
        onClose={handleCloseChat}
        closable={false}
        styles={{
          body: { padding: 0, height: '100%' }
        }}
      >
        {isLoading ? (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%' 
          }}>
            <Spin size="large" />
          </div>
        ) : currentChat ? (
          <ChatWindow 
            chat={currentChat}
            onChatUpdate={setCurrentChat}
            height="100%"
          />
        ) : (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <CustomerServiceOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <p style={{ color: '#999', textAlign: 'center' }}>
              Welcome to customer support!<br />
              Click the button below to start a chat.
            </p>
            <Button 
              type="primary" 
              icon={<MessageOutlined />}
              onClick={loadOrCreateChat}
              loading={isLoading}
            >
              Start Chat
            </Button>
          </div>
        )}
      </Drawer>
    </>
  );
};

export default ChatWidget;
