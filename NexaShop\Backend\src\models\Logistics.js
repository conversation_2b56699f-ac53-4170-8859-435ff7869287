const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// Shipping Carriers
const ShippingCarrier = sequelize.define('ShippingCarrier', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  code: {
    type: DataTypes.STRING(20),
    allowNull: false,
    unique: true
  },
  display_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  logo_url: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  website_url: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  tracking_url_template: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL template with {tracking_number} placeholder'
  },
  api_endpoint: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  api_key: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  api_secret: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  supported_services: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of supported shipping services'
  },
  supported_countries: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of supported country codes'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  configuration: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {},
    comment: 'Carrier-specific configuration'
  }
}, {
  tableName: 'shipping_carriers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['code'],
      unique: true
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    }
  ]
});

// Shipping Services
const ShippingService = sequelize.define('ShippingService', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  carrier_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'shipping_carriers',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  display_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  service_type: {
    type: DataTypes.ENUM('standard', 'express', 'overnight', 'economy', 'priority'),
    allowNull: false
  },
  delivery_time_min: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Minimum delivery time in days'
  },
  delivery_time_max: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Maximum delivery time in days'
  },
  is_tracked: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  is_insured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  max_weight: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true,
    comment: 'Maximum weight in kg'
  },
  max_dimensions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Maximum dimensions {length, width, height, unit}'
  },
  base_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Base shipping rate'
  },
  rate_calculation: {
    type: DataTypes.ENUM('flat', 'weight_based', 'dimension_based', 'api_calculated'),
    defaultValue: 'api_calculated'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
}, {
  tableName: 'shipping_services',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['carrier_id']
    },
    {
      fields: ['carrier_id', 'code'],
      unique: true
    },
    {
      fields: ['service_type']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    }
  ]
});

// Shipping Rates
const ShippingRate = sequelize.define('ShippingRate', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  service_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'shipping_services',
      key: 'id'
    }
  },
  origin_country: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'ISO 2-letter country code'
  },
  origin_state: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  origin_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  origin_postal_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  destination_country: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'ISO 2-letter country code'
  },
  destination_state: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  destination_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  destination_postal_code: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  weight_min: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true,
    comment: 'Minimum weight in kg'
  },
  weight_max: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true,
    comment: 'Maximum weight in kg'
  },
  rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  effective_date: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  expiry_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'shipping_rates',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['service_id']
    },
    {
      fields: ['origin_country']
    },
    {
      fields: ['destination_country']
    },
    {
      fields: ['effective_date']
    },
    {
      fields: ['expiry_date']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Shipments
const Shipment = sequelize.define('Shipment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  shipment_number: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  carrier_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'shipping_carriers',
      key: 'id'
    }
  },
  service_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'shipping_services',
      key: 'id'
    }
  },
  tracking_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    index: true
  },
  label_url: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM(
      'created',
      'label_generated',
      'picked_up',
      'in_transit',
      'out_for_delivery',
      'delivered',
      'failed_delivery',
      'returned',
      'cancelled',
      'exception'
    ),
    defaultValue: 'created',
    allowNull: false
  },
  origin_address: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Complete origin address object'
  },
  destination_address: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Complete destination address object'
  },
  package_details: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Package weight, dimensions, contents'
  },
  shipping_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  insurance_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    defaultValue: 0
  },
  total_cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD'
  },
  estimated_delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actual_delivery_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  shipped_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivered_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  carrier_response: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Raw response from carrier API'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'shipments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['shipment_number'],
      unique: true
    },
    {
      fields: ['order_id']
    },
    {
      fields: ['carrier_id']
    },
    {
      fields: ['service_id']
    },
    {
      fields: ['tracking_number']
    },
    {
      fields: ['status']
    },
    {
      fields: ['shipped_at']
    },
    {
      fields: ['delivered_at']
    }
  ]
});

// Tracking Events
const TrackingEvent = sequelize.define('TrackingEvent', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  shipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'shipments',
      key: 'id'
    }
  },
  event_type: {
    type: DataTypes.ENUM(
      'label_created',
      'picked_up',
      'in_transit',
      'arrived_at_facility',
      'departed_facility',
      'out_for_delivery',
      'delivered',
      'delivery_attempted',
      'exception',
      'returned_to_sender',
      'customs_cleared',
      'customs_held'
    ),
    allowNull: false
  },
  status: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  location: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Location details {city, state, country, postal_code}'
  },
  event_time: {
    type: DataTypes.DATE,
    allowNull: false
  },
  carrier_event_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  carrier_raw_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Raw tracking data from carrier'
  },
  is_delivered: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_exception: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  }
}, {
  tableName: 'tracking_events',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['shipment_id']
    },
    {
      fields: ['event_type']
    },
    {
      fields: ['event_time']
    },
    {
      fields: ['is_delivered']
    },
    {
      fields: ['is_exception']
    }
  ]
});

module.exports = {
  ShippingCarrier,
  ShippingService,
  ShippingRate,
  Shipment,
  TrackingEvent
};
