import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  method: 'paypal' | 'stripe' | 'apple-pay';
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'partially_refunded';
  transactionId?: string;
  paypalOrderId?: string;
  stripePaymentIntentId?: string;
  createdAt: string;
  completedAt?: string;
}

export interface PayPalOrderResponse {
  paypalOrderId: string;
  approvalUrl: string;
  payment: Payment;
}

export interface StripePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  payment: Payment;
}

interface PaymentState {
  payments: Payment[];
  currentPayment: Payment | null;
  loading: boolean;
  error: string | null;
  paypalOrderId: string | null;
  stripeClientSecret: string | null;
  processingPayment: boolean;
}

const initialState: PaymentState = {
  payments: [],
  currentPayment: null,
  loading: false,
  error: null,
  paypalOrderId: null,
  stripeClientSecret: null,
  processingPayment: false,
};

// Async thunks for PayPal
export const createPayPalOrder = createAsyncThunk(
  'payments/createPayPalOrder',
  async ({ orderId, amount, currency = 'USD' }: { 
    orderId: string; 
    amount: number; 
    currency?: string; 
  }) => {
    const response = await fetch('/api/v1/payments/paypal/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ orderId, amount, currency }),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data as PayPalOrderResponse;
  }
);

export const capturePayPalOrder = createAsyncThunk(
  'payments/capturePayPalOrder',
  async (paypalOrderId: string) => {
    const response = await fetch('/api/v1/payments/paypal/capture-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ paypalOrderId }),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.payment as Payment;
  }
);

// Async thunks for Stripe
export const createStripePaymentIntent = createAsyncThunk(
  'payments/createStripePaymentIntent',
  async ({ orderId, amount, currency = 'USD' }: { 
    orderId: string; 
    amount: number; 
    currency?: string; 
  }) => {
    const response = await fetch('/api/v1/payments/stripe/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ orderId, amount, currency }),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data as StripePaymentIntentResponse;
  }
);

export const confirmStripePayment = createAsyncThunk(
  'payments/confirmStripePayment',
  async (paymentIntentId: string) => {
    const response = await fetch('/api/v1/payments/stripe/confirm-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ paymentIntentId }),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.payment as Payment;
  }
);

// General payment thunks
export const fetchPaymentById = createAsyncThunk(
  'payments/fetchPaymentById',
  async (paymentId: string) => {
    const response = await fetch(`/api/v1/payments/${paymentId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.payment as Payment;
  }
);

export const fetchPaymentsByOrderId = createAsyncThunk(
  'payments/fetchPaymentsByOrderId',
  async (orderId: string) => {
    const response = await fetch(`/api/v1/payments/order/${orderId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.payments as Payment[];
  }
);

export const refundPayment = createAsyncThunk(
  'payments/refundPayment',
  async ({ paymentId, amount, reason }: { 
    paymentId: string; 
    amount?: number; 
    reason?: string; 
  }) => {
    const response = await fetch(`/api/v1/payments/${paymentId}/refund`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ amount, reason }),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.payment as Payment;
  }
);

const paymentSlice = createSlice({
  name: 'payments',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentPayment: (state) => {
      state.currentPayment = null;
    },
    clearPaymentData: (state) => {
      state.paypalOrderId = null;
      state.stripeClientSecret = null;
      state.currentPayment = null;
    },
    setProcessingPayment: (state, action: PayloadAction<boolean>) => {
      state.processingPayment = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create PayPal Order
      .addCase(createPayPalOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPayPalOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.paypalOrderId = action.payload.paypalOrderId;
        state.currentPayment = action.payload.payment;
        state.payments.push(action.payload.payment);
      })
      .addCase(createPayPalOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create PayPal order';
      })
      // Capture PayPal Order
      .addCase(capturePayPalOrder.pending, (state) => {
        state.processingPayment = true;
        state.error = null;
      })
      .addCase(capturePayPalOrder.fulfilled, (state, action) => {
        state.processingPayment = false;
        state.currentPayment = action.payload;
        const index = state.payments.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.payments[index] = action.payload;
        }
      })
      .addCase(capturePayPalOrder.rejected, (state, action) => {
        state.processingPayment = false;
        state.error = action.error.message || 'Failed to capture PayPal payment';
      })
      // Create Stripe Payment Intent
      .addCase(createStripePaymentIntent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createStripePaymentIntent.fulfilled, (state, action) => {
        state.loading = false;
        state.stripeClientSecret = action.payload.clientSecret;
        state.currentPayment = action.payload.payment;
        state.payments.push(action.payload.payment);
      })
      .addCase(createStripePaymentIntent.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create Stripe payment intent';
      })
      // Confirm Stripe Payment
      .addCase(confirmStripePayment.pending, (state) => {
        state.processingPayment = true;
        state.error = null;
      })
      .addCase(confirmStripePayment.fulfilled, (state, action) => {
        state.processingPayment = false;
        state.currentPayment = action.payload;
        const index = state.payments.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.payments[index] = action.payload;
        }
      })
      .addCase(confirmStripePayment.rejected, (state, action) => {
        state.processingPayment = false;
        state.error = action.error.message || 'Failed to confirm Stripe payment';
      })
      // Fetch Payment by ID
      .addCase(fetchPaymentById.fulfilled, (state, action) => {
        state.currentPayment = action.payload;
        const index = state.payments.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.payments[index] = action.payload;
        } else {
          state.payments.push(action.payload);
        }
      })
      // Fetch Payments by Order ID
      .addCase(fetchPaymentsByOrderId.fulfilled, (state, action) => {
        // Update payments array with fetched payments
        action.payload.forEach(payment => {
          const index = state.payments.findIndex(p => p.id === payment.id);
          if (index !== -1) {
            state.payments[index] = payment;
          } else {
            state.payments.push(payment);
          }
        });
      })
      // Refund Payment
      .addCase(refundPayment.fulfilled, (state, action) => {
        const index = state.payments.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.payments[index] = action.payload;
        }
        if (state.currentPayment?.id === action.payload.id) {
          state.currentPayment = action.payload;
        }
      });
  },
});

export const {
  clearError,
  clearCurrentPayment,
  clearPaymentData,
  setProcessingPayment,
} = paymentSlice.actions;

export default paymentSlice.reducer;
