module.exports = (sequelize, DataTypes) => {
  const AgentStatus = sequelize.define('AgentStatus', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    agent_id: {
      type: DataTypes.UUID,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'offline',
      allowNull: false,
      validate: {
        isIn: [['online', 'offline', 'busy', 'away', 'break']]
      }
    },
    availability: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
      comment: 'Whether agent is available to receive new chats'
    },
    max_concurrent_chats: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      validate: {
        min: 1,
        max: 20
      },
      comment: 'Maximum number of concurrent chats this agent can handle'
    },
    current_chat_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Current number of active chats'
    },
    specialties: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: [],
      comment: 'Agent specialties for smart routing'
    },
    languages: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: ['en'],
      comment: 'Languages the agent can support'
    },
    priority_score: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
      validate: {
        min: 0,
        max: 1000
      },
      comment: 'Priority score for chat assignment (higher = higher priority)'
    },
    last_activity_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    status_message: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 255]
      },
      comment: 'Custom status message'
    },
    auto_away_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 15,
      validate: {
        min: 5,
        max: 120
      },
      comment: 'Minutes of inactivity before auto-away'
    },
    working_hours: {
      type: DataTypes.JSONB,
      defaultValue: {
        monday: { start: '09:00', end: '17:00', enabled: true },
        tuesday: { start: '09:00', end: '17:00', enabled: true },
        wednesday: { start: '09:00', end: '17:00', enabled: true },
        thursday: { start: '09:00', end: '17:00', enabled: true },
        friday: { start: '09:00', end: '17:00', enabled: true },
        saturday: { start: '09:00', end: '17:00', enabled: false },
        sunday: { start: '09:00', end: '17:00', enabled: false }
      },
      comment: 'Agent working hours schedule'
    },
    timezone: {
      type: DataTypes.STRING,
      defaultValue: 'UTC',
      allowNull: false
    },
    performance_metrics: {
      type: DataTypes.JSONB,
      defaultValue: {
        total_chats: 0,
        average_response_time: 0,
        customer_satisfaction: 0,
        resolution_rate: 0
      },
      comment: 'Performance metrics for the agent'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'agent_status',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['agent_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['availability']
      },
      {
        fields: ['priority_score']
      },
      {
        fields: ['last_activity_at']
      }
    ]
  });

  return AgentStatus;
};
