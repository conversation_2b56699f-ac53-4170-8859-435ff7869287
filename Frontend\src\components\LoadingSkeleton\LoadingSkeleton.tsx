import React from 'react';
import { Card, Skeleton, Row, Col } from 'antd';

interface LoadingSkeletonProps {
  count?: number;
  type?: 'product' | 'category' | 'hero';
}

const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ 
  count = 8, 
  type = 'product' 
}) => {
  const renderProductSkeleton = () => (
    <Card
      style={{
        borderRadius: '16px',
        border: 'none',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        overflow: 'hidden',
      }}
      cover={
        <Skeleton.Image 
          style={{ 
            width: '100%', 
            height: '240px',
            borderRadius: '16px 16px 0 0',
          }} 
        />
      }
      actions={[
        <Skeleton.Button 
          active 
          size="large" 
          style={{ 
            width: '100%', 
            height: '48px',
            borderRadius: '12px',
          }} 
        />
      ]}
    >
      <Skeleton
        active
        title={{ width: '80%' }}
        paragraph={{ 
          rows: 3, 
          width: ['60%', '40%', '90%'] 
        }}
      />
    </Card>
  );

  const renderCategorySkeleton = () => (
    <Card
      style={{
        textAlign: 'center',
        height: '160px',
        borderRadius: '16px',
        border: 'none',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
      }}
    >
      <Skeleton
        active
        avatar={{ 
          size: 'large', 
          shape: 'circle',
          style: { marginBottom: '16px' }
        }}
        title={{ width: '60%' }}
        paragraph={{ rows: 1, width: '40%' }}
      />
    </Card>
  );

  const renderHeroSkeleton = () => (
    <div
      style={{
        height: '600px',
        borderRadius: '16px',
        overflow: 'hidden',
        marginBottom: '48px',
      }}
    >
      <Skeleton.Image 
        style={{ 
          width: '100%', 
          height: '100%',
          borderRadius: '16px',
        }} 
      />
    </div>
  );

  const renderSkeleton = () => {
    switch (type) {
      case 'category':
        return renderCategorySkeleton();
      case 'hero':
        return renderHeroSkeleton();
      default:
        return renderProductSkeleton();
    }
  };

  if (type === 'hero') {
    return renderSkeleton();
  }

  return (
    <Row gutter={[24, 24]}>
      {Array.from({ length: count }, (_, index) => (
        <Col 
          xs={type === 'category' ? 12 : 12} 
          sm={type === 'category' ? 8 : 8} 
          md={type === 'category' ? 4 : 6} 
          key={index}
        >
          {renderSkeleton()}
        </Col>
      ))}
    </Row>
  );
};

export default LoadingSkeleton;
