import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Avatar,
  Modal,
  Form,
  Switch,
  Row,
  Col,
  Typography,
  Drawer,
  Descriptions,
  Divider,
  message,
  Popconfirm,
  DatePicker,
  Statistic
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  PlusOutlined,
  ExportOutlined,
  LockOutlined,
  UnlockOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: 'admin' | 'customer' | 'vendor';
  status: 'active' | 'inactive' | 'suspended';
  emailVerified: boolean;
  phoneVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  totalOrders?: number;
  totalSpent?: number;
  addresses?: Address[];
}

interface Address {
  id: string;
  type: 'shipping' | 'billing';
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  isDefault: boolean;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [form] = Form.useForm();

  // Statistics
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    newUsersThisMonth: 0,
    verifiedUsers: 0
  });

  useEffect(() => {
    fetchUsers();
    fetchUserStats();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params: any = {
        limit: 100
      };

      if (searchText) params.search = searchText;
      if (selectedRole) params.role = selectedRole;
      if (selectedStatus) params.status = selectedStatus;
      if (dateRange) {
        params.startDate = dateRange[0].toISOString();
        params.endDate = dateRange[1].toISOString();
      }

      const response = await axios.get('/api/v1/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        params
      });
      
      if (response.data.success) {
        setUsers(response.data.data.users || []);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      message.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const fetchUserStats = async () => {
    try {
      // Mock stats for now - replace with real API call
      setStats({
        totalUsers: 1250,
        activeUsers: 1180,
        newUsersThisMonth: 85,
        verifiedUsers: 1050
      });
    } catch (error) {
      console.error('Failed to fetch user stats:', error);
    }
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setIsDetailDrawerVisible(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    form.setFieldsValue({
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      emailVerified: user.emailVerified,
      phoneVerified: user.phoneVerified
    });
    setIsEditModalVisible(true);
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await axios.delete(`/api/v1/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      message.success('User deleted successfully');
      fetchUsers();
    } catch (error) {
      console.error('Failed to delete user:', error);
      message.error('Failed to delete user');
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      
      await axios.put(`/api/v1/users/${userId}/status`, 
        { status: newStatus },
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      
      message.success(`User ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
      fetchUsers();
    } catch (error) {
      console.error('Failed to update user status:', error);
      message.error('Failed to update user status');
    }
  };

  const handleUpdateUser = async () => {
    try {
      const values = await form.validateFields();
      
      await axios.put(`/api/v1/users/${selectedUser?.id}`, values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      message.success('User updated successfully');
      setIsEditModalVisible(false);
      fetchUsers();
    } catch (error) {
      console.error('Failed to update user:', error);
      message.error('Failed to update user');
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'red';
      case 'vendor': return 'blue';
      case 'customer': return 'green';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'suspended': return 'red';
      default: return 'default';
    }
  };

  const columns: ColumnsType<User> = [
    {
      title: 'User',
      key: 'user',
      width: 250,
      render: (_, record: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            src={record.avatar} 
            icon={<UserOutlined />} 
            size="large"
            style={{ marginRight: '12px' }}
          />
          <div>
            <Text strong>{record.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.email}
            </Text>
            {record.phone && (
              <>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {record.phone}
                </Text>
              </>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: string) => (
        <Tag color={getRoleColor(role)}>
          {role.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Verification',
      key: 'verification',
      width: 120,
      render: (_, record: User) => (
        <Space direction="vertical" size={2}>
          <Tag color={record.emailVerified ? 'green' : 'orange'}>
            Email {record.emailVerified ? '✓' : '✗'}
          </Tag>
          <Tag color={record.phoneVerified ? 'green' : 'orange'}>
            Phone {record.phoneVerified ? '✓' : '✗'}
          </Tag>
        </Space>
      )
    },
    {
      title: 'Orders/Spent',
      key: 'orders',
      width: 120,
      render: (_, record: User) => (
        <div>
          <Text strong>{record.totalOrders || 0}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ${(record.totalSpent || 0).toFixed(2)}
          </Text>
        </div>
      )
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('MMM DD, YYYY') : 'Never'
    },
    {
      title: 'Joined',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: User) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewUser(record)}
            title="View Details"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
            title="Edit User"
          />
          <Button
            type="text"
            icon={record.status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
            onClick={() => handleToggleUserStatus(record.id, record.status)}
            title={record.status === 'active' ? 'Deactivate' : 'Activate'}
          />
          <Popconfirm
            title="Are you sure you want to delete this user?"
            onConfirm={() => handleDeleteUser(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
              title="Delete User"
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={stats.totalUsers}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Users"
              value={stats.activeUsers}
              prefix={<UserOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="New This Month"
              value={stats.newUsersThisMonth}
              prefix={<CalendarOutlined style={{ color: '#fa8c16' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Verified Users"
              value={stats.verifiedUsers}
              prefix={<MailOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                👥 User Management
              </Title>
            </Col>
            <Col>
              <Space>
                <Button icon={<ExportOutlined />}>Export Users</Button>
                <Button type="primary" icon={<PlusOutlined />}>
                  Add User
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Filters */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Search
              placeholder="Search users..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={fetchUsers}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Role"
              value={selectedRole}
              onChange={setSelectedRole}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="admin">Admin</Option>
              <Option value="vendor">Vendor</Option>
              <Option value="customer">Customer</Option>
            </Select>
          </Col>
          <Col xs={24} sm={4}>
            <Select
              placeholder="Status"
              value={selectedStatus}
              onChange={setSelectedStatus}
              style={{ width: '100%' }}
              allowClear
            >
              <Option value="active">Active</Option>
              <Option value="inactive">Inactive</Option>
              <Option value="suspended">Suspended</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
            />
          </Col>
          <Col xs={24} sm={4}>
            <Button type="primary" onClick={fetchUsers} block>
              Filter
            </Button>
          </Col>
        </Row>

        {/* Users Table */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            total: users.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} users`
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* User Detail Drawer */}
      <Drawer
        title="User Details"
        placement="right"
        onClose={() => setIsDetailDrawerVisible(false)}
        open={isDetailDrawerVisible}
        width={600}
      >
        {selectedUser && (
          <div>
            {/* User Header */}
            <div style={{ textAlign: 'center', marginBottom: '24px' }}>
              <Avatar
                src={selectedUser.avatar}
                icon={<UserOutlined />}
                size={80}
                style={{ marginBottom: '12px' }}
              />
              <Title level={4} style={{ margin: 0 }}>
                {selectedUser.name}
              </Title>
              <Text type="secondary">{selectedUser.email}</Text>
              <div style={{ marginTop: '8px' }}>
                <Tag color={getRoleColor(selectedUser.role)}>
                  {selectedUser.role.toUpperCase()}
                </Tag>
                <Tag color={getStatusColor(selectedUser.status)}>
                  {selectedUser.status.toUpperCase()}
                </Tag>
              </div>
            </div>

            <Divider />

            {/* Basic Information */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Basic Information</Title>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Full Name">
                  {selectedUser.name}
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  <Space>
                    <MailOutlined />
                    {selectedUser.email}
                    {selectedUser.emailVerified && <Tag color="green">Verified</Tag>}
                  </Space>
                </Descriptions.Item>
                {selectedUser.phone && (
                  <Descriptions.Item label="Phone">
                    <Space>
                      <PhoneOutlined />
                      {selectedUser.phone}
                      {selectedUser.phoneVerified && <Tag color="green">Verified</Tag>}
                    </Space>
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="Role">
                  <Tag color={getRoleColor(selectedUser.role)}>
                    {selectedUser.role.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag color={getStatusColor(selectedUser.status)}>
                    {selectedUser.status.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Member Since">
                  {dayjs(selectedUser.createdAt).format('MMMM DD, YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Last Login">
                  {selectedUser.lastLoginAt
                    ? dayjs(selectedUser.lastLoginAt).format('MMMM DD, YYYY at HH:mm')
                    : 'Never'
                  }
                </Descriptions.Item>
              </Descriptions>
            </div>

            {/* Order Statistics */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={5}>Order Statistics</Title>
              <Row gutter={16}>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Total Orders"
                      value={selectedUser.totalOrders || 0}
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Total Spent"
                      value={selectedUser.totalSpent || 0}
                      precision={2}
                      prefix="$"
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
              </Row>
            </div>

            {/* Addresses */}
            {selectedUser.addresses && selectedUser.addresses.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Title level={5}>Addresses</Title>
                {selectedUser.addresses.map((address, index) => (
                  <Card key={address.id} size="small" style={{ marginBottom: '8px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div>
                        <Text strong>{address.type.toUpperCase()}</Text>
                        {address.isDefault && <Tag color="blue" style={{ marginLeft: '8px' }}>Default</Tag>}
                        <br />
                        <Text>
                          {address.street}<br />
                          {address.city}, {address.state} {address.zipCode}<br />
                          {address.country}
                        </Text>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Action Buttons */}
            <div style={{ textAlign: 'center' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => {
                    setIsDetailDrawerVisible(false);
                    handleEditUser(selectedUser);
                  }}
                >
                  Edit User
                </Button>
                <Button
                  icon={selectedUser.status === 'active' ? <LockOutlined /> : <UnlockOutlined />}
                  onClick={() => {
                    setIsDetailDrawerVisible(false);
                    handleToggleUserStatus(selectedUser.id, selectedUser.status);
                  }}
                >
                  {selectedUser.status === 'active' ? 'Deactivate' : 'Activate'}
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>

      {/* Edit User Modal */}
      <Modal
        title="Edit User"
        open={isEditModalVisible}
        onOk={handleUpdateUser}
        onCancel={() => setIsEditModalVisible(false)}
        width={600}
        okText="Update"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Full Name"
                rules={[{ required: true, message: 'Please enter full name' }]}
              >
                <Input placeholder="Enter full name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please enter email' },
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input placeholder="Enter email" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Phone"
              >
                <Input placeholder="Enter phone number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="role"
                label="Role"
                rules={[{ required: true, message: 'Please select role' }]}
              >
                <Select>
                  <Option value="admin">Admin</Option>
                  <Option value="vendor">Vendor</Option>
                  <Option value="customer">Customer</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                  <Option value="suspended">Suspended</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="emailVerified"
                label="Email Verified"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phoneVerified"
                label="Phone Verified"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
