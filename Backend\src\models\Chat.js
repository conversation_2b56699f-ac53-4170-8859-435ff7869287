module.exports = (sequelize, DataTypes) => {
  const Chat = sequelize.define('Chat', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    type: {
      type: DataTypes.STRING(20),
      defaultValue: 'customer_support',
      allowNull: false,
      validate: {
        isIn: [['customer_support', 'group', 'direct']]
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [1, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'pending',
      allowNull: false,
      validate: {
        isIn: [['active', 'closed', 'pending', 'transferred']]
      }
    },
    priority: {
      type: DataTypes.STRING(20),
      defaultValue: 'normal',
      allowNull: false,
      validate: {
        isIn: [['low', 'normal', 'high', 'urgent']]
      }
    },
    category: {
      type: DataTypes.STRING(30),
      defaultValue: 'general_inquiry',
      allowNull: false,
      validate: {
        isIn: [[
          'general_inquiry',
          'order_support',
          'payment_issue',
          'shipping_inquiry',
          'product_question',
          'technical_support',
          'complaint',
          'refund_request'
        ]]
      }
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'Customer who initiated the chat'
    },
    assigned_agent_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      comment: 'Customer service agent assigned to this chat'
    },
    last_message_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_message_preview: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: 'Preview of the last message for quick display'
    },
    unread_count_customer: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Unread messages count for customer'
    },
    unread_count_agent: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0
      },
      comment: 'Unread messages count for agent'
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      },
      comment: 'Customer satisfaction rating (1-5)'
    },
    feedback: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Customer feedback about the support experience'
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: [],
      comment: 'Tags for categorizing and searching chats'
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Additional metadata like browser info, order references, etc.'
    },
    closed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    closed_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chats',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['customer_id']
      },
      {
        fields: ['assigned_agent_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['priority']
      },
      {
        fields: ['category']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['last_message_at']
      }
    ]
  });

  return Chat;
};
