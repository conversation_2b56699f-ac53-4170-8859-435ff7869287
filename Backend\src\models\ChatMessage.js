module.exports = (sequelize, DataTypes) => {
  const ChatMessage = sequelize.define('ChatMessage', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    chat_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'chats',
        key: 'id'
      }
    },
    sender_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    sender_type: {
      type: DataTypes.STRING(20),
      allowNull: false,
      validate: {
        isIn: [['customer', 'agent', 'system']]
      },
      comment: 'Type of sender: customer, agent, or system'
    },
    message_type: {
      type: DataTypes.STRING(20),
      defaultValue: 'text',
      allowNull: false,
      validate: {
        isIn: [['text', 'image', 'file', 'system', 'quick_reply']]
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Message content or file description'
    },
    file_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL for uploaded files or images'
    },
    file_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Original file name'
    },
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'File size in bytes'
    },
    file_type: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'MIME type of the file'
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    read_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    is_edited: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    edited_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    reply_to_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'chat_messages',
        key: 'id'
      },
      comment: 'Reference to the message being replied to'
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Additional metadata like delivery status, reactions, etc.'
    },
    delivery_status: {
      type: DataTypes.STRING(20),
      defaultValue: 'sent',
      allowNull: false,
      validate: {
        isIn: [['sent', 'delivered', 'read', 'failed']]
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chat_messages',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['chat_id']
      },
      {
        fields: ['sender_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['is_read']
      },
      {
        fields: ['message_type']
      },
      {
        fields: ['reply_to_id']
      }
    ]
  });

  return ChatMessage;
};
