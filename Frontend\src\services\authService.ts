import { api, ApiResponse } from './api';
import { User, LoginFormData, RegisterFormData } from '../types';

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
}

export interface PasswordResetData {
  email: string;
}

export interface PasswordResetConfirmData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const authService = {
  // User registration
  register: async (userData: RegisterFormData): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>('/auth/register', userData);
  },

  // User login
  login: async (credentials: LoginFormData): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>('/auth/login', credentials);
  },

  // User logout
  logout: async (): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/logout');
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>('/auth/refresh', { refreshToken });
  },

  // Get current user profile
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    return api.get<User>('/auth/me');
  },

  // Update user profile
  updateProfile: async (userData: Partial<User>): Promise<ApiResponse<User>> => {
    return api.put<User>('/auth/profile', userData);
  },

  // Change password
  changePassword: async (passwordData: ChangePasswordData): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/change-password', passwordData);
  },

  // Request password reset
  requestPasswordReset: async (data: PasswordResetData): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/forgot-password', data);
  },

  // Confirm password reset
  confirmPasswordReset: async (data: PasswordResetConfirmData): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/reset-password', data);
  },

  // Verify email
  verifyEmail: async (token: string): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/verify-email', { token });
  },

  // Resend verification email
  resendVerificationEmail: async (): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/resend-verification');
  },

  // Social login (Google, Facebook, etc.)
  socialLogin: async (provider: string, token: string): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>(`/auth/social/${provider}`, { token });
  },

  // Two-factor authentication
  twoFactor: {
    // Enable 2FA
    enable: async (): Promise<ApiResponse<{ qrCode: string; secret: string }>> => {
      return api.post<{ qrCode: string; secret: string }>('/auth/2fa/enable');
    },

    // Verify 2FA setup
    verify: async (token: string): Promise<ApiResponse<{ backupCodes: string[] }>> => {
      return api.post<{ backupCodes: string[] }>('/auth/2fa/verify', { token });
    },

    // Disable 2FA
    disable: async (token: string): Promise<ApiResponse<void>> => {
      return api.post<void>('/auth/2fa/disable', { token });
    },

    // Verify 2FA token during login
    verifyLogin: async (token: string): Promise<ApiResponse<AuthResponse>> => {
      return api.post<AuthResponse>('/auth/2fa/verify-login', { token });
    },
  },

  // Account management
  account: {
    // Deactivate account
    deactivate: async (password: string): Promise<ApiResponse<void>> => {
      return api.post<void>('/auth/account/deactivate', { password });
    },

    // Delete account
    delete: async (password: string): Promise<ApiResponse<void>> => {
      return api.post<void>('/auth/account/delete', { password });
    },

    // Export user data
    exportData: async (): Promise<ApiResponse<any>> => {
      return api.get<any>('/auth/account/export');
    },
  },

  // Session management
  session: {
    // Get active sessions
    getSessions: async (): Promise<ApiResponse<any[]>> => {
      return api.get<any[]>('/auth/sessions');
    },

    // Revoke session
    revokeSession: async (sessionId: string): Promise<ApiResponse<void>> => {
      return api.delete<void>(`/auth/sessions/${sessionId}`);
    },

    // Revoke all sessions
    revokeAllSessions: async (): Promise<ApiResponse<void>> => {
      return api.delete<void>('/auth/sessions');
    },
  },
};

export default authService;
