import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Form,
  Rate,
  Input,
  Button,
  Upload,
  Space,
  message,
  Typography,
  Card,
} from 'antd';
import {
  StarOutlined,
  UploadOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { createReview, updateReview } from '../../store/slices/reviewSlice';
import { Review } from '../../store/slices/reviewSlice';

const { TextArea } = Input;
const { Text } = Typography;

interface ReviewFormProps {
  productId: string;
  review?: Review | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const ReviewForm: React.FC<ReviewFormProps> = ({
  productId,
  review,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.reviews);

  const [rating, setRating] = useState(0);
  const [fileList, setFileList] = useState<any[]>([]);

  useEffect(() => {
    if (review) {
      form.setFieldsValue({
        rating: review.rating,
        title: review.title,
        comment: review.comment,
      });
      setRating(review.rating);
      // Convert image URLs to file list format
      const imageFiles = review.images.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}.jpg`,
        status: 'done',
        url: url,
      }));
      setFileList(imageFiles);
    } else {
      form.resetFields();
      setRating(0);
      setFileList([]);
    }
  }, [review, form]);

  const handleSubmit = async (values: any) => {
    try {
      const reviewData = {
        productId,
        rating: values.rating,
        title: values.title,
        comment: values.comment,
        images: fileList.map(file => file.url || file.response?.url || '').filter(Boolean),
      };

      if (review) {
        // Update existing review
        await dispatch(updateReview({
          reviewId: review.id,
          reviewData,
        })).unwrap();
        message.success('Review updated successfully!');
      } else {
        // Create new review
        await dispatch(createReview(reviewData)).unwrap();
        message.success('Review submitted successfully!');
      }

      form.resetFields();
      setRating(0);
      setFileList([]);
      onSuccess?.();
    } catch (error: any) {
      message.error(error.message || 'Failed to submit review');
    }
  };

  const handleUploadChange = (info: any) => {
    let newFileList = [...info.fileList];

    // Limit to 5 images
    newFileList = newFileList.slice(-5);

    // Handle upload response
    newFileList = newFileList.map(file => {
      if (file.response && file.response.success) {
        // Use the medium size image for display
        const mediumImage = file.response.data.images.medium;
        file.url = mediumImage?.url || mediumImage?.path;
        file.status = 'done';
      } else if (file.response && !file.response.success) {
        file.status = 'error';
        message.error(`Upload failed: ${file.response.message}`);
      }
      return file;
    });

    setFileList(newFileList);
  };

  const beforeUpload = (file: any) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('You can only upload image files!');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('Image must be smaller than 10MB!');
      return false;
    }

    return true; // Allow automatic upload
  };

  const customRequest = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('category', 'review');
      formData.append('description', 'Product review image');

      // Use the upload API from services
      const response = await fetch('/api/v1/upload/image', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}` // Add auth token
        }
      });

      const result = await response.json();

      if (result.success) {
        onSuccess(result, file);
      } else {
        onError(new Error(result.message || 'Upload failed'));
      }
    } catch (error) {
      console.error('Upload error:', error);
      onError(error);
    }
  };

  const ratingLabels = {
    1: 'Terrible',
    2: 'Poor',
    3: 'Average',
    4: 'Good',
    5: 'Excellent',
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        rating: 0,
      }}
    >
      <Form.Item
        name="rating"
        label="Overall Rating"
        rules={[
          { required: true, message: 'Please select a rating' },
          { 
            validator: (_, value) => {
              if (value && value > 0) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Please select a rating'));
            }
          }
        ]}
      >
        <div>
          <Rate
            value={rating}
            onChange={setRating}
            style={{ fontSize: '24px' }}
            character={<StarOutlined />}
          />
          {rating > 0 && (
            <Text style={{ marginLeft: '12px', fontSize: '16px' }}>
              {ratingLabels[rating as keyof typeof ratingLabels]}
            </Text>
          )}
        </div>
      </Form.Item>

      <Form.Item
        name="title"
        label="Review Title"
        rules={[
          { required: true, message: 'Please enter a review title' },
          { min: 5, message: 'Title must be at least 5 characters' },
          { max: 100, message: 'Title must be less than 100 characters' }
        ]}
      >
        <Input
          placeholder="Summarize your experience in a few words"
          maxLength={100}
          showCount
        />
      </Form.Item>

      <Form.Item
        name="comment"
        label="Detailed Review"
        rules={[
          { required: true, message: 'Please enter your review' },
          { min: 20, message: 'Review must be at least 20 characters' },
          { max: 1000, message: 'Review must be less than 1000 characters' }
        ]}
      >
        <TextArea
          rows={6}
          placeholder="Share your experience with this product. What did you like or dislike about it?"
          maxLength={1000}
          showCount
        />
      </Form.Item>

      <Form.Item label="Photos (Optional)">
        <Upload
          listType="picture-card"
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          customRequest={customRequest}
          multiple
          maxCount={5}
        >
          {fileList.length >= 5 ? null : (
            <div>
              <PictureOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </div>
          )}
        </Upload>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Upload up to 5 photos (max 10MB each)
        </Text>
      </Form.Item>

      <Card 
        size="small" 
        style={{ 
          background: '#f6f8fa', 
          border: '1px solid #e1e4e8',
          marginBottom: '24px'
        }}
      >
        <Text type="secondary" style={{ fontSize: '12px' }}>
          <strong>Review Guidelines:</strong>
          <br />
          • Be honest and helpful to other customers
          <br />
          • Focus on the product features and your experience
          <br />
          • Avoid inappropriate language or personal information
          <br />
          • Reviews are public and cannot be anonymous
        </Text>
      </Card>

      <Form.Item>
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            style={{
              background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              border: 'none',
            }}
          >
            {review ? 'Update Review' : 'Submit Review'}
          </Button>
          {onCancel && (
            <Button size="large" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
};

export default ReviewForm;
