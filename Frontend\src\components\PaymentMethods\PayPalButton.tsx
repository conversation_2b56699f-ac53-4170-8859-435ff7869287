import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, message, Spin } from 'antd';
import { AppDispatch, RootState } from '../../store';
import { createPayPalOrder, capturePayPalOrder } from '../../store/slices/paymentSlice';

interface PayPalButtonProps {
  orderId: string;
  amount: number;
  currency?: string;
  onSuccess?: (payment: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  disabled?: boolean;
}

const PayPalButton: React.FC<PayPalButtonProps> = ({
  orderId,
  amount,
  currency = 'USD',
  onSuccess,
  onError,
  onCancel,
  disabled = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, processingPayment, paypalOrderId, error } = useSelector(
    (state: RootState) => state.payments
  );

  const [isPayPalLoaded, setIsPayPalLoaded] = useState(false);
  const [paypalWindow, setPaypalWindow] = useState<Window | null>(null);

  useEffect(() => {
    // In a real app, you would load PayPal SDK here
    // For demo purposes, we'll simulate PayPal being loaded
    const timer = setTimeout(() => {
      setIsPayPalLoaded(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (error) {
      message.error(error);
      onError?.(error);
    }
  }, [error, onError]);

  const handlePayPalPayment = async () => {
    try {
      // Create PayPal order
      const result = await dispatch(createPayPalOrder({
        orderId,
        amount,
        currency,
      })).unwrap();

      // In a real app, you would redirect to PayPal or open PayPal popup
      // For demo purposes, we'll simulate the PayPal flow
      message.info('Redirecting to PayPal...');
      
      // Simulate opening PayPal window
      const popup = window.open(
        'about:blank',
        'paypal-popup',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );
      
      if (popup) {
        setPaypalWindow(popup);
        
        // Simulate PayPal approval process
        popup.document.write(`
          <html>
            <head>
              <title>PayPal Payment</title>
              <style>
                body { 
                  font-family: Arial, sans-serif; 
                  padding: 20px; 
                  text-align: center;
                  background: #f8f9fa;
                }
                .container {
                  max-width: 400px;
                  margin: 50px auto;
                  background: white;
                  padding: 30px;
                  border-radius: 8px;
                  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .paypal-logo {
                  color: #003087;
                  font-size: 24px;
                  font-weight: bold;
                  margin-bottom: 20px;
                }
                .amount {
                  font-size: 32px;
                  font-weight: bold;
                  color: #333;
                  margin: 20px 0;
                }
                .button {
                  background: #0070ba;
                  color: white;
                  border: none;
                  padding: 12px 24px;
                  border-radius: 6px;
                  font-size: 16px;
                  cursor: pointer;
                  margin: 10px;
                  min-width: 120px;
                }
                .button:hover {
                  background: #005ea6;
                }
                .cancel-button {
                  background: #6c757d;
                }
                .cancel-button:hover {
                  background: #5a6268;
                }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="paypal-logo">PayPal</div>
                <p>You are about to pay:</p>
                <div class="amount">$${amount.toFixed(2)} ${currency}</div>
                <p>Order ID: ${orderId}</p>
                <br>
                <button class="button" onclick="approvePayment()">
                  Approve Payment
                </button>
                <button class="button cancel-button" onclick="cancelPayment()">
                  Cancel
                </button>
              </div>
              
              <script>
                function approvePayment() {
                  window.opener.postMessage({
                    type: 'PAYPAL_APPROVED',
                    paypalOrderId: '${result.paypalOrderId}'
                  }, '*');
                  window.close();
                }
                
                function cancelPayment() {
                  window.opener.postMessage({
                    type: 'PAYPAL_CANCELLED'
                  }, '*');
                  window.close();
                }
              </script>
            </body>
          </html>
        `);
      }

    } catch (error: any) {
      message.error(error.message || 'Failed to initiate PayPal payment');
      onError?.(error);
    }
  };

  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      if (event.data.type === 'PAYPAL_APPROVED') {
        try {
          const payment = await dispatch(capturePayPalOrder(event.data.paypalOrderId)).unwrap();
          message.success('Payment completed successfully!');
          onSuccess?.(payment);
        } catch (error: any) {
          message.error(error.message || 'Failed to complete payment');
          onError?.(error);
        }
      } else if (event.data.type === 'PAYPAL_CANCELLED') {
        message.info('Payment cancelled');
        onCancel?.();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [dispatch, onSuccess, onError, onCancel]);

  if (!isPayPalLoaded) {
    return (
      <Button
        size="large"
        disabled
        style={{
          width: '100%',
          height: '50px',
          background: '#ffc439',
          border: 'none',
          borderRadius: '25px',
          fontSize: '16px',
          fontWeight: 'bold',
        }}
      >
        <Spin size="small" style={{ marginRight: '8px' }} />
        Loading PayPal...
      </Button>
    );
  }

  return (
    <Button
      size="large"
      loading={loading || processingPayment}
      disabled={disabled}
      onClick={handlePayPalPayment}
      style={{
        width: '100%',
        height: '50px',
        background: '#ffc439',
        border: 'none',
        borderRadius: '25px',
        color: '#003087',
        fontSize: '16px',
        fontWeight: 'bold',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {loading || processingPayment ? (
        <>
          <Spin size="small" style={{ marginRight: '8px' }} />
          Processing...
        </>
      ) : (
        <>
          <span style={{ marginRight: '8px' }}>💳</span>
          Pay with PayPal
        </>
      )}
    </Button>
  );
};

export default PayPalButton;
