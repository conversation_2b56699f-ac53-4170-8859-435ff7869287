const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config();

console.log('🚀 Starting upload test server...');

const app = express();

// Basic middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json());

// Test Sharp availability
let sharp = null;
let sharpAvailable = false;
try {
  sharp = require('sharp');
  sharpAvailable = true;
  console.log('✅ Sharp package loaded successfully');
  console.log('📊 Sharp version:', sharp.versions.sharp);
} catch (error) {
  console.log('⚠️ Sharp package not available:', error.message);
}

// Create upload directories
const uploadDirs = [
  'uploads',
  'uploads/temp',
  'uploads/images',
  'uploads/images/thumbnails',
  'uploads/images/small',
  'uploads/images/medium',
  'uploads/images/large',
  'uploads/documents'
];

uploadDirs.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.status(200).json({
    status: 'success',
    message: 'Upload test server is running',
    timestamp: new Date().toISOString(),
    sharp_available: sharpAvailable,
    sharp_version: sharpAvailable ? sharp.versions.sharp : null
  });
});

// Upload health check
app.get('/api/upload/health', (req, res) => {
  console.log('Upload health check requested');
  
  // Check upload directories
  const dirStatus = {};
  uploadDirs.forEach(dir => {
    const fullPath = path.join(__dirname, dir);
    dirStatus[dir] = fs.existsSync(fullPath);
  });
  
  res.status(200).json({
    status: 'success',
    message: 'Upload service is ready',
    timestamp: new Date().toISOString(),
    sharp_available: sharpAvailable,
    sharp_version: sharpAvailable ? sharp.versions.sharp : null,
    directories: dirStatus,
    max_file_size: '10MB',
    supported_formats: ['jpg', 'jpeg', 'png', 'gif', 'webp']
  });
});

// Test Sharp image processing
app.get('/api/upload/test-sharp', async (req, res) => {
  console.log('Sharp test requested');
  
  if (!sharpAvailable) {
    return res.status(500).json({
      status: 'error',
      message: 'Sharp not available'
    });
  }
  
  try {
    // Create a simple test image
    const testImage = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    })
    .png()
    .toBuffer();
    
    // Get image info
    const info = await sharp(testImage).metadata();
    
    res.status(200).json({
      status: 'success',
      message: 'Sharp is working correctly',
      test_image_info: {
        width: info.width,
        height: info.height,
        format: info.format,
        channels: info.channels,
        size: testImage.length
      }
    });
  } catch (error) {
    console.error('Sharp test error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Sharp test failed',
      error: error.message
    });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`
  });
});

const PORT = process.env.PORT || 5007;

const server = app.listen(PORT, () => {
  console.log(`✅ Upload test server running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/health`);
  console.log(`📁 Upload health: http://localhost:${PORT}/api/upload/health`);
  console.log(`🔧 Sharp test: http://localhost:${PORT}/api/upload/test-sharp`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down upload test server...');
  server.close(() => {
    console.log('✅ Upload test server closed');
    process.exit(0);
  });
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Rejection:', err);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});
