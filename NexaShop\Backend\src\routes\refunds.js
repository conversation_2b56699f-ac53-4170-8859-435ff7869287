const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const refundService = require('../services/refundService');
const { Refund, RefundItem, RefundStatusHistory } = require('../models/Refund');
const { Order, Payment, User, Product } = require('../models');

const router = express.Router();

// @route   POST /api/v1/refunds
// @desc    Create a new refund request
// @access  Private
router.post('/', [
  authenticate,
  body('orderId').isUUID().withMessage('Valid order ID is required'),
  body('paymentId').isUUID().withMessage('Valid payment ID is required'),
  body('amount').isFloat({ min: 0.01 }).withMessage('Amount must be greater than 0'),
  body('reason').isIn([
    'requested_by_customer',
    'duplicate',
    'fraudulent',
    'subscription_canceled',
    'product_unacceptable',
    'no_longer_want',
    'damaged_product',
    'wrong_product',
    'not_received',
    'other'
  ]).withMessage('Invalid refund reason'),
  body('refundType').optional().isIn(['full', 'partial']).withMessage('Invalid refund type'),
  body('description').optional().isString().isLength({ max: 1000 }).withMessage('Description too long'),
  body('items').optional().isArray().withMessage('Items must be an array')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      orderId,
      paymentId,
      amount,
      reason,
      description,
      refundType,
      items
    } = req.body;

    const result = await refundService.createRefund({
      orderId,
      paymentId,
      userId: req.user.id,
      amount,
      reason,
      description,
      refundType,
      items,
      requestedBy: req.user.id
    });

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Refund request created successfully',
        data: { refund: result.refund }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/refunds
// @desc    Get user's refunds
// @access  Private
router.get('/', [
  authenticate,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isString().withMessage('Status must be a string'),
  query('orderId').optional().isUUID().withMessage('Order ID must be valid UUID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      orderId
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { user_id: req.user.id };

    if (status) {
      whereClause.status = status;
    }

    if (orderId) {
      whereClause.order_id = orderId;
    }

    const { count, rows: refunds } = await Refund.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_number', 'total_amount', 'status']
        },
        {
          model: Payment,
          as: 'payment',
          attributes: ['id', 'payment_method', 'amount', 'currency']
        },
        {
          model: RefundItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'sku']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        refunds,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get refunds error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/refunds/:id
// @desc    Get refund details
// @access  Private
router.get('/:id', [
  authenticate,
  param('id').isUUID().withMessage('Valid refund ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const refund = await refundService.getRefundById(req.params.id);

    if (!refund) {
      return res.status(404).json({
        success: false,
        message: 'Refund not found'
      });
    }

    // Check if user owns this refund or is admin
    if (refund.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { refund }
    });
  } catch (error) {
    console.error('Get refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/refunds/:id/process
// @desc    Process refund (Admin only)
// @access  Private (Admin)
router.post('/:id/process', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid refund ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await refundService.processRefund(req.params.id, req.user.id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Refund processed successfully',
        data: { 
          refund: result.refund,
          providerResult: result.providerResult
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/v1/refunds/:id/status
// @desc    Update refund status (Admin only)
// @access  Private (Admin)
router.put('/:id/status', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid refund ID is required'),
  body('status').isIn([
    'pending',
    'processing',
    'succeeded',
    'failed',
    'canceled',
    'requires_action'
  ]).withMessage('Invalid status'),
  body('reason').optional().isString().withMessage('Reason must be a string'),
  body('notes').optional().isString().withMessage('Notes must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, reason, notes } = req.body;

    await refundService.updateRefundStatus(
      req.params.id,
      status,
      req.user.id,
      reason || `Status updated to ${status}`
    );

    // Update notes if provided
    if (notes) {
      await Refund.update(
        { notes },
        { where: { id: req.params.id } }
      );
    }

    const refund = await refundService.getRefundById(req.params.id);

    res.json({
      success: true,
      message: 'Refund status updated successfully',
      data: { refund }
    });
  } catch (error) {
    console.error('Update refund status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/refunds/admin/all
// @desc    Get all refunds (Admin only)
// @access  Private (Admin)
router.get('/admin/all', [
  authenticate,
  requireAdmin,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isString().withMessage('Status must be a string'),
  query('paymentMethod').optional().isString().withMessage('Payment method must be a string'),
  query('search').optional().isString().withMessage('Search must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      paymentMethod,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (paymentMethod) {
      whereClause.payment_method = paymentMethod;
    }

    const { count, rows: refunds } = await Refund.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'first_name', 'last_name', 'email']
            }
          ]
        },
        {
          model: Payment,
          as: 'payment'
        },
        {
          model: RefundItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'sku']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        refunds,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get all refunds error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/refunds/:id/history
// @desc    Get refund status history
// @access  Private
router.get('/:id/history', [
  authenticate,
  param('id').isUUID().withMessage('Valid refund ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const refund = await Refund.findByPk(req.params.id);
    if (!refund) {
      return res.status(404).json({
        success: false,
        message: 'Refund not found'
      });
    }

    // Check if user owns this refund or is admin
    if (refund.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const history = await RefundStatusHistory.findAll({
      where: { refund_id: req.params.id },
      include: [
        {
          model: User,
          as: 'changedBy',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['created_at', 'ASC']]
    });

    res.json({
      success: true,
      data: { history }
    });
  } catch (error) {
    console.error('Get refund history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
