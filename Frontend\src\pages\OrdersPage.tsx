import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Typography,
  Button,
  Space,
  Table,
  Tag,
  Select,
  Row,
  Col,
  Statistic,
  Empty,
  Image,
  Timeline,
  Modal,
  message,
} from 'antd';
import {
  ShoppingOutlined,
  EyeOutlined,
  CloseOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchOrders, cancelOrder, setFilters } from '../store/slices/orderSlice';
import { Order } from '../store/slices/orderSlice';

const { Title, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;

const OrdersPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { orders, loading, pagination, filters } = useSelector((state: RootState) => state.orders);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  useEffect(() => {
    dispatch(fetchOrders({
      page: pagination.currentPage,
      limit: pagination.itemsPerPage,
      status: filters.status
    }));
  }, [dispatch, pagination.currentPage, pagination.itemsPerPage, filters.status]);

  const handleStatusFilter = (status: string) => {
    dispatch(setFilters({ status }));
  };

  const handleCancelOrder = (order: Order) => {
    confirm({
      title: 'Cancel Order',
      content: `Are you sure you want to cancel order ${order.orderNumber}?`,
      okText: 'Cancel Order',
      okType: 'danger',
      cancelText: 'Keep Order',
      onOk: async () => {
        try {
          await dispatch(cancelOrder(order.id)).unwrap();
          message.success('Order cancelled successfully');
        } catch (error: any) {
          message.error(error.message || 'Failed to cancel order');
        }
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'confirmed':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined />;
      case 'confirmed':
        return <CheckCircleOutlined />;
      case 'shipped':
        return <TruckOutlined />;
      case 'delivered':
        return <CheckCircleOutlined />;
      case 'cancelled':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const columns = [
    {
      title: 'Order',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (orderNumber: string, record: Order) => (
        <div>
          <Link to={`/orders/${record.id}`}>
            <Text strong style={{ color: '#1890ff' }}>{orderNumber}</Text>
          </Link>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(record.createdAt).toLocaleDateString()}
          </Text>
        </div>
      ),
    },
    {
      title: 'Items',
      dataIndex: 'items',
      key: 'items',
      render: (items: Order['items']) => (
        <div>
          <Text>{items.length} item{items.length > 1 ? 's' : ''}</Text>
          <div style={{ marginTop: '4px' }}>
            {items.slice(0, 2).map((item, index) => (
              <Image
                key={index}
                src={item.image}
                alt={item.name}
                width={30}
                height={30}
                style={{
                  marginRight: '4px',
                  borderRadius: '4px',
                  objectFit: 'cover'
                }}
              />
            ))}
            {items.length > 2 && (
              <span style={{
                fontSize: '12px',
                color: '#8c8c8c',
                marginLeft: '4px'
              }}>
                +{items.length - 2} more
              </span>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Tag>
      ),
    },
    {
      title: 'Total',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number, record: Order) => (
        <Text strong>${amount.toFixed(2)} {record.currency}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: Order) => (
        <Space>
          <Link to={`/orders/${record.id}`}>
            <Button type="text" icon={<EyeOutlined />} size="small">
              View
            </Button>
          </Link>
          {['pending', 'confirmed'].includes(record.status) && (
            <Button
              type="text"
              danger
              icon={<CloseOutlined />}
              size="small"
              onClick={() => handleCancelOrder(record)}
            >
              Cancel
            </Button>
          )}
          {record.trackingNumber && (
            <Link to={`/tracking?number=${record.trackingNumber}`}>
              <Button type="text" icon={<TruckOutlined />} size="small">
                Track
              </Button>
            </Link>
          )}
        </Space>
      ),
    },
  ];

  // Calculate statistics
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    shipped: orders.filter(o => o.status === 'shipped').length,
    delivered: orders.filter(o => o.status === 'delivered').length,
  };

  if (orders.length === 0 && !loading) {
    return (
      <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <Title level={2} style={{ marginBottom: '32px' }}>
            <ShoppingOutlined style={{ marginRight: '12px' }} />
            My Orders
          </Title>

          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="No orders found"
            >
              <Link to="/products">
                <Button type="primary" icon={<ShoppingOutlined />}>
                  Start Shopping
                </Button>
              </Link>
            </Empty>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: '32px' }}>
          <ShoppingOutlined style={{ marginRight: '12px' }} />
          My Orders
        </Title>

        {/* Statistics */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Total Orders"
                value={stats.total}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Pending"
                value={stats.pending}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Shipped"
                value={stats.shipped}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={6}>
            <Card>
              <Statistic
                title="Delivered"
                value={stats.delivered}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Filters */}
        <Card style={{ marginBottom: '24px' }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <Text strong>Filter by status:</Text>
                <Select
                  value={filters.status}
                  onChange={handleStatusFilter}
                  style={{ width: 150 }}
                >
                  <Option value="all">All Orders</Option>
                  <Option value="pending">Pending</Option>
                  <Option value="confirmed">Confirmed</Option>
                  <Option value="shipped">Shipped</Option>
                  <Option value="delivered">Delivered</Option>
                  <Option value="cancelled">Cancelled</Option>
                </Select>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* Orders Table */}
        <Card>
          <Table
            columns={columns}
            dataSource={orders}
            rowKey="id"
            loading={loading}
            pagination={{
              current: pagination.currentPage,
              total: pagination.totalItems,
              pageSize: pagination.itemsPerPage,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} orders`,
            }}
            scroll={{ x: 800 }}
          />
        </Card>
      </div>
    </div>
  );
};

export default OrdersPage;
