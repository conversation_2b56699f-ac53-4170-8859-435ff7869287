const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const logisticsService = require('../services/logisticsService');
const {
  ShippingCarrier,
  ShippingService,
  Shipment,
  TrackingEvent
} = require('../models/Logistics');
const { Order, User } = require('../models');

const router = express.Router();

// @route   POST /api/v1/logistics/rates
// @desc    Calculate shipping rates
// @access  Public
router.post('/rates', [
  body('originAddress').isObject().withMessage('Origin address is required'),
  body('destinationAddress').isObject().withMessage('Destination address is required'),
  body('packages').isArray().withMessage('Packages array is required'),
  body('packages.*.weight').isFloat({ min: 0 }).withMessage('Package weight must be positive'),
  body('orderValue').optional().isFloat({ min: 0 }).withMessage('Order value must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await logisticsService.calculateShippingRates(req.body);

    if (result.success) {
      res.json({
        success: true,
        data: { rates: result.rates }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Calculate shipping rates error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/logistics/carriers
// @desc    Get all shipping carriers
// @access  Public
router.get('/carriers', async (req, res) => {
  try {
    const carriers = await ShippingCarrier.findAll({
      where: { is_active: true },
      include: [
        {
          model: ShippingService,
          as: 'services',
          where: { is_active: true },
          required: false
        }
      ],
      order: [['sort_order', 'ASC']]
    });

    res.json({
      success: true,
      data: { carriers }
    });
  } catch (error) {
    console.error('Get carriers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/logistics/shipments
// @desc    Create shipment (Admin only)
// @access  Private (Admin)
router.post('/shipments', [
  authenticate,
  requireAdmin,
  body('orderId').isUUID().withMessage('Valid order ID required'),
  body('carrierId').isUUID().withMessage('Valid carrier ID required'),
  body('serviceId').isUUID().withMessage('Valid service ID required'),
  body('originAddress').isObject().withMessage('Origin address is required'),
  body('destinationAddress').isObject().withMessage('Destination address is required'),
  body('packages').isArray().withMessage('Packages array is required'),
  body('shippingCost').isFloat({ min: 0 }).withMessage('Shipping cost must be positive'),
  body('insuranceCost').optional().isFloat({ min: 0 }).withMessage('Insurance cost must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await logisticsService.createShipment(req.body);

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Shipment created successfully',
        data: { shipment: result.shipment }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create shipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/logistics/shipments
// @desc    Get shipments (Admin only)
// @access  Private (Admin)
router.get('/shipments', [
  authenticate,
  requireAdmin,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be positive'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('status').optional().isString().withMessage('Status must be string'),
  query('carrierId').optional().isUUID().withMessage('Valid carrier ID required'),
  query('orderId').optional().isUUID().withMessage('Valid order ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      carrierId,
      orderId
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status) whereClause.status = status;
    if (carrierId) whereClause.carrier_id = carrierId;
    if (orderId) whereClause.order_id = orderId;

    const { count, rows: shipments } = await Shipment.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: ShippingCarrier,
          as: 'carrier',
          attributes: ['id', 'name', 'code', 'display_name']
        },
        {
          model: ShippingService,
          as: 'service',
          attributes: ['id', 'name', 'code', 'display_name', 'service_type']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_number', 'status']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        shipments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get shipments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/logistics/shipments/:id
// @desc    Get shipment by ID
// @access  Private (Admin) or shipment owner
router.get('/shipments/:id', [
  authenticate,
  param('id').isUUID().withMessage('Valid shipment ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const shipment = await Shipment.findByPk(req.params.id, {
      include: [
        {
          model: ShippingCarrier,
          as: 'carrier'
        },
        {
          model: ShippingService,
          as: 'service'
        },
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'first_name', 'last_name', 'email']
            }
          ]
        }
      ]
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        message: 'Shipment not found'
      });
    }

    // Check if user has permission to view this shipment
    if (req.user.role !== 'admin' && shipment.order.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { shipment }
    });
  } catch (error) {
    console.error('Get shipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/logistics/shipments/:id/label
// @desc    Generate shipping label (Admin only)
// @access  Private (Admin)
router.post('/shipments/:id/label', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid shipment ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await logisticsService.generateShippingLabel(req.params.id);

    if (result.success) {
      res.json({
        success: true,
        message: 'Shipping label generated successfully',
        data: {
          trackingNumber: result.trackingNumber,
          labelUrl: result.labelUrl
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Generate shipping label error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/logistics/track/:trackingNumber
// @desc    Track shipment by tracking number
// @access  Public
router.get('/track/:trackingNumber', [
  param('trackingNumber').isString().withMessage('Tracking number is required'),
  query('carrier').optional().isString().withMessage('Carrier code must be string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { trackingNumber } = req.params;
    const { carrier } = req.query;

    const result = await logisticsService.trackShipment(trackingNumber, carrier);

    if (result.success) {
      res.json({
        success: true,
        data: result
      });
    } else {
      res.status(404).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Track shipment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/logistics/shipments/:id/tracking
// @desc    Get shipment tracking events
// @access  Private (Admin) or shipment owner
router.get('/shipments/:id/tracking', [
  authenticate,
  param('id').isUUID().withMessage('Valid shipment ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check shipment ownership
    const shipment = await Shipment.findByPk(req.params.id, {
      include: [
        {
          model: Order,
          as: 'order',
          attributes: ['user_id']
        }
      ]
    });

    if (!shipment) {
      return res.status(404).json({
        success: false,
        message: 'Shipment not found'
      });
    }

    if (req.user.role !== 'admin' && shipment.order.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const events = await logisticsService.getShipmentTrackingEvents(req.params.id);

    res.json({
      success: true,
      data: { events }
    });
  } catch (error) {
    console.error('Get tracking events error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
