const { sequelize } = require('./src/models');

async function checkTables() {
  try {
    const tables = await sequelize.query(
      "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;",
      { type: sequelize.QueryTypes.SELECT }
    );
    
    console.log('Existing tables:');
    console.log(JSON.stringify(tables, null, 2));
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkTables();
