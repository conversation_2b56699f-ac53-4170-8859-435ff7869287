import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar: string;
  rating: number;
  title: string;
  comment: string;
  images: string[];
  verified: boolean;
  helpful: number;
  createdAt: string;
  updatedAt: string;
}

export interface ReviewStatistics {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface ReviewState {
  reviews: Review[];
  productReviews: Review[];
  currentReview: Review | null;
  statistics: ReviewStatistics | null;
  loading: boolean;
  error: string | null;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  filters: {
    sort: string;
    rating: number | null;
  };
}

const initialState: ReviewState = {
  reviews: [],
  productReviews: [],
  currentReview: null,
  statistics: null,
  loading: false,
  error: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
  },
  filters: {
    sort: 'newest',
    rating: null,
  },
};

// Async thunks
export const fetchProductReviews = createAsyncThunk(
  'reviews/fetchProductReviews',
  async ({ 
    productId, 
    page = 1, 
    limit = 10, 
    sort = 'newest' 
  }: { 
    productId: string; 
    page?: number; 
    limit?: number; 
    sort?: string; 
  }) => {
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());
    queryParams.append('sort', sort);

    const response = await fetch(`/api/v1/reviews/product/${productId}?${queryParams}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data;
  }
);

export const createReview = createAsyncThunk(
  'reviews/createReview',
  async (reviewData: {
    productId: string;
    rating: number;
    title: string;
    comment: string;
    images?: string[];
  }) => {
    const response = await fetch('/api/v1/reviews', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reviewData),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.review;
  }
);

export const updateReview = createAsyncThunk(
  'reviews/updateReview',
  async ({ 
    reviewId, 
    reviewData 
  }: { 
    reviewId: string; 
    reviewData: Partial<{
      rating: number;
      title: string;
      comment: string;
      images: string[];
    }>; 
  }) => {
    const response = await fetch(`/api/v1/reviews/${reviewId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reviewData),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.review;
  }
);

export const deleteReview = createAsyncThunk(
  'reviews/deleteReview',
  async (reviewId: string) => {
    const response = await fetch(`/api/v1/reviews/${reviewId}`, {
      method: 'DELETE',
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return reviewId;
  }
);

export const markReviewHelpful = createAsyncThunk(
  'reviews/markReviewHelpful',
  async (reviewId: string) => {
    const response = await fetch(`/api/v1/reviews/${reviewId}/helpful`, {
      method: 'POST',
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.review;
  }
);

const reviewSlice = createSlice({
  name: 'reviews',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<ReviewState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.pagination.currentPage = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearReviews: (state) => {
      state.reviews = [];
      state.productReviews = [];
      state.statistics = null;
      state.pagination = initialState.pagination;
    },
    setCurrentReview: (state, action: PayloadAction<Review | null>) => {
      state.currentReview = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch product reviews
      .addCase(fetchProductReviews.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProductReviews.fulfilled, (state, action) => {
        state.loading = false;
        state.productReviews = action.payload.reviews;
        state.statistics = action.payload.statistics;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchProductReviews.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch reviews';
      })
      // Create review
      .addCase(createReview.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createReview.fulfilled, (state, action) => {
        state.loading = false;
        state.productReviews.unshift(action.payload);
        state.reviews.unshift(action.payload);
        // Update statistics
        if (state.statistics) {
          state.statistics.totalReviews += 1;
          state.statistics.ratingDistribution[action.payload.rating as keyof typeof state.statistics.ratingDistribution] += 1;
          // Recalculate average rating
          const totalRating = state.productReviews.reduce((sum, review) => sum + review.rating, 0);
          state.statistics.averageRating = Math.round((totalRating / state.productReviews.length) * 10) / 10;
        }
      })
      .addCase(createReview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create review';
      })
      // Update review
      .addCase(updateReview.fulfilled, (state, action) => {
        const index = state.productReviews.findIndex(review => review.id === action.payload.id);
        if (index !== -1) {
          state.productReviews[index] = action.payload;
        }
        const reviewIndex = state.reviews.findIndex(review => review.id === action.payload.id);
        if (reviewIndex !== -1) {
          state.reviews[reviewIndex] = action.payload;
        }
        if (state.currentReview?.id === action.payload.id) {
          state.currentReview = action.payload;
        }
      })
      // Delete review
      .addCase(deleteReview.fulfilled, (state, action) => {
        state.productReviews = state.productReviews.filter(review => review.id !== action.payload);
        state.reviews = state.reviews.filter(review => review.id !== action.payload);
        if (state.currentReview?.id === action.payload) {
          state.currentReview = null;
        }
        // Update statistics
        if (state.statistics) {
          state.statistics.totalReviews -= 1;
        }
      })
      // Mark review helpful
      .addCase(markReviewHelpful.fulfilled, (state, action) => {
        const index = state.productReviews.findIndex(review => review.id === action.payload.id);
        if (index !== -1) {
          state.productReviews[index] = action.payload;
        }
        const reviewIndex = state.reviews.findIndex(review => review.id === action.payload.id);
        if (reviewIndex !== -1) {
          state.reviews[reviewIndex] = action.payload;
        }
      });
  },
});

export const {
  setFilters,
  setCurrentPage,
  clearError,
  clearReviews,
  setCurrentReview,
} = reviewSlice.actions;

export default reviewSlice.reducer;
