import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams, Link } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Space,
  Pagination,
  Spin,
  Empty,
  Slider,
  Checkbox,
  Select,
  Input,
  Rate,
  Tag,
  Badge,
  Drawer,
} from 'antd';
import {
  FilterOutlined,
  ShoppingCartOutlined,
  HeartOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchProducts, searchProducts, setFilters, setCurrentPage } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { addToWishlist } from '../store/slices/userSlice';
import ProductCard from '../components/ProductCard/ProductCard';
import SearchBar from '../components/Search/SearchBar';
import ProductFilters from '../components/Search/ProductFilters';

const { Title, Text } = Typography;
const { Meta } = Card;
const { Option } = Select;
const { Search } = Input;

const ProductListPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);

  const { products, loading, filters, pagination } = useSelector((state: RootState) => state.products);

  useEffect(() => {
    const category = searchParams.get('category') || '';
    const brand = searchParams.get('brand') || '';
    const search = searchParams.get('search') || '';
    const minPrice = searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined;
    const maxPrice = searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined;
    const rating = searchParams.get('rating') ? Number(searchParams.get('rating')) : undefined;
    const inStock = searchParams.get('inStock') ? searchParams.get('inStock') === 'true' : undefined;
    const featured = searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined;
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';
    const page = searchParams.get('page') ? Number(searchParams.get('page')) : 1;

    const filters = {
      category: category || undefined,
      brand: brand || undefined,
      minPrice,
      maxPrice,
      rating,
      inStock,
      featured,
    };

    dispatch(setFilters(filters));

    if (search) {
      dispatch(searchProducts({
        query: search,
        page,
        limit: pagination.itemsPerPage,
        ...filters,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      }));
    } else {
      dispatch(fetchProducts({
        page,
        limit: pagination.itemsPerPage,
        ...filters,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
      }));
    }
  }, [dispatch, searchParams]);

  const handleAddToCart = (product: any) => {
    dispatch(addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      shipping: product.shipping,
    }));
  };

  const handleAddToWishlist = (productId: string) => {
    dispatch(addToWishlist(productId));
  };

  const handleFilterChange = (key: string, value: any) => {
    dispatch(setFilters({ [key]: value }));
  };

  const handlePageChange = (page: number) => {
    dispatch(setCurrentPage(page));
  };



  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        {/* Search Bar */}
        <div style={{ background: '#fff', padding: '24px', borderRadius: '8px', marginBottom: '24px' }}>
          <SearchBar
            placeholder="Search products, brands, categories..."
            showFilters={true}
            showSort={true}
          />
        </div>

        {/* Page Header */}
        <div style={{ background: '#fff', padding: '16px 24px', borderRadius: '8px', marginBottom: '24px' }}>
          <Space>
            <Title level={2} style={{ margin: 0 }}>
              Products
            </Title>
            {searchParams.get('search') && (
              <Tag color="blue">
                Search: "{searchParams.get('search')}"
              </Tag>
            )}
            {searchParams.get('category') && (
              <Tag color="green">
                Category: {searchParams.get('category')}
              </Tag>
            )}
            {products.length > 0 && (
              <Text type="secondary">
                ({pagination.totalItems} results)
              </Text>
            )}
          </Space>
        </div>

        <Row gutter={24}>
          {/* Desktop Filters */}
          <Col xs={0} md={6}>
            <ProductFilters />
          </Col>

          {/* Products */}
          <Col xs={24} md={18}>
            <Spin spinning={loading}>
              {products.length === 0 ? (
                <Empty
                  description="No products found"
                  style={{ background: '#fff', padding: '48px', borderRadius: '8px' }}
                />
              ) : (
                <>
                  <Row gutter={[24, 24]}>
                    {products.map((product) => (
                      <Col xs={12} sm={8} lg={6} key={product.id}>
                        <ProductCard
                          product={product}
                          loading={loading}
                        />
                      </Col>
                    ))}
                  </Row>

                  {/* Pagination */}
                  <div style={{ textAlign: 'center', marginTop: '32px' }}>
                    <Pagination
                      current={pagination.currentPage}
                      total={pagination.totalItems}
                      pageSize={pagination.itemsPerPage}
                      onChange={handlePageChange}
                      showSizeChanger={false}
                      showQuickJumper
                      showTotal={(total, range) =>
                        `${range[0]}-${range[1]} of ${total} products`
                      }
                    />
                  </div>
                </>
              )}
            </Spin>
          </Col>
        </Row>

        {/* Mobile Filter Drawer */}
        <Drawer
          title="Filters"
          placement="right"
          onClose={() => setFilterDrawerVisible(false)}
          open={filterDrawerVisible}
          width={320}
        >
          <ProductFilters />
        </Drawer>
      </div>
    </div>
  );
};

export default ProductListPage;
