import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Space,
  Rate,
  Tag,
  InputNumber,
  Select,
  Carousel,
  Tabs,
  Divider,
  Badge,
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  ShareAltOutlined,
  TruckOutlined,
  SafetyOutlined,
  CustomerServiceOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchProductById } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { addToWishlist, addToRecentlyViewed } from '../store/slices/userSlice';
import ReviewList from '../components/Reviews/ReviewList';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const dispatch = useDispatch<AppDispatch>();
  const [quantity, setQuantity] = useState(1);
  const [selectedVariant, setSelectedVariant] = useState<Record<string, string>>({});

  const { currentProduct: product, loading } = useSelector((state: RootState) => state.products);

  useEffect(() => {
    if (id) {
      dispatch(fetchProductById(id));
      dispatch(addToRecentlyViewed(id));
    }
  }, [dispatch, id]);

  const handleAddToCart = () => {
    if (product) {
      dispatch(addToCart({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.images[0],
        quantity,
        variant: selectedVariant,
        shipping: {
          cost: product.shipping.cost,
          estimatedDays: product.shipping.estimatedDays,
          carrier: product.shipping.carriers[0] || 'Standard',
        },
      }));
    }
  };

  const handleAddToWishlist = () => {
    if (product) {
      dispatch(addToWishlist(product.id));
    }
  };

  if (loading || !product) {
    return (
      <div style={{ padding: '48px', textAlign: 'center' }}>
        <Card loading={loading} />
      </div>
    );
  }

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        <Row gutter={24}>
          {/* Product Images */}
          <Col xs={24} md={12}>
            <Card style={{ marginBottom: '24px' }}>
              <Carousel dots={{ className: 'custom-dots' }}>
                {product.images.map((image, index) => (
                  <div key={index}>
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      style={{ width: '100%', height: '400px', objectFit: 'cover' }}
                    />
                  </div>
                ))}
              </Carousel>
            </Card>
          </Col>

          {/* Product Info */}
          <Col xs={24} md={12}>
            <Card>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* Title and Rating */}
                <div>
                  <Title level={2}>{product.name}</Title>
                  <Space>
                    <Rate disabled defaultValue={product.rating} />
                    <Text>({product.reviewCount} reviews)</Text>
                    <Text type="secondary">Brand: {product.brand}</Text>
                  </Space>
                </div>

                {/* Price */}
                <div>
                  <Space align="baseline">
                    <Title level={3} style={{ color: '#1890ff', margin: 0 }}>
                      ${product.price}
                    </Title>
                    {product.originalPrice && (
                      <Text delete type="secondary" style={{ fontSize: '18px' }}>
                        ${product.originalPrice}
                      </Text>
                    )}
                    {product.discount && (
                      <Badge count={`-${product.discount.percentage}%`} style={{ backgroundColor: '#f50' }} />
                    )}
                  </Space>
                  <div style={{ marginTop: '8px' }}>
                    <Tag color="green">Free Shipping</Tag>
                    <Tag color="blue">In Stock ({product.stockQuantity} available)</Tag>
                  </div>
                </div>

                {/* Variants */}
                {product.variants && (
                  <div>
                    {product.variants.colors && (
                      <div style={{ marginBottom: '16px' }}>
                        <Text strong>Color: </Text>
                        <Select
                          placeholder="Select color"
                          style={{ width: 120, marginLeft: '8px' }}
                          onChange={(value) => setSelectedVariant({ ...selectedVariant, color: value })}
                        >
                          {product.variants.colors.map((color) => (
                            <Option key={color} value={color}>{color}</Option>
                          ))}
                        </Select>
                      </div>
                    )}
                    {product.variants.sizes && (
                      <div style={{ marginBottom: '16px' }}>
                        <Text strong>Size: </Text>
                        <Select
                          placeholder="Select size"
                          style={{ width: 120, marginLeft: '8px' }}
                          onChange={(value) => setSelectedVariant({ ...selectedVariant, size: value })}
                        >
                          {product.variants.sizes.map((size) => (
                            <Option key={size} value={size}>{size}</Option>
                          ))}
                        </Select>
                      </div>
                    )}
                  </div>
                )}

                {/* Quantity and Actions */}
                <div>
                  <Space size="large">
                    <div>
                      <Text strong>Quantity: </Text>
                      <InputNumber
                        min={1}
                        max={product.stockQuantity}
                        value={quantity}
                        onChange={(value) => setQuantity(value || 1)}
                        style={{ marginLeft: '8px' }}
                      />
                    </div>
                  </Space>
                </div>

                <Space size="middle" style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    size="large"
                    icon={<ShoppingCartOutlined />}
                    onClick={handleAddToCart}
                    style={{ flex: 1 }}
                  >
                    Add to Cart
                  </Button>
                  <Button
                    size="large"
                    icon={<HeartOutlined />}
                    onClick={handleAddToWishlist}
                  >
                    Wishlist
                  </Button>
                  <Button size="large" icon={<ShareAltOutlined />}>
                    Share
                  </Button>
                </Space>

                {/* Features */}
                <div>
                  <Space direction="vertical" size="small">
                    <Space>
                      <TruckOutlined style={{ color: '#1890ff' }} />
                      <Text>Free shipping on orders over $50</Text>
                    </Space>
                    <Space>
                      <SafetyOutlined style={{ color: '#1890ff' }} />
                      <Text>Secure payment with SSL encryption</Text>
                    </Space>
                    <Space>
                      <CustomerServiceOutlined style={{ color: '#1890ff' }} />
                      <Text>24/7 customer support</Text>
                    </Space>
                  </Space>
                </div>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* Product Details Tabs */}
        <Card style={{ marginTop: '24px' }}>
          <Tabs defaultActiveKey="description">
            <TabPane tab="Description" key="description">
              <Paragraph>{product.description}</Paragraph>
            </TabPane>
            <TabPane tab="Specifications" key="specifications">
              <Row gutter={[16, 16]}>
                {Object.entries(product.specifications).map(([key, value]) => (
                  <Col xs={24} sm={12} key={key}>
                    <Space>
                      <Text strong>{key}:</Text>
                      <Text>{value}</Text>
                    </Space>
                  </Col>
                ))}
              </Row>
            </TabPane>
            <TabPane tab="Shipping" key="shipping">
              <Space direction="vertical" size="middle">
                <div>
                  <Text strong>Shipping Cost: </Text>
                  <Text>${product.shipping.cost}</Text>
                </div>
                <div>
                  <Text strong>Estimated Delivery: </Text>
                  <Text>{product.shipping.estimatedDays} days</Text>
                </div>
                <div>
                  <Text strong>Available Carriers: </Text>
                  <Text>{product.shipping.carriers.join(', ')}</Text>
                </div>
                {product.shipping.freeShippingThreshold && (
                  <div>
                    <Text strong>Free Shipping: </Text>
                    <Text>On orders over ${product.shipping.freeShippingThreshold}</Text>
                  </div>
                )}
              </Space>
            </TabPane>
            <TabPane tab={`Reviews (${product.reviewCount})`} key="reviews">
              <ReviewList productId={product.id} showAddReview={true} />
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </div>
  );
};

export default ProductDetailPage;
