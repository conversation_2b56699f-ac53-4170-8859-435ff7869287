import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Space,
  Carousel,
  Badge,
  Rate,
  Tag,
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  EyeOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchFeaturedProducts } from '../store/slices/productSlice';
import { addToCart } from '../store/slices/cartSlice';
import { addToWishlist } from '../store/slices/userSlice';
import ProductCard from '../components/ProductCard/ProductCard';

const { Title, Text } = Typography;
const { Meta } = Card;

const HomePage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { featuredProducts, loading } = useSelector((state: RootState) => state.products);

  useEffect(() => {
    dispatch(fetchFeaturedProducts(8));
  }, [dispatch]);

  const handleAddToCart = (product: any) => {
    dispatch(addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      shipping: {
        cost: product.shipping.cost,
        estimatedDays: product.shipping.estimatedDays,
        carrier: product.shipping.carriers[0] || 'Standard',
      },
    }));
  };

  const handleAddToWishlist = (productId: string) => {
    dispatch(addToWishlist(productId));
  };

  // Hero Carousel Data
  const heroSlides = [
    {
      id: 1,
      title: 'Global Electronics Sale',
      subtitle: 'Up to 50% off on premium electronics worldwide',
      image: 'https://images.unsplash.com/photo-1468495244123-6c6c332eeece?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      buttonText: 'Shop Electronics',
      link: '/products?category=electronics',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    {
      id: 2,
      title: 'Fashion Week Special',
      subtitle: 'Latest trends from around the world',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      buttonText: 'Explore Fashion',
      link: '/products?category=fashion',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    },
    {
      id: 3,
      title: 'Home & Garden Collection',
      subtitle: 'Transform your space with global designs',
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
      buttonText: 'Shop Home',
      link: '/products?category=home',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    },
  ];

  // Categories Data
  const categories = [
    {
      name: 'Electronics',
      icon: '📱',
      link: '/products?category=electronics',
      count: '2.5k+',
      color: '#1890ff',
      bgColor: '#e6f7ff'
    },
    {
      name: 'Fashion',
      icon: '👗',
      link: '/products?category=fashion',
      count: '1.8k+',
      color: '#eb2f96',
      bgColor: '#fff0f6'
    },
    {
      name: 'Home & Garden',
      icon: '🏠',
      link: '/products?category=home',
      count: '950+',
      color: '#52c41a',
      bgColor: '#f6ffed'
    },
    {
      name: 'Sports',
      icon: '⚽',
      link: '/products?category=sports',
      count: '720+',
      color: '#fa8c16',
      bgColor: '#fff7e6'
    },
    {
      name: 'Beauty',
      icon: '💄',
      link: '/products?category=beauty',
      count: '640+',
      color: '#722ed1',
      bgColor: '#f9f0ff'
    },
    {
      name: 'Books',
      icon: '📚',
      link: '/products?category=books',
      count: '1.2k+',
      color: '#13c2c2',
      bgColor: '#e6fffb'
    },
  ];

  return (
    <div style={{ background: '#f5f5f5' }}>
      {/* Hero Carousel */}
      <Carousel
        autoplay
        effect="fade"
        style={{ marginBottom: '48px' }}
        dots={{ className: 'custom-carousel-dots' }}
      >
        {heroSlides.map((slide) => (
          <div key={slide.id}>
            <div
              style={{
                height: '600px',
                background: `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url(${slide.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                textAlign: 'center',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <div
                style={{
                  background: slide.gradient,
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  opacity: 0.8,
                  zIndex: 1,
                }}
              />
              <div style={{ position: 'relative', zIndex: 2, maxWidth: '600px', padding: '0 24px' }}>
                <Title
                  level={1}
                  style={{
                    color: 'white',
                    fontSize: '56px',
                    marginBottom: '24px',
                    fontWeight: 'bold',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
                  }}
                >
                  {slide.title}
                </Title>
                <Text
                  style={{
                    color: 'white',
                    fontSize: '24px',
                    display: 'block',
                    marginBottom: '40px',
                    textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
                  }}
                >
                  {slide.subtitle}
                </Text>
                <Link to={slide.link}>
                  <Button
                    type="primary"
                    size="large"
                    icon={<RightOutlined />}
                    style={{
                      height: '56px',
                      fontSize: '18px',
                      padding: '0 32px',
                      borderRadius: '28px',
                      background: 'rgba(255,255,255,0.2)',
                      border: '2px solid rgba(255,255,255,0.3)',
                      backdropFilter: 'blur(10px)',
                    }}
                  >
                    {slide.buttonText}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </Carousel>

      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 24px' }}>
        {/* Categories Section */}
        <section style={{ marginBottom: '48px' }}>
          <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
            Shop by Category
          </Title>
          <Row gutter={[24, 24]}>
            {categories.map((category) => (
              <Col xs={12} sm={8} md={4} key={category.name}>
                <Link to={category.link}>
                  <Card
                    hoverable
                    style={{
                      textAlign: 'center',
                      height: '160px',
                      borderRadius: '16px',
                      border: 'none',
                      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                      transition: 'all 0.3s ease',
                      background: category.bgColor,
                    }}
                    bodyStyle={{ padding: '24px 16px' }}
                    className="category-card"
                  >
                    <div
                      style={{
                        fontSize: '40px',
                        marginBottom: '12px',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                      }}
                    >
                      {category.icon}
                    </div>
                    <Text
                      strong
                      style={{
                        fontSize: '16px',
                        color: category.color,
                        display: 'block',
                        marginBottom: '4px'
                      }}
                    >
                      {category.name}
                    </Text>
                    <Text
                      type="secondary"
                      style={{
                        fontSize: '13px',
                        color: '#8c8c8c'
                      }}
                    >
                      {category.count} items
                    </Text>
                  </Card>
                </Link>
              </Col>
            ))}
          </Row>
        </section>

        {/* Featured Products */}
        <section style={{ marginBottom: '48px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '32px' }}>
            <Title level={2}>Featured Products</Title>
            <Link to="/products">
              <Button type="link" icon={<RightOutlined />}>
                View All Products
              </Button>
            </Link>
          </div>

          <Row gutter={[24, 24]}>
            {featuredProducts.slice(0, 8).map((product) => (
              <Col xs={12} sm={8} md={6} key={product.id}>
                <ProductCard
                  product={product}
                  loading={loading}
                />
              </Col>
            ))}
          </Row>
        </section>

        {/* Features Section */}
        <section style={{ marginBottom: '48px' }}>
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <Card style={{ textAlign: 'center', height: '100%' }}>
                <div style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }}>
                  🚚
                </div>
                <Title level={4}>Free Global Shipping</Title>
                <Text type="secondary">
                  Free shipping on orders over $50 to most countries worldwide
                </Text>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{ textAlign: 'center', height: '100%' }}>
                <div style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }}>
                  🔒
                </div>
                <Title level={4}>Secure Payments</Title>
                <Text type="secondary">
                  Your payment information is processed securely with SSL encryption
                </Text>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{ textAlign: 'center', height: '100%' }}>
                <div style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }}>
                  📞
                </div>
                <Title level={4}>24/7 Support</Title>
                <Text type="secondary">
                  Get help anytime with our round-the-clock customer support
                </Text>
              </Card>
            </Col>
          </Row>
        </section>
      </div>
    </div>
  );
};

export default HomePage;
