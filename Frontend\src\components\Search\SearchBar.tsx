import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { Input, AutoComplete, Button, Space, Dropdown, Menu } from 'antd';
import { SearchOutlined, FilterOutlined, SortAscendingOutlined } from '@ant-design/icons';
import { AppDispatch } from '../../store';
import { searchProducts } from '../../store/slices/productSlice';
import { debounce } from 'lodash';

const { Search } = Input;

interface SearchBarProps {
  placeholder?: string;
  showFilters?: boolean;
  showSort?: boolean;
  onSearch?: (value: string) => void;
  className?: string;
}

interface SearchSuggestion {
  value: string;
  label: string;
  category?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search products...",
  showFilters = true,
  showSort = true,
  onSearch,
  className
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();

  // Initialize search value from URL params
  useEffect(() => {
    const query = searchParams.get('search');
    if (query) {
      setSearchValue(query);
    }
  }, [searchParams]);

  // Debounced search suggestions
  const debouncedGetSuggestions = useCallback(
    debounce(async (value: string) => {
      if (value.length < 2) {
        setSuggestions([]);
        return;
      }

      setLoading(true);
      try {
        // Mock suggestions - replace with actual API call
        const mockSuggestions: SearchSuggestion[] = [
          { value: `${value} headphones`, label: `${value} headphones`, category: 'Electronics' },
          { value: `${value} laptop`, label: `${value} laptop`, category: 'Electronics' },
          { value: `${value} phone`, label: `${value} phone`, category: 'Electronics' },
          { value: `${value} watch`, label: `${value} watch`, category: 'Accessories' },
          { value: `${value} shoes`, label: `${value} shoes`, category: 'Fashion' },
        ].filter(item => 
          item.value.toLowerCase().includes(value.toLowerCase())
        );

        setSuggestions(mockSuggestions);
      } catch (error) {
        console.error('Error fetching suggestions:', error);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    debouncedGetSuggestions(value);
  };

  // Handle search submit
  const handleSearch = (value: string) => {
    if (!value.trim()) return;

    // Update URL with search params
    const params = new URLSearchParams(searchParams);
    params.set('search', value.trim());
    params.delete('page'); // Reset to first page
    navigate(`/products?${params.toString()}`);

    // Dispatch search action
    dispatch(searchProducts({ query: value.trim(), page: 1, limit: 12 }));

    // Call custom onSearch callback
    if (onSearch) {
      onSearch(value.trim());
    }

    // Clear suggestions
    setSuggestions([]);
  };

  // Handle suggestion select
  const handleSuggestionSelect = (value: string) => {
    setSearchValue(value);
    handleSearch(value);
  };

  // Sort options
  const sortOptions = [
    { key: 'relevance', label: 'Relevance' },
    { key: 'price_low', label: 'Price: Low to High' },
    { key: 'price_high', label: 'Price: High to Low' },
    { key: 'rating', label: 'Customer Rating' },
    { key: 'newest', label: 'Newest First' },
    { key: 'popular', label: 'Most Popular' },
  ];

  // Handle sort change
  const handleSortChange = ({ key }: { key: string }) => {
    const params = new URLSearchParams(searchParams);
    params.set('sortBy', key);
    navigate(`/products?${params.toString()}`);
  };

  // Filter menu
  const filterMenu = (
    <Menu>
      <Menu.Item key="category">
        <span>Category</span>
      </Menu.Item>
      <Menu.Item key="price">
        <span>Price Range</span>
      </Menu.Item>
      <Menu.Item key="brand">
        <span>Brand</span>
      </Menu.Item>
      <Menu.Item key="rating">
        <span>Rating</span>
      </Menu.Item>
      <Menu.Item key="availability">
        <span>Availability</span>
      </Menu.Item>
    </Menu>
  );

  // Sort menu
  const sortMenu = (
    <Menu onClick={handleSortChange}>
      {sortOptions.map(option => (
        <Menu.Item key={option.key}>
          {option.label}
        </Menu.Item>
      ))}
    </Menu>
  );

  // Format suggestions for AutoComplete
  const formattedSuggestions = suggestions.map(suggestion => ({
    value: suggestion.value,
    label: (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>{suggestion.label}</span>
        {suggestion.category && (
          <span style={{ color: '#999', fontSize: '12px' }}>{suggestion.category}</span>
        )}
      </div>
    ),
  }));

  return (
    <div className={`search-bar ${className || ''}`}>
      <Space.Compact style={{ width: '100%' }}>
        <AutoComplete
          style={{ flex: 1 }}
          options={formattedSuggestions}
          onSelect={handleSuggestionSelect}
          onSearch={handleSearchChange}
          value={searchValue}
          placeholder={placeholder}
          notFoundContent={loading ? 'Loading...' : 'No suggestions'}
        >
          <Search
            enterButton={<SearchOutlined />}
            onSearch={handleSearch}
            loading={loading}
            allowClear
          />
        </AutoComplete>
        
        {showFilters && (
          <Dropdown overlay={filterMenu} trigger={['click']}>
            <Button icon={<FilterOutlined />} />
          </Dropdown>
        )}
        
        {showSort && (
          <Dropdown overlay={sortMenu} trigger={['click']}>
            <Button icon={<SortAscendingOutlined />} />
          </Dropdown>
        )}
      </Space.Compact>
    </div>
  );
};

export default SearchBar;
