const { Sequelize } = require('sequelize');
const config = require('./config/config.json').development;

async function checkDatabaseTables() {
  const sequelize = new Sequelize(config.database, config.username, config.password, config);
  
  try {
    console.log('🔍 检查数据库表...');
    
    // 检查数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 查询所有表
    const [results] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log('\n📋 数据库表列表:');
    if (results.length === 0) {
      console.log('⚠️  没有找到数据表，需要运行数据库迁移');
      console.log('\n💡 建议执行:');
      console.log('   npx sequelize-cli db:migrate');
      console.log('   npx sequelize-cli db:seed:all');
    } else {
      console.log(`📊 找到 ${results.length} 个数据表:`);
      results.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.table_name}`);
      });
    }
    
    // 检查关键表是否存在
    const keyTables = ['users', 'products', 'categories', 'orders'];
    const existingTables = results.map(row => row.table_name);
    
    console.log('\n🔑 关键表检查:');
    keyTables.forEach(table => {
      const exists = existingTables.includes(table);
      console.log(`   ${exists ? '✅' : '❌'} ${table}: ${exists ? '存在' : '缺失'}`);
    });
    
    await sequelize.close();
    
  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message);
    process.exit(1);
  }
}

checkDatabaseTables();
