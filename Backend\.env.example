# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/crossborder-ecommerce
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d
JWT_COOKIE_EXPIRE=7

# Email Configuration
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password

# Payment Gateway Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# Cloud Storage Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Shipping API Configuration
FEDEX_API_KEY=your_fedex_api_key
UPS_API_KEY=your_ups_api_key
DHL_API_KEY=your_dhl_api_key

# External Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
CURRENCY_API_KEY=your_currency_api_key

# Security
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Session Configuration
SESSION_SECRET=your-session-secret-key

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Notification Services
FIREBASE_SERVER_KEY=your_firebase_server_key
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
