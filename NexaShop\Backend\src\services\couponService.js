const { Coupon, CouponProduct, CouponCategory, CouponUser, CouponUsage } = require('../models/Coupon');
const { Product, Category, User, Order } = require('../models');
const { Op } = require('sequelize');

class CouponService {
  /**
   * Create a new coupon
   * @param {Object} couponData - Coupon data
   * @returns {Object} Created coupon
   */
  async createCoupon(couponData) {
    try {
      const {
        code,
        name,
        description,
        type,
        value,
        currency,
        minimumAmount,
        maximumDiscount,
        usageLimit,
        usageLimitPerUser,
        startDate,
        endDate,
        applicableTo,
        firstTimeOnly,
        stackable,
        autoApply,
        createdBy,
        productIds = [],
        categoryIds = [],
        userIds = [],
        buyQuantity,
        getQuantity,
        getDiscountPercentage,
        metadata = {}
      } = couponData;

      // Validate coupon code uniqueness
      const existingCoupon = await Coupon.findOne({ where: { code } });
      if (existingCoupon) {
        throw new Error('Coupon code already exists');
      }

      // Validate dates
      if (endDate && new Date(startDate) >= new Date(endDate)) {
        throw new Error('End date must be after start date');
      }

      // Create coupon
      const coupon = await Coupon.create({
        code: code.toUpperCase(),
        name,
        description,
        type,
        value,
        currency,
        minimum_amount: minimumAmount,
        maximum_discount: maximumDiscount,
        usage_limit: usageLimit,
        usage_limit_per_user: usageLimitPerUser,
        start_date: startDate,
        end_date: endDate,
        applicable_to: applicableTo,
        first_time_only: firstTimeOnly,
        stackable,
        auto_apply: autoApply,
        created_by: createdBy,
        buy_quantity: buyQuantity,
        get_quantity: getQuantity,
        get_discount_percentage: getDiscountPercentage,
        metadata
      });

      // Create associations based on applicable_to
      if (applicableTo === 'specific_products' && productIds.length > 0) {
        const productAssociations = productIds.map(productId => ({
          coupon_id: coupon.id,
          product_id: productId
        }));
        await CouponProduct.bulkCreate(productAssociations);
      }

      if (applicableTo === 'specific_categories' && categoryIds.length > 0) {
        const categoryAssociations = categoryIds.map(categoryId => ({
          coupon_id: coupon.id,
          category_id: categoryId
        }));
        await CouponCategory.bulkCreate(categoryAssociations);
      }

      if (applicableTo === 'specific_users' && userIds.length > 0) {
        const userAssociations = userIds.map(userId => ({
          coupon_id: coupon.id,
          user_id: userId
        }));
        await CouponUser.bulkCreate(userAssociations);
      }

      return {
        success: true,
        coupon: await this.getCouponById(coupon.id)
      };
    } catch (error) {
      console.error('Create coupon error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate and apply coupon to order
   * @param {string} couponCode - Coupon code
   * @param {Object} orderData - Order data
   * @param {string} userId - User ID
   * @returns {Object} Validation result with discount calculation
   */
  async validateAndApplyCoupon(couponCode, orderData, userId) {
    try {
      const coupon = await this.getCouponByCode(couponCode);
      if (!coupon) {
        return {
          valid: false,
          error: 'Coupon not found'
        };
      }

      // Validate coupon status and dates
      const validation = await this.validateCouponEligibility(coupon, userId, orderData);
      if (!validation.valid) {
        return validation;
      }

      // Calculate discount
      const discountCalculation = await this.calculateDiscount(coupon, orderData);
      
      return {
        valid: true,
        coupon: coupon,
        discount: discountCalculation
      };
    } catch (error) {
      console.error('Validate coupon error:', error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Validate coupon eligibility
   * @param {Object} coupon - Coupon object
   * @param {string} userId - User ID
   * @param {Object} orderData - Order data
   * @returns {Object} Validation result
   */
  async validateCouponEligibility(coupon, userId, orderData) {
    const now = new Date();

    // Check if coupon is active
    if (coupon.status !== 'active') {
      return { valid: false, error: 'Coupon is not active' };
    }

    // Check start date
    if (new Date(coupon.start_date) > now) {
      return { valid: false, error: 'Coupon is not yet valid' };
    }

    // Check end date
    if (coupon.end_date && new Date(coupon.end_date) < now) {
      return { valid: false, error: 'Coupon has expired' };
    }

    // Check usage limit
    if (coupon.usage_limit && coupon.used_count >= coupon.usage_limit) {
      return { valid: false, error: 'Coupon usage limit reached' };
    }

    // Check user-specific usage limit
    if (coupon.usage_limit_per_user) {
      const userUsageCount = await CouponUsage.count({
        where: {
          coupon_id: coupon.id,
          user_id: userId
        }
      });

      if (userUsageCount >= coupon.usage_limit_per_user) {
        return { valid: false, error: 'User usage limit reached for this coupon' };
      }
    }

    // Check minimum amount
    if (coupon.minimum_amount && orderData.subtotal < coupon.minimum_amount) {
      return {
        valid: false,
        error: `Minimum order amount of ${coupon.currency} ${coupon.minimum_amount} required`
      };
    }

    // Check first-time customer requirement
    if (coupon.first_time_only) {
      const previousOrders = await Order.count({
        where: {
          user_id: userId,
          status: 'completed'
        }
      });

      if (previousOrders > 0) {
        return { valid: false, error: 'This coupon is only for first-time customers' };
      }
    }

    // Check user eligibility for specific_users coupons
    if (coupon.applicable_to === 'specific_users') {
      const userAssociation = await CouponUser.findOne({
        where: {
          coupon_id: coupon.id,
          user_id: userId
        }
      });

      if (!userAssociation) {
        return { valid: false, error: 'This coupon is not available for your account' };
      }
    }

    // Check product/category eligibility
    if (coupon.applicable_to === 'specific_products') {
      const applicableProducts = await CouponProduct.findAll({
        where: { coupon_id: coupon.id }
      });
      
      const applicableProductIds = applicableProducts.map(cp => cp.product_id);
      const orderProductIds = orderData.items.map(item => item.product_id);
      
      const hasApplicableProduct = orderProductIds.some(id => 
        applicableProductIds.includes(id)
      );

      if (!hasApplicableProduct) {
        return { valid: false, error: 'No applicable products in cart' };
      }
    }

    if (coupon.applicable_to === 'specific_categories') {
      const applicableCategories = await CouponCategory.findAll({
        where: { coupon_id: coupon.id }
      });
      
      const applicableCategoryIds = applicableCategories.map(cc => cc.category_id);
      
      // Get product categories for items in order
      const products = await Product.findAll({
        where: {
          id: { [Op.in]: orderData.items.map(item => item.product_id) }
        },
        attributes: ['id', 'category_id']
      });

      const orderCategoryIds = products.map(p => p.category_id);
      
      const hasApplicableCategory = orderCategoryIds.some(id => 
        applicableCategoryIds.includes(id)
      );

      if (!hasApplicableCategory) {
        return { valid: false, error: 'No applicable categories in cart' };
      }
    }

    return { valid: true };
  }

  /**
   * Calculate discount amount
   * @param {Object} coupon - Coupon object
   * @param {Object} orderData - Order data
   * @returns {Object} Discount calculation
   */
  async calculateDiscount(coupon, orderData) {
    let discountAmount = 0;
    let applicableAmount = orderData.subtotal;

    // For specific products/categories, calculate applicable amount
    if (coupon.applicable_to === 'specific_products') {
      const applicableProducts = await CouponProduct.findAll({
        where: { coupon_id: coupon.id }
      });
      const applicableProductIds = applicableProducts.map(cp => cp.product_id);
      
      applicableAmount = orderData.items
        .filter(item => applicableProductIds.includes(item.product_id))
        .reduce((sum, item) => sum + (item.price * item.quantity), 0);
    }

    if (coupon.applicable_to === 'specific_categories') {
      const applicableCategories = await CouponCategory.findAll({
        where: { coupon_id: coupon.id }
      });
      const applicableCategoryIds = applicableCategories.map(cc => cc.category_id);
      
      const products = await Product.findAll({
        where: {
          id: { [Op.in]: orderData.items.map(item => item.product_id) }
        },
        attributes: ['id', 'category_id']
      });

      const applicableProductIds = products
        .filter(p => applicableCategoryIds.includes(p.category_id))
        .map(p => p.id);

      applicableAmount = orderData.items
        .filter(item => applicableProductIds.includes(item.product_id))
        .reduce((sum, item) => sum + (item.price * item.quantity), 0);
    }

    // Calculate discount based on type
    switch (coupon.type) {
      case 'percentage':
        discountAmount = (applicableAmount * coupon.value) / 100;
        if (coupon.maximum_discount) {
          discountAmount = Math.min(discountAmount, coupon.maximum_discount);
        }
        break;

      case 'fixed_amount':
        discountAmount = Math.min(coupon.value, applicableAmount);
        break;

      case 'free_shipping':
        discountAmount = orderData.shipping_cost || 0;
        break;

      case 'buy_x_get_y':
        discountAmount = this.calculateBuyXGetYDiscount(coupon, orderData);
        break;

      default:
        discountAmount = 0;
    }

    return {
      discount_amount: Math.round(discountAmount * 100) / 100,
      applicable_amount: applicableAmount,
      discount_type: coupon.type,
      discount_value: coupon.value
    };
  }

  /**
   * Calculate Buy X Get Y discount
   * @param {Object} coupon - Coupon object
   * @param {Object} orderData - Order data
   * @returns {number} Discount amount
   */
  calculateBuyXGetYDiscount(coupon, orderData) {
    // Simplified implementation - can be enhanced for specific product combinations
    const totalQuantity = orderData.items.reduce((sum, item) => sum + item.quantity, 0);
    const eligibleSets = Math.floor(totalQuantity / coupon.buy_quantity);
    
    if (eligibleSets === 0) return 0;

    // Find cheapest items to apply discount to
    const sortedItems = orderData.items
      .flatMap(item => Array(item.quantity).fill(item.price))
      .sort((a, b) => a - b);

    const discountItems = sortedItems.slice(0, eligibleSets * coupon.get_quantity);
    const discountAmount = discountItems.reduce((sum, price) => 
      sum + (price * (coupon.get_discount_percentage || 100) / 100), 0
    );

    return discountAmount;
  }

  /**
   * Record coupon usage
   * @param {Object} usageData - Usage data
   * @returns {Object} Usage record
   */
  async recordCouponUsage(usageData) {
    try {
      const {
        couponId,
        userId,
        orderId,
        discountAmount,
        originalAmount,
        finalAmount,
        currency,
        metadata = {}
      } = usageData;

      // Create usage record
      const usage = await CouponUsage.create({
        coupon_id: couponId,
        user_id: userId,
        order_id: orderId,
        discount_amount: discountAmount,
        original_amount: originalAmount,
        final_amount: finalAmount,
        currency,
        metadata
      });

      // Update coupon used count
      await Coupon.increment('used_count', {
        where: { id: couponId }
      });

      // Check if usage limit reached and update status
      const coupon = await Coupon.findByPk(couponId);
      if (coupon.usage_limit && coupon.used_count >= coupon.usage_limit) {
        await coupon.update({ status: 'used_up' });
      }

      return {
        success: true,
        usage
      };
    } catch (error) {
      console.error('Record coupon usage error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get coupon by code
   * @param {string} code - Coupon code
   * @returns {Object} Coupon object
   */
  async getCouponByCode(code) {
    return await Coupon.findOne({
      where: { code: code.toUpperCase() },
      include: [
        {
          model: CouponProduct,
          as: 'products',
          include: [{ model: Product, as: 'product' }]
        },
        {
          model: CouponCategory,
          as: 'categories',
          include: [{ model: Category, as: 'category' }]
        },
        {
          model: CouponUser,
          as: 'users',
          include: [{ model: User, as: 'user' }]
        }
      ]
    });
  }

  /**
   * Get coupon by ID
   * @param {string} id - Coupon ID
   * @returns {Object} Coupon object
   */
  async getCouponById(id) {
    return await Coupon.findByPk(id, {
      include: [
        {
          model: CouponProduct,
          as: 'products',
          include: [{ model: Product, as: 'product' }]
        },
        {
          model: CouponCategory,
          as: 'categories',
          include: [{ model: Category, as: 'category' }]
        },
        {
          model: CouponUser,
          as: 'users',
          include: [{ model: User, as: 'user' }]
        }
      ]
    });
  }

  /**
   * Get available coupons for user
   * @param {string} userId - User ID
   * @returns {Array} Available coupons
   */
  async getAvailableCouponsForUser(userId) {
    const now = new Date();
    
    return await Coupon.findAll({
      where: {
        status: 'active',
        start_date: { [Op.lte]: now },
        [Op.or]: [
          { end_date: null },
          { end_date: { [Op.gte]: now } }
        ],
        [Op.or]: [
          { usage_limit: null },
          { used_count: { [Op.lt]: sequelize.col('usage_limit') } }
        ]
      },
      include: [
        {
          model: CouponUser,
          as: 'users',
          where: { user_id: userId },
          required: false
        }
      ]
    });
  }
}

module.exports = new CouponService();
