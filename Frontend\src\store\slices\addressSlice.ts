import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Address {
  id: string;
  userId: string;
  type: 'home' | 'work' | 'other';
  name: string;
  phone: string;
  country: string;
  state: string;
  city: string;
  address: string;
  postalCode: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AddressState {
  addresses: Address[];
  loading: boolean;
  error: string | null;
  selectedAddress: Address | null;
}

const initialState: AddressState = {
  addresses: [],
  loading: false,
  error: null,
  selectedAddress: null,
};

// Async thunks
export const fetchAddresses = createAsyncThunk(
  'addresses/fetchAddresses',
  async () => {
    const response = await fetch('/api/v1/addresses');
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.addresses;
  }
);

export const fetchAddressById = createAsyncThunk(
  'addresses/fetchAddressById',
  async (addressId: string) => {
    const response = await fetch(`/api/v1/addresses/${addressId}`);
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.address;
  }
);

export const createAddress = createAsyncThunk(
  'addresses/createAddress',
  async (addressData: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    const response = await fetch('/api/v1/addresses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(addressData),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.address;
  }
);

export const updateAddress = createAsyncThunk(
  'addresses/updateAddress',
  async ({ 
    addressId, 
    addressData 
  }: { 
    addressId: string; 
    addressData: Partial<Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>; 
  }) => {
    const response = await fetch(`/api/v1/addresses/${addressId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(addressData),
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.address;
  }
);

export const deleteAddress = createAsyncThunk(
  'addresses/deleteAddress',
  async (addressId: string) => {
    const response = await fetch(`/api/v1/addresses/${addressId}`, {
      method: 'DELETE',
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return addressId;
  }
);

export const setDefaultAddress = createAsyncThunk(
  'addresses/setDefaultAddress',
  async (addressId: string) => {
    const response = await fetch(`/api/v1/addresses/${addressId}/default`, {
      method: 'PUT',
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message);
    }
    
    return data.data.address;
  }
);

const addressSlice = createSlice({
  name: 'addresses',
  initialState,
  reducers: {
    setSelectedAddress: (state, action: PayloadAction<Address | null>) => {
      state.selectedAddress = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearAddresses: (state) => {
      state.addresses = [];
      state.selectedAddress = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch addresses
      .addCase(fetchAddresses.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAddresses.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses = action.payload;
        // Set default address as selected if none is selected
        if (!state.selectedAddress) {
          const defaultAddress = action.payload.find((addr: Address) => addr.isDefault);
          if (defaultAddress) {
            state.selectedAddress = defaultAddress;
          }
        }
      })
      .addCase(fetchAddresses.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch addresses';
      })
      // Fetch address by ID
      .addCase(fetchAddressById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAddressById.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.addresses.findIndex(addr => addr.id === action.payload.id);
        if (index !== -1) {
          state.addresses[index] = action.payload;
        } else {
          state.addresses.push(action.payload);
        }
      })
      .addCase(fetchAddressById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch address';
      })
      // Create address
      .addCase(createAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAddress.fulfilled, (state, action) => {
        state.loading = false;
        state.addresses.push(action.payload);
        // If this is the first address or set as default, select it
        if (action.payload.isDefault || state.addresses.length === 1) {
          state.selectedAddress = action.payload;
          // Update other addresses to not be default
          state.addresses.forEach(addr => {
            if (addr.id !== action.payload.id) {
              addr.isDefault = false;
            }
          });
        }
      })
      .addCase(createAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create address';
      })
      // Update address
      .addCase(updateAddress.fulfilled, (state, action) => {
        const index = state.addresses.findIndex(addr => addr.id === action.payload.id);
        if (index !== -1) {
          state.addresses[index] = action.payload;
          // If this address is set as default, update other addresses
          if (action.payload.isDefault) {
            state.addresses.forEach(addr => {
              if (addr.id !== action.payload.id) {
                addr.isDefault = false;
              }
            });
            state.selectedAddress = action.payload;
          }
        }
      })
      // Delete address
      .addCase(deleteAddress.fulfilled, (state, action) => {
        state.addresses = state.addresses.filter(addr => addr.id !== action.payload);
        // If deleted address was selected, select default or first address
        if (state.selectedAddress?.id === action.payload) {
          const defaultAddress = state.addresses.find(addr => addr.isDefault);
          state.selectedAddress = defaultAddress || state.addresses[0] || null;
        }
      })
      // Set default address
      .addCase(setDefaultAddress.fulfilled, (state, action) => {
        // Update all addresses
        state.addresses.forEach(addr => {
          addr.isDefault = addr.id === action.payload.id;
        });
        state.selectedAddress = action.payload;
      });
  },
});

export const {
  setSelectedAddress,
  clearError,
  clearAddresses,
} = addressSlice.actions;

export default addressSlice.reducer;
