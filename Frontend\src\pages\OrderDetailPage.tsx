import React from 'react';
import { <PERSON>, Typo<PERSON>, Button, Space } from 'antd';

const { Title } = Typography;

const OrderDetailPage: React.FC = () => {
  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <Title level={2}>Order Details</Title>
        <Card>
          <div style={{ textAlign: 'center', padding: '48px' }}>
            <Title level={4}>Order Details Feature Coming Soon</Title>
            <Space>
              <Button type="primary">Track Order</Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OrderDetailPage;
