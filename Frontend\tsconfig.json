{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@pages/*": ["pages/*"], "@services/*": ["services/*"], "@store/*": ["store/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"]}, "typeRoots": ["node_modules/@types"], "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts"]}