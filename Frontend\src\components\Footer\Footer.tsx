import React from 'react';
import { Layout, Row, Col, Space, Typography, Divider } from 'antd';
import {
  FacebookOutlined,
  TwitterOutlined,
  InstagramOutlined,
  LinkedinOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons';

const { Footer: AntFooter } = Layout;
const { Title, Text, Link } = Typography;

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <AntFooter style={{ background: '#001529', color: '#fff', padding: '48px 24px 24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Row gutter={[32, 32]}>
          {/* Company Info */}
          <Col xs={24} sm={12} md={6}>
            <Title level={4} style={{ color: '#fff', marginBottom: '16px' }}>
              CrossBorder Store
            </Title>
            <Text style={{ color: '#bfbfbf', display: 'block', marginBottom: '16px' }}>
              Your trusted partner for cross-border e-commerce. Connecting global buyers with quality products worldwide.
            </Text>
            <Space direction="vertical" size="small">
              <Space>
                <EnvironmentOutlined style={{ color: '#1890ff' }} />
                <Text style={{ color: '#bfbfbf' }}>123 Global Trade Center, International District</Text>
              </Space>
              <Space>
                <PhoneOutlined style={{ color: '#1890ff' }} />
                <Text style={{ color: '#bfbfbf' }}>+****************</Text>
              </Space>
              <Space>
                <MailOutlined style={{ color: '#1890ff' }} />
                <Text style={{ color: '#bfbfbf' }}><EMAIL></Text>
              </Space>
            </Space>
          </Col>

          {/* Quick Links */}
          <Col xs={24} sm={12} md={6}>
            <Title level={5} style={{ color: '#fff', marginBottom: '16px' }}>
              Quick Links
            </Title>
            <Space direction="vertical" size="small">
              <Link href="/" style={{ color: '#bfbfbf' }}>Home</Link>
              <Link href="/products" style={{ color: '#bfbfbf' }}>Products</Link>
              <Link href="/deals" style={{ color: '#bfbfbf' }}>Deals</Link>
              <Link href="/about" style={{ color: '#bfbfbf' }}>About Us</Link>
              <Link href="/contact" style={{ color: '#bfbfbf' }}>Contact</Link>
              <Link href="/blog" style={{ color: '#bfbfbf' }}>Blog</Link>
            </Space>
          </Col>

          {/* Customer Service */}
          <Col xs={24} sm={12} md={6}>
            <Title level={5} style={{ color: '#fff', marginBottom: '16px' }}>
              Customer Service
            </Title>
            <Space direction="vertical" size="small">
              <Link href="/help" style={{ color: '#bfbfbf' }}>Help Center</Link>
              <Link href="/shipping" style={{ color: '#bfbfbf' }}>Shipping Info</Link>
              <Link href="/returns" style={{ color: '#bfbfbf' }}>Returns & Exchanges</Link>
              <Link href="/tracking" style={{ color: '#bfbfbf' }}>Order Tracking</Link>
              <Link href="/faq" style={{ color: '#bfbfbf' }}>FAQ</Link>
              <Link href="/support" style={{ color: '#bfbfbf' }}>Live Support</Link>
            </Space>
          </Col>

          {/* Legal & Policies */}
          <Col xs={24} sm={12} md={6}>
            <Title level={5} style={{ color: '#fff', marginBottom: '16px' }}>
              Legal & Policies
            </Title>
            <Space direction="vertical" size="small">
              <Link href="/privacy" style={{ color: '#bfbfbf' }}>Privacy Policy</Link>
              <Link href="/terms" style={{ color: '#bfbfbf' }}>Terms of Service</Link>
              <Link href="/cookies" style={{ color: '#bfbfbf' }}>Cookie Policy</Link>
              <Link href="/security" style={{ color: '#bfbfbf' }}>Security</Link>
              <Link href="/compliance" style={{ color: '#bfbfbf' }}>Compliance</Link>
              <Link href="/accessibility" style={{ color: '#bfbfbf' }}>Accessibility</Link>
            </Space>
          </Col>
        </Row>

        <Divider style={{ borderColor: '#434343', margin: '32px 0 24px' }} />

        {/* Bottom Section */}
        <Row justify="space-between" align="middle">
          <Col xs={24} md={12}>
            <Text style={{ color: '#bfbfbf' }}>
              © {currentYear} CrossBorder Store. All rights reserved.
            </Text>
          </Col>
          <Col xs={24} md={12} style={{ textAlign: 'right' }}>
            <Space size="large">
              <Text style={{ color: '#bfbfbf' }}>Follow us:</Text>
              <Space>
                <Link href="https://facebook.com" target="_blank" style={{ color: '#bfbfbf' }}>
                  <FacebookOutlined style={{ fontSize: '20px' }} />
                </Link>
                <Link href="https://twitter.com" target="_blank" style={{ color: '#bfbfbf' }}>
                  <TwitterOutlined style={{ fontSize: '20px' }} />
                </Link>
                <Link href="https://instagram.com" target="_blank" style={{ color: '#bfbfbf' }}>
                  <InstagramOutlined style={{ fontSize: '20px' }} />
                </Link>
                <Link href="https://linkedin.com" target="_blank" style={{ color: '#bfbfbf' }}>
                  <LinkedinOutlined style={{ fontSize: '20px' }} />
                </Link>
              </Space>
            </Space>
          </Col>
        </Row>

        {/* Payment Methods & Certifications */}
        <Row justify="center" style={{ marginTop: '24px' }}>
          <Col>
            <Space size="large" wrap>
              <Text style={{ color: '#8c8c8c', fontSize: '12px' }}>
                Secure payments powered by Stripe & PayPal
              </Text>
              <Text style={{ color: '#8c8c8c', fontSize: '12px' }}>
                SSL Encrypted
              </Text>
              <Text style={{ color: '#8c8c8c', fontSize: '12px' }}>
                PCI DSS Compliant
              </Text>
            </Space>
          </Col>
        </Row>
      </div>
    </AntFooter>
  );
};

export default Footer;
