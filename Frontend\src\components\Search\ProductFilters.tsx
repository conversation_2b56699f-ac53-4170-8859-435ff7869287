import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Checkbox,
  Slider,
  Rate,
  Button,
  Space,
  Divider,
  Typography,
  Collapse,
  Tag,
  InputNumber,
  Row,
  Col,
} from 'antd';
import { ClearOutlined, FilterOutlined } from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { setFilters } from '../../store/slices/productSlice';
import { FilterParams } from '../../types';

const { Title, Text } = Typography;
const { Panel } = Collapse;

interface ProductFiltersProps {
  className?: string;
  onFilterChange?: (filters: FilterParams) => void;
}

interface FilterState extends FilterParams {
  priceRange: [number, number];
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  className,
  onFilterChange
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();
  const { filters } = useSelector((state: RootState) => state.products);

  const [localFilters, setLocalFilters] = useState<FilterState>({
    category: '',
    brand: '',
    minPrice: undefined,
    maxPrice: undefined,
    rating: undefined,
    inStock: undefined,
    featured: undefined,
    priceRange: [0, 1000],
  });

  // Initialize filters from URL params
  useEffect(() => {
    const category = searchParams.get('category') || '';
    const brand = searchParams.get('brand') || '';
    const minPrice = searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined;
    const maxPrice = searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined;
    const rating = searchParams.get('rating') ? Number(searchParams.get('rating')) : undefined;
    const inStock = searchParams.get('inStock') ? searchParams.get('inStock') === 'true' : undefined;
    const featured = searchParams.get('featured') ? searchParams.get('featured') === 'true' : undefined;

    setLocalFilters({
      category,
      brand,
      minPrice,
      maxPrice,
      rating,
      inStock,
      featured,
      priceRange: [minPrice || 0, maxPrice || 1000],
    });
  }, [searchParams]);

  // Mock data - replace with actual API calls
  const categories = [
    { value: 'electronics', label: 'Electronics', count: 156 },
    { value: 'fashion', label: 'Fashion', count: 89 },
    { value: 'home', label: 'Home & Garden', count: 67 },
    { value: 'sports', label: 'Sports & Outdoors', count: 45 },
    { value: 'beauty', label: 'Beauty & Personal Care', count: 34 },
    { value: 'books', label: 'Books', count: 78 },
    { value: 'toys', label: 'Toys & Games', count: 23 },
  ];

  const brands = [
    { value: 'apple', label: 'Apple', count: 23 },
    { value: 'samsung', label: 'Samsung', count: 18 },
    { value: 'nike', label: 'Nike', count: 15 },
    { value: 'adidas', label: 'Adidas', count: 12 },
    { value: 'sony', label: 'Sony', count: 10 },
    { value: 'lg', label: 'LG', count: 8 },
    { value: 'hp', label: 'HP', count: 7 },
  ];

  // Update URL and dispatch filters
  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...localFilters, ...newFilters };
    setLocalFilters(updatedFilters);

    // Update URL params
    const params = new URLSearchParams(searchParams);
    
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (key === 'priceRange') return; // Handle separately
      
      if (value !== undefined && value !== '' && value !== null) {
        params.set(key, String(value));
      } else {
        params.delete(key);
      }
    });

    // Handle price range
    if (updatedFilters.priceRange[0] > 0) {
      params.set('minPrice', String(updatedFilters.priceRange[0]));
    } else {
      params.delete('minPrice');
    }

    if (updatedFilters.priceRange[1] < 1000) {
      params.set('maxPrice', String(updatedFilters.priceRange[1]));
    } else {
      params.delete('maxPrice');
    }

    params.delete('page'); // Reset to first page
    navigate(`/products?${params.toString()}`);

    // Dispatch to Redux
    const reduxFilters: FilterParams = {
      category: updatedFilters.category || undefined,
      brand: updatedFilters.brand || undefined,
      minPrice: updatedFilters.priceRange[0] > 0 ? updatedFilters.priceRange[0] : undefined,
      maxPrice: updatedFilters.priceRange[1] < 1000 ? updatedFilters.priceRange[1] : undefined,
      rating: updatedFilters.rating,
      inStock: updatedFilters.inStock,
      featured: updatedFilters.featured,
    };

    dispatch(setFilters(reduxFilters));

    // Call custom callback
    if (onFilterChange) {
      onFilterChange(reduxFilters);
    }
  };

  // Handle category change
  const handleCategoryChange = (checkedValues: string[]) => {
    updateFilters({ category: checkedValues[0] || '' });
  };

  // Handle brand change
  const handleBrandChange = (checkedValues: string[]) => {
    updateFilters({ brand: checkedValues[0] || '' });
  };

  // Handle price range change
  const handlePriceRangeChange = (value: [number, number]) => {
    updateFilters({ 
      priceRange: value,
      minPrice: value[0],
      maxPrice: value[1]
    });
  };

  // Handle rating change
  const handleRatingChange = (value: number) => {
    updateFilters({ rating: value });
  };

  // Handle availability change
  const handleAvailabilityChange = (checked: boolean) => {
    updateFilters({ inStock: checked ? true : undefined });
  };

  // Handle featured change
  const handleFeaturedChange = (checked: boolean) => {
    updateFilters({ featured: checked ? true : undefined });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setLocalFilters({
      category: '',
      brand: '',
      minPrice: undefined,
      maxPrice: undefined,
      rating: undefined,
      inStock: undefined,
      featured: undefined,
      priceRange: [0, 1000],
    });

    navigate('/products');
    dispatch(setFilters({}));

    if (onFilterChange) {
      onFilterChange({});
    }
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (localFilters.category) count++;
    if (localFilters.brand) count++;
    if (localFilters.minPrice && localFilters.minPrice > 0) count++;
    if (localFilters.maxPrice && localFilters.maxPrice < 1000) count++;
    if (localFilters.rating) count++;
    if (localFilters.inStock) count++;
    if (localFilters.featured) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card 
      className={`product-filters ${className || ''}`}
      title={
        <Space>
          <FilterOutlined />
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <Tag color="blue">{activeFilterCount}</Tag>
          )}
        </Space>
      }
      extra={
        activeFilterCount > 0 && (
          <Button 
            type="link" 
            size="small" 
            icon={<ClearOutlined />}
            onClick={clearAllFilters}
          >
            Clear All
          </Button>
        )
      }
    >
      <Collapse defaultActiveKey={['category', 'price']} ghost>
        {/* Category Filter */}
        <Panel header="Category" key="category">
          <Checkbox.Group
            value={localFilters.category ? [localFilters.category] : []}
            onChange={handleCategoryChange}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {categories.map(category => (
                <Checkbox key={category.value} value={category.value}>
                  <Space>
                    <span>{category.label}</span>
                    <Text type="secondary">({category.count})</Text>
                  </Space>
                </Checkbox>
              ))}
            </Space>
          </Checkbox.Group>
        </Panel>

        {/* Price Range Filter */}
        <Panel header="Price Range" key="price">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Slider
              range
              min={0}
              max={1000}
              value={localFilters.priceRange}
              onChange={handlePriceRangeChange}
              tooltip={{ formatter: (value) => `$${value}` }}
            />
            <Row gutter={8}>
              <Col span={12}>
                <InputNumber
                  size="small"
                  min={0}
                  max={1000}
                  value={localFilters.priceRange[0]}
                  onChange={(value) => handlePriceRangeChange([value || 0, localFilters.priceRange[1]])}
                  prefix="$"
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={12}>
                <InputNumber
                  size="small"
                  min={0}
                  max={1000}
                  value={localFilters.priceRange[1]}
                  onChange={(value) => handlePriceRangeChange([localFilters.priceRange[0], value || 1000])}
                  prefix="$"
                  style={{ width: '100%' }}
                />
              </Col>
            </Row>
          </Space>
        </Panel>

        {/* Brand Filter */}
        <Panel header="Brand" key="brand">
          <Checkbox.Group
            value={localFilters.brand ? [localFilters.brand] : []}
            onChange={handleBrandChange}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {brands.map(brand => (
                <Checkbox key={brand.value} value={brand.value}>
                  <Space>
                    <span>{brand.label}</span>
                    <Text type="secondary">({brand.count})</Text>
                  </Space>
                </Checkbox>
              ))}
            </Space>
          </Checkbox.Group>
        </Panel>

        {/* Rating Filter */}
        <Panel header="Customer Rating" key="rating">
          <Space direction="vertical">
            {[5, 4, 3, 2, 1].map(rating => (
              <div
                key={rating}
                style={{ cursor: 'pointer' }}
                onClick={() => handleRatingChange(rating)}
              >
                <Space>
                  <Rate disabled value={rating} style={{ fontSize: '14px' }} />
                  <Text>& Up</Text>
                  {localFilters.rating === rating && <Tag color="blue">Selected</Tag>}
                </Space>
              </div>
            ))}
          </Space>
        </Panel>

        {/* Availability Filter */}
        <Panel header="Availability" key="availability">
          <Space direction="vertical">
            <Checkbox
              checked={localFilters.inStock === true}
              onChange={(e) => handleAvailabilityChange(e.target.checked)}
            >
              In Stock Only
            </Checkbox>
            <Checkbox
              checked={localFilters.featured === true}
              onChange={(e) => handleFeaturedChange(e.target.checked)}
            >
              Featured Products
            </Checkbox>
          </Space>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default ProductFilters;
