import { api, ApiResponse } from './api';
import { CartItem } from '../types';

export interface AddToCartData {
  productId: string;
  quantity: number;
  variant?: Record<string, string>;
}

export interface UpdateCartItemData {
  quantity: number;
  variant?: Record<string, string>;
}

export interface CartSummary {
  items: CartItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  itemCount: number;
  currency: string;
}

export interface ApplyCouponData {
  code: string;
}

export interface CouponInfo {
  code: string;
  discount: number;
  discountType: 'percentage' | 'fixed';
  description: string;
}

export const cartService = {
  // Get cart contents
  getCart: async (): Promise<ApiResponse<CartSummary>> => {
    return api.get<CartSummary>('/cart');
  },

  // Add item to cart
  addToCart: async (data: AddToCartData): Promise<ApiResponse<CartSummary>> => {
    return api.post<CartSummary>('/cart/items', data);
  },

  // Update cart item
  updateCartItem: async (itemId: string, data: UpdateCartItemData): Promise<ApiResponse<CartSummary>> => {
    return api.put<CartSummary>(`/cart/items/${itemId}`, data);
  },

  // Remove item from cart
  removeFromCart: async (itemId: string): Promise<ApiResponse<CartSummary>> => {
    return api.delete<CartSummary>(`/cart/items/${itemId}`);
  },

  // Clear entire cart
  clearCart: async (): Promise<ApiResponse<void>> => {
    return api.delete<void>('/cart');
  },

  // Get cart item count
  getCartCount: async (): Promise<ApiResponse<{ count: number }>> => {
    return api.get<{ count: number }>('/cart/count');
  },

  // Apply coupon/discount code
  applyCoupon: async (data: ApplyCouponData): Promise<ApiResponse<CartSummary & { coupon: CouponInfo }>> => {
    return api.post<CartSummary & { coupon: CouponInfo }>('/cart/coupon', data);
  },

  // Remove coupon
  removeCoupon: async (): Promise<ApiResponse<CartSummary>> => {
    return api.delete<CartSummary>('/cart/coupon');
  },

  // Validate cart (check availability, prices, etc.)
  validateCart: async (): Promise<ApiResponse<{
    valid: boolean;
    issues: Array<{
      itemId: string;
      issue: 'out_of_stock' | 'price_changed' | 'unavailable';
      message: string;
    }>;
    cart: CartSummary;
  }>> => {
    return api.post<{
      valid: boolean;
      issues: Array<{
        itemId: string;
        issue: 'out_of_stock' | 'price_changed' | 'unavailable';
        message: string;
      }>;
      cart: CartSummary;
    }>('/cart/validate');
  },

  // Calculate shipping costs
  calculateShipping: async (addressId?: string): Promise<ApiResponse<{
    options: Array<{
      id: string;
      name: string;
      cost: number;
      estimatedDays: number;
      carrier: string;
    }>;
  }>> => {
    const url = addressId ? `/cart/shipping?addressId=${addressId}` : '/cart/shipping';
    return api.get<{
      options: Array<{
        id: string;
        name: string;
        cost: number;
        estimatedDays: number;
        carrier: string;
      }>;
    }>(url);
  },

  // Calculate taxes
  calculateTax: async (addressId: string): Promise<ApiResponse<{
    tax: number;
    taxRate: number;
    breakdown: Array<{
      name: string;
      rate: number;
      amount: number;
    }>;
  }>> => {
    return api.get<{
      tax: number;
      taxRate: number;
      breakdown: Array<{
        name: string;
        rate: number;
        amount: number;
      }>;
    }>(`/cart/tax?addressId=${addressId}`);
  },

  // Save cart for later (wishlist-like functionality)
  saveForLater: async (itemId: string): Promise<ApiResponse<CartSummary>> => {
    return api.post<CartSummary>(`/cart/items/${itemId}/save-for-later`);
  },

  // Move item from saved to cart
  moveToCart: async (itemId: string): Promise<ApiResponse<CartSummary>> => {
    return api.post<CartSummary>(`/cart/items/${itemId}/move-to-cart`);
  },

  // Get saved items
  getSavedItems: async (): Promise<ApiResponse<CartItem[]>> => {
    return api.get<CartItem[]>('/cart/saved-items');
  },

  // Merge guest cart with user cart (after login)
  mergeCart: async (guestCartItems: CartItem[]): Promise<ApiResponse<CartSummary>> => {
    return api.post<CartSummary>('/cart/merge', { items: guestCartItems });
  },

  // Estimate delivery date
  estimateDelivery: async (addressId: string, shippingMethodId: string): Promise<ApiResponse<{
    estimatedDate: string;
    minDays: number;
    maxDays: number;
  }>> => {
    return api.get<{
      estimatedDate: string;
      minDays: number;
      maxDays: number;
    }>(`/cart/delivery-estimate?addressId=${addressId}&shippingMethodId=${shippingMethodId}`);
  },
};

export default cartService;
