# Development Environment Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_API_TIMEOUT=30000

# App Configuration
REACT_APP_NAME=Cross-Border E-commerce Platform (Dev)
REACT_APP_VERSION=1.0.0-dev
REACT_APP_ENVIRONMENT=development

# Development Features
REACT_APP_DEBUG=true
REACT_APP_MOCK_API=false
GENERATE_SOURCEMAP=true

# Default Settings
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_DEFAULT_LANGUAGE=en

# Feature Flags for Development
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_CHAT_SUPPORT=false
REACT_APP_ENABLE_DARK_MODE=true
