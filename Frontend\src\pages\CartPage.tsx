import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Typography,
  Space,
  Row,
  Col,
  InputNumber,
  Empty,
  Divider,
  Image,
} from 'antd';
import { DeleteOutlined, ShoppingOutlined } from '@ant-design/icons';
import { RootState, AppDispatch } from '../store';
import { removeFromCart, updateQuantity } from '../store/slices/cartSlice';

const { Title, Text } = Typography;

const CartPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { items, totalAmount, shippingCost, currency } = useSelector((state: RootState) => state.cart);

  const handleQuantityChange = (id: string, variant: any, quantity: number) => {
    dispatch(updateQuantity({ id, variant, quantity }));
  };

  const handleRemoveItem = (id: string, variant: any) => {
    dispatch(removeFromCart({ id, variant }));
  };

  if (items.length === 0) {
    return (
      <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '48px' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="Your cart is empty"
            >
              <Link to="/products">
                <Button type="primary" icon={<ShoppingOutlined />}>
                  Start Shopping
                </Button>
              </Link>
            </Empty>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Shopping Cart</Title>
        
        <Row gutter={24}>
          <Col xs={24} lg={16}>
            <Card title="Cart Items">
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {items.map((item, index) => (
                  <div key={`${item.id}-${JSON.stringify(item.variant)}`}>
                    <Row gutter={16} align="middle">
                      <Col xs={6} sm={4}>
                        <Image
                          src={item.image}
                          alt={item.name}
                          width={80}
                          height={80}
                          style={{ objectFit: 'cover' }}
                        />
                      </Col>
                      <Col xs={18} sm={20}>
                        <Row justify="space-between" align="middle">
                          <Col xs={24} sm={12}>
                            <Link to={`/products/${item.id}`}>
                              <Text strong>{item.name}</Text>
                            </Link>
                            {item.variant && (
                              <div>
                                {Object.entries(item.variant).map(([key, value]) => (
                                  <Text type="secondary" key={key} style={{ display: 'block' }}>
                                    {key}: {value}
                                  </Text>
                                ))}
                              </div>
                            )}
                          </Col>
                          <Col xs={24} sm={12}>
                            <Row justify="space-between" align="middle">
                              <Col>
                                <Space>
                                  <InputNumber
                                    min={1}
                                    value={item.quantity}
                                    onChange={(value) => handleQuantityChange(item.id, item.variant, value || 1)}
                                    size="small"
                                  />
                                  <Text strong>${(item.price * item.quantity).toFixed(2)}</Text>
                                </Space>
                              </Col>
                              <Col>
                                <Button
                                  type="text"
                                  danger
                                  icon={<DeleteOutlined />}
                                  onClick={() => handleRemoveItem(item.id, item.variant)}
                                />
                              </Col>
                            </Row>
                          </Col>
                        </Row>
                      </Col>
                    </Row>
                    {index < items.length - 1 && <Divider />}
                  </div>
                ))}
              </Space>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="Order Summary">
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Row justify="space-between">
                  <Text>Subtotal:</Text>
                  <Text>${totalAmount.toFixed(2)}</Text>
                </Row>
                <Row justify="space-between">
                  <Text>Shipping:</Text>
                  <Text>${shippingCost.toFixed(2)}</Text>
                </Row>
                <Divider />
                <Row justify="space-between">
                  <Text strong>Total:</Text>
                  <Text strong style={{ fontSize: '18px' }}>
                    ${(totalAmount + shippingCost).toFixed(2)} {currency}
                  </Text>
                </Row>
                <Button
                  type="primary"
                  size="large"
                  block
                  onClick={() => navigate('/checkout')}
                >
                  Proceed to Checkout
                </Button>
                <Link to="/products">
                  <Button block>Continue Shopping</Button>
                </Link>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default CartPage;
