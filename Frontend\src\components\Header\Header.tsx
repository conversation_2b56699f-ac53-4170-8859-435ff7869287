import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Layout,
  Menu,
  Input,
  Badge,
  Avatar,
  Dropdown,
  Button,
  Space,
  Select,
  Drawer,
} from 'antd';
import {
  SearchOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  HeartOutlined,
  MenuOutlined,
  GlobalOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { RootState, AppDispatch } from '../../store';
import { toggleCartDrawer, setLanguage, setCurrency } from '../../store/slices/uiSlice';
import { logoutUser } from '../../store/slices/authSlice';
import SearchBar from '../Search/SearchBar';

const { Header: AntHeader } = Layout;
const { Search } = Input;
const { Option } = Select;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const { totalItems } = useSelector((state: RootState) => state.cart);
  const { language, currency } = useSelector((state: RootState) => state.ui);

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/products?search=${encodeURIComponent(value)}`);
    }
  };

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/');
  };

  const handleLanguageChange = (lang: string) => {
    dispatch(setLanguage(lang));
  };

  const handleCurrencyChange = (curr: string) => {
    dispatch(setCurrency(curr));
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'orders',
      icon: <SettingOutlined />,
      label: 'My Orders',
      onClick: () => navigate('/orders'),
    },
    {
      key: 'wishlist',
      icon: <HeartOutlined />,
      label: 'Wishlist',
      onClick: () => navigate('/wishlist'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  const mainMenuItems = [
    {
      key: 'home',
      label: <Link to="/">Home</Link>,
    },
    {
      key: 'products',
      label: <Link to="/products">Products</Link>,
    },
    {
      key: 'categories',
      label: 'Categories',
      children: [
        { key: 'electronics', label: <Link to="/products?category=electronics">Electronics</Link> },
        { key: 'fashion', label: <Link to="/products?category=fashion">Fashion</Link> },
        { key: 'home', label: <Link to="/products?category=home">Home & Garden</Link> },
        { key: 'sports', label: <Link to="/products?category=sports">Sports</Link> },
      ],
    },
    {
      key: 'deals',
      label: <Link to="/deals">Deals</Link>,
    },
  ];

  return (
    <AntHeader
      className="header"
      style={{
        background: 'linear-gradient(135deg, #fff 0%, #f8f9fa 100%)',
        padding: '0 24px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        borderBottom: '1px solid rgba(0,0,0,0.06)',
        backdropFilter: 'blur(10px)',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', height: '100%' }}>
        {/* Logo */}
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Link to="/" style={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}>
            <div style={{
              fontSize: '28px',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginRight: '32px',
              letterSpacing: '-0.5px'
            }}>
              🌍 CrossBorder
            </div>
          </Link>

          {/* Desktop Menu */}
          <Menu
            mode="horizontal"
            items={mainMenuItems}
            style={{ border: 'none', minWidth: '400px' }}
            className="desktop-menu"
          />
        </div>

        {/* Search Bar */}
        <div style={{ flex: 1, maxWidth: '500px', margin: '0 32px' }}>
          <SearchBar
            placeholder="Search for products, brands, categories..."
            showFilters={false}
            showSort={false}
            onSearch={handleSearch}
            className="header-search"
          />
        </div>

        {/* Right Side Actions */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Language & Currency Selector */}
          <Space>
            <Select
              value={language}
              onChange={handleLanguageChange}
              style={{ width: 80 }}
              size="small"
              suffixIcon={<GlobalOutlined />}
            >
              <Option value="en">EN</Option>
              <Option value="zh">中文</Option>
              <Option value="es">ES</Option>
              <Option value="fr">FR</Option>
            </Select>

            <Select
              value={currency}
              onChange={handleCurrencyChange}
              style={{ width: 80 }}
              size="small"
            >
              <Option value="USD">USD</Option>
              <Option value="EUR">EUR</Option>
              <Option value="CNY">CNY</Option>
              <Option value="GBP">GBP</Option>
            </Select>
          </Space>

          {/* Cart */}
          <Badge count={totalItems} size="small">
            <Button
              type="text"
              icon={<ShoppingCartOutlined style={{ fontSize: '20px' }} />}
              onClick={() => dispatch(toggleCartDrawer())}
            />
          </Badge>

          {/* User Menu */}
          {isAuthenticated ? (
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar src={user?.avatar} icon={<UserOutlined />} />
                <span className="desktop-only">{user?.name}</span>
              </Space>
            </Dropdown>
          ) : (
            <Space>
              <Button type="text" onClick={() => navigate('/login')}>
                Login
              </Button>
              <Button type="primary" onClick={() => navigate('/register')}>
                Sign Up
              </Button>
            </Space>
          )}

          {/* Mobile Menu Button */}
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            className="mobile-only"
          />
        </div>
      </div>

      {/* Mobile Menu Drawer */}
      <Drawer
        title="Menu"
        placement="right"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        width={280}
      >
        <Menu
          mode="vertical"
          items={mainMenuItems}
          onClick={() => setMobileMenuVisible(false)}
        />
      </Drawer>


    </AntHeader>
  );
};

export default Header;
