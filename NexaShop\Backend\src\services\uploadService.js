const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 临时解决方案：检查Sharp是否可用
let sharp = null;
let sharpAvailable = false;

try {
  sharp = require('sharp');
  sharpAvailable = true;
  console.log('✅ Sharp包加载成功');
} catch (error) {
  console.log('⚠️ Sharp包不可用，使用基础文件处理功能');
  console.log('Sharp错误:', error.message);
}

class UploadService {
  constructor() {
    this.uploadsDir = path.join(__dirname, '../../uploads');
    this.tempDir = path.join(this.uploadsDir, 'temp');
    this.imagesDir = path.join(this.uploadsDir, 'images');
    this.documentsDir = path.join(this.uploadsDir, 'documents');
    
    this.initializeDirectories();
  }

  initializeDirectories() {
    const dirs = [this.uploadsDir, this.tempDir, this.imagesDir, this.documentsDir];
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  // Image processing configurations
  getImageSizes() {
    return {
      thumbnail: { width: 150, height: 150, suffix: '_thumb', quality: 80 },
      small: { width: 300, height: 300, suffix: '_small', quality: 85 },
      medium: { width: 600, height: 600, suffix: '_medium', quality: 85 },
      large: { width: 1200, height: 1200, suffix: '_large', quality: 90 },
      original: { width: null, height: null, suffix: '_original', quality: 95 }
    };
  }

  // Process single image with multiple sizes
  async processImage(inputPath, filename, options = {}) {
    try {
      const baseName = path.parse(filename).name;
      const processedImages = {};

      if (sharpAvailable) {
        // 使用Sharp进行高质量图片处理
        const sizes = this.getImageSizes();

        // Get image metadata
        const metadata = await sharp(inputPath).metadata();

        for (const [sizeName, config] of Object.entries(sizes)) {
          const outputFilename = `${baseName}${config.suffix}.jpg`;
          const outputPath = path.join(this.imagesDir, outputFilename);

          let sharpInstance = sharp(inputPath);

          // Resize if dimensions are specified
          if (config.width && config.height) {
            sharpInstance = sharpInstance.resize(config.width, config.height, {
              fit: 'inside',
              withoutEnlargement: true
            });
          }

          // Convert to JPEG with quality
          await sharpInstance
            .jpeg({ quality: config.quality })
            .toFile(outputPath);

          processedImages[sizeName] = {
            filename: outputFilename,
            path: `/api/v1/upload/image/${outputFilename}`,
            url: `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/v1/upload/image/${outputFilename}`,
            width: config.width || metadata.width,
            height: config.height || metadata.height,
            size: fs.statSync(outputPath).size
          };
        }

        return {
          success: true,
          images: processedImages,
          originalMetadata: {
            width: metadata.width,
            height: metadata.height,
            format: metadata.format,
            size: metadata.size
          }
        };

      } else {
        // 基础文件处理（Sharp不可用时的降级方案）
        console.log(`⚠️ Sharp不可用，使用基础文件处理: ${filename}`);

        const outputFilename = `${baseName}_original.jpg`;
        const outputPath = path.join(this.imagesDir, outputFilename);

        // 简单复制文件到目标位置
        fs.copyFileSync(inputPath, outputPath);
        const fileStats = fs.statSync(outputPath);

        processedImages.original = {
          filename: outputFilename,
          path: `/api/v1/upload/image/${outputFilename}`,
          url: `${process.env.BACKEND_URL || 'http://localhost:5000'}/api/v1/upload/image/${outputFilename}`,
          width: 'unknown',
          height: 'unknown',
          size: fileStats.size
        };

        return {
          success: true,
          images: processedImages,
          originalMetadata: {
            width: 'unknown',
            height: 'unknown',
            format: 'unknown',
            size: fileStats.size
          },
          warning: 'Sharp不可用，仅进行基础文件处理，无法生成多尺寸图片'
        };
      }

    } catch (error) {
      console.error('Image processing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Validate file type
  validateFileType(file, allowedTypes = []) {
    if (allowedTypes.length === 0) {
      // Default allowed image types
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    }
    
    return allowedTypes.includes(file.mimetype);
  }

  // Validate file size
  validateFileSize(file, maxSize = 10 * 1024 * 1024) { // 10MB default
    return file.size <= maxSize;
  }

  // Generate unique filename
  generateUniqueFilename(originalName) {
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    const timestamp = Date.now();
    const uuid = uuidv4().split('-')[0]; // Use first part of UUID
    return `${baseName}_${timestamp}_${uuid}${ext}`;
  }

  // Clean up temporary files
  cleanupTempFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        return true;
      }
    } catch (error) {
      console.error('Cleanup error:', error);
    }
    return false;
  }

  // Delete uploaded image and all its variants
  async deleteImage(filename) {
    try {
      const baseName = path.parse(filename).name.replace(/_thumb|_small|_medium|_large|_original$/, '');
      const sizes = this.getImageSizes();
      let deletedFiles = 0;

      for (const [sizeName, config] of Object.entries(sizes)) {
        const fileToDelete = path.join(this.imagesDir, `${baseName}${config.suffix}.jpg`);
        if (fs.existsSync(fileToDelete)) {
          fs.unlinkSync(fileToDelete);
          deletedFiles++;
        }
      }

      return {
        success: deletedFiles > 0,
        deletedFiles: deletedFiles
      };

    } catch (error) {
      console.error('Delete image error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get image file path
  getImagePath(filename) {
    return path.join(this.imagesDir, filename);
  }

  // Check if image exists
  imageExists(filename) {
    return fs.existsSync(this.getImagePath(filename));
  }

  // Get file stats
  getFileStats(filename) {
    try {
      const filePath = this.getImagePath(filename);
      if (fs.existsSync(filePath)) {
        return fs.statSync(filePath);
      }
    } catch (error) {
      console.error('Get file stats error:', error);
    }
    return null;
  }

  // Health check for upload service
  async healthCheck() {
    try {
      const checks = {
        uploadsDir: fs.existsSync(this.uploadsDir),
        tempDir: fs.existsSync(this.tempDir),
        imagesDir: fs.existsSync(this.imagesDir),
        documentsDir: fs.existsSync(this.documentsDir),
        sharpAvailable: true,
        diskSpace: true
      };

      // Test sharp functionality
      try {
        await sharp({
          create: {
            width: 100,
            height: 100,
            channels: 3,
            background: { r: 255, g: 255, b: 255 }
          }
        }).jpeg().toBuffer();
      } catch (sharpError) {
        checks.sharpAvailable = false;
      }

      // Check disk space (basic check)
      try {
        const stats = fs.statSync(this.uploadsDir);
        checks.diskSpace = true;
      } catch (diskError) {
        checks.diskSpace = false;
      }

      const allHealthy = Object.values(checks).every(check => check === true);

      return {
        success: allHealthy,
        checks: checks,
        message: allHealthy ? 'Upload service is healthy' : 'Upload service has issues'
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: 'Upload service health check failed'
      };
    }
  }

  // Get upload statistics
  getUploadStats() {
    try {
      const imageFiles = fs.readdirSync(this.imagesDir);
      const totalImages = imageFiles.length;
      
      let totalSize = 0;
      imageFiles.forEach(file => {
        const filePath = path.join(this.imagesDir, file);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
      });

      return {
        totalImages: totalImages,
        totalSize: totalSize,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
        averageFileSize: totalImages > 0 ? (totalSize / totalImages) : 0
      };

    } catch (error) {
      console.error('Get upload stats error:', error);
      return {
        totalImages: 0,
        totalSize: 0,
        totalSizeMB: '0.00',
        averageFileSize: 0
      };
    }
  }
}

module.exports = new UploadService();
