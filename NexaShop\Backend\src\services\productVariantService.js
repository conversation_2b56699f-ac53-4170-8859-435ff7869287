const {
  VariantAttribute,
  VariantAttributeValue,
  ProductVariant,
  ProductVariantAttributeValue,
  InventoryLog
} = require('../models/ProductVariant');
const { Product } = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

class ProductVariantService {
  /**
   * Create variant attributes (e.g., Color, Size)
   * @param {Object} attributeData - Attribute data
   * @returns {Object} Created attribute
   */
  async createVariantAttribute(attributeData) {
    try {
      const {
        name,
        displayName,
        type = 'text',
        isRequired = false,
        sortOrder = 0
      } = attributeData;

      const attribute = await VariantAttribute.create({
        name: name.toLowerCase().replace(/\s+/g, '_'),
        display_name: displayName,
        type,
        is_required: isRequired,
        sort_order: sortOrder
      });

      return {
        success: true,
        attribute
      };
    } catch (error) {
      console.error('Create variant attribute error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create variant attribute values (e.g., Red, Blue for Color)
   * @param {Object} valueData - Value data
   * @returns {Object} Created value
   */
  async createVariantAttributeValue(valueData) {
    try {
      const {
        attributeId,
        value,
        displayValue,
        colorCode,
        imageUrl,
        sortOrder = 0
      } = valueData;

      const attributeValue = await VariantAttributeValue.create({
        attribute_id: attributeId,
        value: value.toLowerCase().replace(/\s+/g, '_'),
        display_value: displayValue,
        color_code: colorCode,
        image_url: imageUrl,
        sort_order: sortOrder
      });

      return {
        success: true,
        attributeValue
      };
    } catch (error) {
      console.error('Create variant attribute value error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create product variant
   * @param {Object} variantData - Variant data
   * @returns {Object} Created variant
   */
  async createProductVariant(variantData) {
    const transaction = await sequelize.transaction();
    
    try {
      const {
        productId,
        sku,
        barcode,
        price,
        compareAtPrice,
        costPrice,
        weight,
        dimensions,
        inventoryQuantity = 0,
        inventoryPolicy = 'deny',
        requiresShipping = true,
        taxable = true,
        taxCode,
        position = 0,
        isDefault = false,
        imageUrls = [],
        attributes = [], // Array of {attributeId, attributeValueId}
        metadata = {}
      } = variantData;

      // Check if product exists
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      // If this is set as default, unset other defaults
      if (isDefault) {
        await ProductVariant.update(
          { is_default: false },
          { 
            where: { product_id: productId },
            transaction
          }
        );
      }

      // Create variant
      const variant = await ProductVariant.create({
        product_id: productId,
        sku,
        barcode,
        price,
        compare_at_price: compareAtPrice,
        cost_price: costPrice,
        weight,
        dimensions,
        inventory_quantity: inventoryQuantity,
        inventory_policy: inventoryPolicy,
        requires_shipping: requiresShipping,
        taxable,
        tax_code: taxCode,
        position,
        is_default: isDefault,
        image_urls: imageUrls,
        metadata
      }, { transaction });

      // Create attribute associations
      if (attributes.length > 0) {
        const attributeAssociations = attributes.map(attr => ({
          variant_id: variant.id,
          attribute_id: attr.attributeId,
          attribute_value_id: attr.attributeValueId
        }));

        await ProductVariantAttributeValue.bulkCreate(
          attributeAssociations,
          { transaction }
        );
      }

      // Log initial inventory
      if (inventoryQuantity > 0) {
        await this.logInventoryChange({
          variantId: variant.id,
          changeType: 'initial',
          quantityChange: inventoryQuantity,
          quantityBefore: 0,
          quantityAfter: inventoryQuantity,
          reason: 'Initial stock',
          transaction
        });
      }

      await transaction.commit();

      return {
        success: true,
        variant: await this.getVariantById(variant.id)
      };
    } catch (error) {
      await transaction.rollback();
      console.error('Create product variant error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update variant inventory
   * @param {Object} inventoryData - Inventory update data
   * @returns {Object} Update result
   */
  async updateVariantInventory(inventoryData) {
    const transaction = await sequelize.transaction();
    
    try {
      const {
        variantId,
        quantityChange,
        changeType,
        referenceType,
        referenceId,
        reason,
        notes,
        userId,
        location
      } = inventoryData;

      const variant = await ProductVariant.findByPk(variantId, { transaction });
      if (!variant) {
        throw new Error('Variant not found');
      }

      const quantityBefore = variant.inventory_quantity;
      const quantityAfter = quantityBefore + quantityChange;

      // Validate inventory policy
      if (quantityAfter < 0 && variant.inventory_policy === 'deny') {
        throw new Error('Insufficient inventory');
      }

      // Update variant inventory
      await variant.update(
        { inventory_quantity: quantityAfter },
        { transaction }
      );

      // Log inventory change
      await this.logInventoryChange({
        variantId,
        changeType,
        quantityChange,
        quantityBefore,
        quantityAfter,
        referenceType,
        referenceId,
        reason,
        notes,
        userId,
        location,
        transaction
      });

      await transaction.commit();

      return {
        success: true,
        variant: await this.getVariantById(variantId),
        inventoryChange: {
          before: quantityBefore,
          after: quantityAfter,
          change: quantityChange
        }
      };
    } catch (error) {
      await transaction.rollback();
      console.error('Update variant inventory error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Log inventory change
   * @param {Object} logData - Log data
   * @returns {Object} Log entry
   */
  async logInventoryChange(logData) {
    const {
      variantId,
      changeType,
      quantityChange,
      quantityBefore,
      quantityAfter,
      referenceType,
      referenceId,
      reason,
      notes,
      userId,
      location,
      metadata = {},
      transaction
    } = logData;

    return await InventoryLog.create({
      variant_id: variantId,
      change_type: changeType,
      quantity_change: quantityChange,
      quantity_before: quantityBefore,
      quantity_after: quantityAfter,
      reference_type: referenceType,
      reference_id: referenceId,
      reason,
      notes,
      user_id: userId,
      location,
      metadata
    }, { transaction });
  }

  /**
   * Get variant by ID with full details
   * @param {string} variantId - Variant ID
   * @returns {Object} Variant details
   */
  async getVariantById(variantId) {
    return await ProductVariant.findByPk(variantId, {
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name', 'description', 'status']
        },
        {
          model: ProductVariantAttributeValue,
          as: 'attributeValues',
          include: [
            {
              model: VariantAttribute,
              as: 'attribute'
            },
            {
              model: VariantAttributeValue,
              as: 'value'
            }
          ]
        }
      ]
    });
  }

  /**
   * Get variants for a product
   * @param {string} productId - Product ID
   * @returns {Array} Product variants
   */
  async getProductVariants(productId) {
    return await ProductVariant.findAll({
      where: { 
        product_id: productId,
        is_active: true
      },
      include: [
        {
          model: ProductVariantAttributeValue,
          as: 'attributeValues',
          include: [
            {
              model: VariantAttribute,
              as: 'attribute'
            },
            {
              model: VariantAttributeValue,
              as: 'value'
            }
          ]
        }
      ],
      order: [['position', 'ASC'], ['created_at', 'ASC']]
    });
  }

  /**
   * Find variant by attributes
   * @param {string} productId - Product ID
   * @param {Array} attributes - Array of {attributeId, valueId}
   * @returns {Object} Matching variant
   */
  async findVariantByAttributes(productId, attributes) {
    if (!attributes || attributes.length === 0) {
      // Return default variant if no attributes specified
      return await ProductVariant.findOne({
        where: {
          product_id: productId,
          is_default: true,
          is_active: true
        }
      });
    }

    // Find variants that match all specified attributes
    const variantIds = await ProductVariantAttributeValue.findAll({
      where: {
        attribute_id: { [Op.in]: attributes.map(a => a.attributeId) },
        attribute_value_id: { [Op.in]: attributes.map(a => a.valueId) }
      },
      attributes: ['variant_id'],
      group: ['variant_id'],
      having: sequelize.where(
        sequelize.fn('COUNT', sequelize.col('variant_id')),
        attributes.length
      )
    });

    if (variantIds.length === 0) {
      return null;
    }

    return await ProductVariant.findOne({
      where: {
        id: { [Op.in]: variantIds.map(v => v.variant_id) },
        product_id: productId,
        is_active: true
      },
      include: [
        {
          model: ProductVariantAttributeValue,
          as: 'attributeValues',
          include: [
            {
              model: VariantAttribute,
              as: 'attribute'
            },
            {
              model: VariantAttributeValue,
              as: 'value'
            }
          ]
        }
      ]
    });
  }

  /**
   * Get all variant attributes
   * @returns {Array} Variant attributes
   */
  async getAllVariantAttributes() {
    return await VariantAttribute.findAll({
      where: { is_active: true },
      include: [
        {
          model: VariantAttributeValue,
          as: 'values',
          where: { is_active: true },
          required: false
        }
      ],
      order: [
        ['sort_order', 'ASC'],
        ['display_name', 'ASC'],
        [{ model: VariantAttributeValue, as: 'values' }, 'sort_order', 'ASC']
      ]
    });
  }

  /**
   * Check variant availability
   * @param {string} variantId - Variant ID
   * @param {number} quantity - Requested quantity
   * @returns {Object} Availability check result
   */
  async checkVariantAvailability(variantId, quantity = 1) {
    try {
      const variant = await ProductVariant.findByPk(variantId);
      if (!variant) {
        return {
          available: false,
          error: 'Variant not found'
        };
      }

      if (!variant.is_active) {
        return {
          available: false,
          error: 'Variant is not active'
        };
      }

      const availableQuantity = variant.inventory_quantity;
      
      if (variant.inventory_policy === 'deny' && availableQuantity < quantity) {
        return {
          available: false,
          error: 'Insufficient inventory',
          availableQuantity
        };
      }

      return {
        available: true,
        availableQuantity,
        canBackorder: variant.inventory_policy === 'continue'
      };
    } catch (error) {
      console.error('Check variant availability error:', error);
      return {
        available: false,
        error: error.message
      };
    }
  }

  /**
   * Get inventory logs for a variant
   * @param {string} variantId - Variant ID
   * @param {Object} options - Query options
   * @returns {Object} Inventory logs with pagination
   */
  async getVariantInventoryLogs(variantId, options = {}) {
    const {
      page = 1,
      limit = 20,
      changeType,
      startDate,
      endDate
    } = options;

    const offset = (page - 1) * limit;
    const whereClause = { variant_id: variantId };

    if (changeType) {
      whereClause.change_type = changeType;
    }

    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at[Op.gte] = startDate;
      if (endDate) whereClause.created_at[Op.lte] = endDate;
    }

    const { count, rows: logs } = await InventoryLog.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    return {
      logs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    };
  }

  /**
   * Generate variant combinations
   * @param {Array} attributeOptions - Array of {attributeId, valueIds[]}
   * @returns {Array} All possible combinations
   */
  generateVariantCombinations(attributeOptions) {
    if (attributeOptions.length === 0) {
      return [[]];
    }

    const [first, ...rest] = attributeOptions;
    const restCombinations = this.generateVariantCombinations(rest);
    
    const combinations = [];
    for (const valueId of first.valueIds) {
      for (const restCombination of restCombinations) {
        combinations.push([
          { attributeId: first.attributeId, valueId },
          ...restCombination
        ]);
      }
    }

    return combinations;
  }
}

module.exports = new ProductVariantService();
