const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Basic health check
app.get('/', (req, res) => {
  res.json({ 
    success: true, 
    message: 'NexaShop API Server is running',
    timestamp: new Date().toISOString()
  });
});

// Test routes for our new features
app.get('/api/v1/refunds', (req, res) => {
  res.json({ success: true, data: [], message: 'Refunds API working' });
});

app.get('/api/v1/coupons', (req, res) => {
  res.json({ success: true, data: [], message: 'Coupons API working' });
});

app.get('/api/v1/variant-attributes', (req, res) => {
  res.json({ success: true, data: [], message: 'Variant Attributes API working' });
});

app.get('/api/v1/shipping/carriers', (req, res) => {
  res.json({ success: true, data: [], message: 'Shipping Carriers API working' });
});

const PORT = process.env.PORT || 5007;

app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`✅ Ready for testing`);
});
