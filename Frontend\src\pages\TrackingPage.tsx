import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Typography,
  Input,
  Button,
  Timeline,
  Tag,
  Row,
  Col,
  Space,
  message,
  Spin,
  Empty,
  Progress,
} from 'antd';
import {
  TruckOutlined,
  SearchOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Search } = Input;

interface TrackingEvent {
  timestamp: string;
  status: string;
  location: string;
  description: string;
}

interface TrackingInfo {
  trackingNumber: string;
  carrier: string;
  status: string;
  estimatedDelivery: string;
  deliveredAt?: string;
  currentLocation: string;
  events: TrackingEvent[];
}

const TrackingPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [trackingNumber, setTrackingNumber] = useState(searchParams.get('number') || '');
  const [trackingInfo, setTrackingInfo] = useState<TrackingInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);

  useEffect(() => {
    const number = searchParams.get('number');
    if (number) {
      setTrackingNumber(number);
      handleTrack(number);
    }
  }, [searchParams]);

  const handleTrack = async (number?: string) => {
    const trackingNum = number || trackingNumber;

    if (!trackingNum.trim()) {
      message.error('Please enter a tracking number');
      return;
    }

    setLoading(true);
    setSearched(true);

    try {
      const response = await fetch(`/api/v1/shipping/track/${trackingNum}`);
      const data = await response.json();

      if (data.success) {
        setTrackingInfo(data.data.tracking);
        // Update URL with tracking number
        setSearchParams({ number: trackingNum });
      } else {
        setTrackingInfo(null);
        message.error(data.message || 'Tracking number not found');
      }
    } catch (error) {
      setTrackingInfo(null);
      message.error('Failed to fetch tracking information');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created':
      case 'shipped':
        return 'blue';
      case 'in_transit':
        return 'orange';
      case 'out_for_delivery':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'exception':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'created':
      case 'shipped':
        return <TruckOutlined />;
      case 'in_transit':
        return <ClockCircleOutlined />;
      case 'out_for_delivery':
        return <TruckOutlined />;
      case 'delivered':
        return <CheckCircleOutlined />;
      case 'exception':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getProgressPercent = (status: string) => {
    switch (status) {
      case 'created':
        return 20;
      case 'shipped':
        return 40;
      case 'in_transit':
        return 60;
      case 'out_for_delivery':
        return 80;
      case 'delivered':
        return 100;
      default:
        return 0;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: '32px', textAlign: 'center' }}>
          <TruckOutlined style={{ marginRight: '12px' }} />
          Package Tracking
        </Title>

        {/* Search Section */}
        <Card style={{ marginBottom: '24px' }}>
          <Row gutter={16} align="middle">
            <Col xs={24} md={18}>
              <Search
                placeholder="Enter tracking number (e.g., TRK123456789)"
                value={trackingNumber}
                onChange={(e) => setTrackingNumber(e.target.value)}
                onSearch={() => handleTrack()}
                enterButton={
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    loading={loading}
                    style={{
                      background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                      border: 'none',
                    }}
                  >
                    Track Package
                  </Button>
                }
                size="large"
              />
            </Col>
            <Col xs={24} md={6}>
              <div style={{ textAlign: 'center', marginTop: '16px' }}>
                <Text type="secondary">
                  Try: TRK123456789 or TRK987654321
                </Text>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Loading State */}
        {loading && (
          <Card>
            <div style={{ textAlign: 'center', padding: '48px' }}>
              <Spin size="large" />
              <p style={{ marginTop: '16px' }}>Tracking your package...</p>
            </div>
          </Card>
        )}

        {/* No Results */}
        {searched && !loading && !trackingInfo && (
          <Card>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="Tracking number not found"
            >
              <Text type="secondary">
                Please check your tracking number and try again
              </Text>
            </Empty>
          </Card>
        )}

        {/* Tracking Results */}
        {trackingInfo && !loading && (
          <div>
            {/* Package Status Overview */}
            <Card style={{ marginBottom: '24px' }}>
              <Row gutter={24}>
                <Col xs={24} md={12}>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <div>
                      <Text strong style={{ fontSize: '16px' }}>
                        Tracking Number: {trackingInfo.trackingNumber}
                      </Text>
                    </div>
                    <div>
                      <Text>Carrier: </Text>
                      <Tag color="blue">{trackingInfo.carrier}</Tag>
                    </div>
                    <div>
                      <Text>Status: </Text>
                      <Tag color={getStatusColor(trackingInfo.status)} icon={getStatusIcon(trackingInfo.status)}>
                        {trackingInfo.status.replace('_', ' ').toUpperCase()}
                      </Tag>
                    </div>
                    <div>
                      <EnvironmentOutlined style={{ marginRight: '8px' }} />
                      <Text>Current Location: {trackingInfo.currentLocation}</Text>
                    </div>
                  </Space>
                </Col>
                <Col xs={24} md={12}>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <div>
                      <CalendarOutlined style={{ marginRight: '8px' }} />
                      <Text>
                        {trackingInfo.status === 'delivered' ? 'Delivered: ' : 'Estimated Delivery: '}
                        <Text strong>
                          {trackingInfo.deliveredAt
                            ? formatDate(trackingInfo.deliveredAt)
                            : new Date(trackingInfo.estimatedDelivery).toLocaleDateString()
                          }
                        </Text>
                      </Text>
                    </div>
                    <div style={{ marginTop: '16px' }}>
                      <Text strong>Delivery Progress</Text>
                      <Progress
                        percent={getProgressPercent(trackingInfo.status)}
                        status={trackingInfo.status === 'delivered' ? 'success' : 'active'}
                        strokeColor={{
                          '0%': '#108ee9',
                          '100%': '#87d068',
                        }}
                      />
                    </div>
                  </Space>
                </Col>
              </Row>
            </Card>

            {/* Tracking Timeline */}
            <Card title="Tracking History">
              <Timeline mode="left">
                {trackingInfo.events
                  .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                  .map((event, index) => (
                    <Timeline.Item
                      key={index}
                      dot={getStatusIcon(event.status)}
                      color={getStatusColor(event.status)}
                    >
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong style={{ fontSize: '14px' }}>
                          {event.description}
                        </Text>
                      </div>
                      <div style={{ marginBottom: '4px' }}>
                        <EnvironmentOutlined style={{ marginRight: '4px', color: '#8c8c8c' }} />
                        <Text type="secondary">{event.location}</Text>
                      </div>
                      <div>
                        <ClockCircleOutlined style={{ marginRight: '4px', color: '#8c8c8c' }} />
                        <Text type="secondary">{formatDate(event.timestamp)}</Text>
                      </div>
                    </Timeline.Item>
                  ))}
              </Timeline>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrackingPage;
