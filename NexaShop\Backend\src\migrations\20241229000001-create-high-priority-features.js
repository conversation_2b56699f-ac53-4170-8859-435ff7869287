'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Create Refund tables
      await queryInterface.createTable('Refunds', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        refund_id: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true
        },
        order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        payment_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'payments',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL'
        },
        amount: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        currency: {
          type: Sequelize.STRING(3),
          allowNull: false,
          defaultValue: 'USD'
        },
        reason: {
          type: Sequelize.ENUM('customer_request', 'defective_product', 'wrong_item', 'damaged_shipping', 'not_as_described', 'other'),
          allowNull: false
        },
        status: {
          type: Sequelize.ENUM('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action'),
          allowNull: false,
          defaultValue: 'pending'
        },
        refund_type: {
          type: Sequelize.ENUM('full', 'partial'),
          allowNull: false,
          defaultValue: 'full'
        },
        payment_provider: {
          type: Sequelize.ENUM('stripe', 'paypal'),
          allowNull: true
        },
        provider_refund_id: {
          type: Sequelize.STRING(255),
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        processed_at: {
          type: Sequelize.DATE,
          allowNull: true
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: {}
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('RefundItems', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        refund_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'refunds',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        order_item_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'order_items',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        quantity: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 1
        },
        amount: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        reason: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('RefundStatusHistories', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        refund_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'refunds',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        status: {
          type: Sequelize.ENUM('pending', 'processing', 'succeeded', 'failed', 'canceled', 'requires_action'),
          allowNull: false
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        changed_by: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // Create Coupon tables
      await queryInterface.createTable('Coupons', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        code: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true
        },
        name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        type: {
          type: Sequelize.ENUM('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y'),
          allowNull: false
        },
        value: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        percentage: {
          type: Sequelize.DECIMAL(5, 2),
          allowNull: true
        },
        buy_quantity: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        get_quantity: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        minimum_amount: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        maximum_discount: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        usage_limit: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        usage_limit_per_user: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        used_count: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        applicable_to: {
          type: Sequelize.ENUM('all', 'specific_products', 'specific_categories', 'specific_users'),
          allowNull: false,
          defaultValue: 'all'
        },
        starts_at: {
          type: Sequelize.DATE,
          allowNull: true
        },
        expires_at: {
          type: Sequelize.DATE,
          allowNull: true
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('CouponProducts', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        coupon_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'coupons',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        product_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'products',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('CouponCategories', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        coupon_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'coupons',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        category_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'categories',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('CouponUsers', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        coupon_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'coupons',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        user_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('CouponUsages', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        coupon_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'coupons',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        user_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        discount_amount: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Drop tables in reverse order
      await queryInterface.dropTable('CouponUsages', { transaction });
      await queryInterface.dropTable('CouponUsers', { transaction });
      await queryInterface.dropTable('CouponCategories', { transaction });
      await queryInterface.dropTable('CouponProducts', { transaction });
      await queryInterface.dropTable('Coupons', { transaction });
      
      await queryInterface.dropTable('RefundStatusHistories', { transaction });
      await queryInterface.dropTable('RefundItems', { transaction });
      await queryInterface.dropTable('Refunds', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
