import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Empty,
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  HomeOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  UserOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { 
  deleteAddress, 
  setDefaultAddress, 
  setSelectedAddress 
} from '../../store/slices/addressSlice';
import { Address } from '../../store/slices/addressSlice';
import AddressForm from '../AddressForm/AddressForm';

const { Text, Title } = Typography;
const { confirm } = Modal;

interface AddressListProps {
  selectable?: boolean;
  onSelect?: (address: Address) => void;
  showActions?: boolean;
}

const AddressList: React.FC<AddressListProps> = ({ 
  selectable = false, 
  onSelect,
  showActions = true 
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { addresses, loading, selectedAddress } = useSelector((state: RootState) => state.addresses);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  const handleDelete = (address: Address) => {
    confirm({
      title: 'Delete Address',
      content: `Are you sure you want to delete this address?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await dispatch(deleteAddress(address.id)).unwrap();
          message.success('Address deleted successfully');
        } catch (error: any) {
          message.error(error.message || 'Failed to delete address');
        }
      },
    });
  };

  const handleSetDefault = async (address: Address) => {
    try {
      await dispatch(setDefaultAddress(address.id)).unwrap();
      message.success('Default address updated');
    } catch (error: any) {
      message.error(error.message || 'Failed to update default address');
    }
  };

  const handleSelect = (address: Address) => {
    if (selectable) {
      dispatch(setSelectedAddress(address));
      onSelect?.(address);
    }
  };

  const getAddressTypeIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <HomeOutlined />;
      case 'work':
        return <EnvironmentOutlined />;
      default:
        return <EnvironmentOutlined />;
    }
  };

  const getAddressTypeColor = (type: string) => {
    switch (type) {
      case 'home':
        return 'blue';
      case 'work':
        return 'green';
      default:
        return 'default';
    }
  };

  if (addresses.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '48px 24px' }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="No addresses found"
        >
          {showActions && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setShowAddForm(true)}
              style={{
                background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                border: 'none',
              }}
            >
              Add Address
            </Button>
          )}
        </Empty>

        <Modal
          title="Add New Address"
          open={showAddForm}
          onCancel={() => setShowAddForm(false)}
          footer={null}
          width={800}
        >
          <AddressForm
            onSuccess={() => setShowAddForm(false)}
            onCancel={() => setShowAddForm(false)}
          />
        </Modal>
      </div>
    );
  }

  return (
    <div>
      {showActions && (
        <div style={{ marginBottom: '24px', textAlign: 'right' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowAddForm(true)}
            style={{
              background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              border: 'none',
            }}
          >
            Add New Address
          </Button>
        </div>
      )}

      <Row gutter={[16, 16]}>
        {addresses.map((address) => (
          <Col xs={24} md={12} lg={8} key={address.id}>
            <Card
              hoverable={selectable}
              onClick={() => handleSelect(address)}
              style={{
                borderRadius: '12px',
                border: selectable && selectedAddress?.id === address.id 
                  ? '2px solid #1890ff' 
                  : '1px solid #d9d9d9',
                boxShadow: selectable && selectedAddress?.id === address.id
                  ? '0 4px 12px rgba(24, 144, 255, 0.3)'
                  : '0 2px 8px rgba(0,0,0,0.1)',
                cursor: selectable ? 'pointer' : 'default',
              }}
              actions={showActions ? [
                <Button
                  type="text"
                  icon={address.isDefault ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!address.isDefault) {
                      handleSetDefault(address);
                    }
                  }}
                  disabled={address.isDefault}
                  title={address.isDefault ? 'Default address' : 'Set as default'}
                />,
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingAddress(address);
                  }}
                />,
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(address);
                  }}
                />,
              ] : undefined}
            >
              <div style={{ marginBottom: '12px' }}>
                <Space>
                  <Tag 
                    color={getAddressTypeColor(address.type)} 
                    icon={getAddressTypeIcon(address.type)}
                  >
                    {address.type.charAt(0).toUpperCase() + address.type.slice(1)}
                  </Tag>
                  {address.isDefault && (
                    <Tag color="gold" icon={<StarFilled />}>
                      Default
                    </Tag>
                  )}
                </Space>
              </div>

              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div>
                  <UserOutlined style={{ marginRight: '8px', color: '#8c8c8c' }} />
                  <Text strong>{address.name}</Text>
                </div>
                
                <div>
                  <PhoneOutlined style={{ marginRight: '8px', color: '#8c8c8c' }} />
                  <Text>{address.phone}</Text>
                </div>
                
                <div>
                  <EnvironmentOutlined style={{ marginRight: '8px', color: '#8c8c8c' }} />
                  <Text>
                    {address.address}
                    <br />
                    {address.city}, {address.state} {address.postalCode}
                    <br />
                    {address.country}
                  </Text>
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>

      {/* Add Address Modal */}
      <Modal
        title="Add New Address"
        open={showAddForm}
        onCancel={() => setShowAddForm(false)}
        footer={null}
        width={800}
      >
        <AddressForm
          onSuccess={() => setShowAddForm(false)}
          onCancel={() => setShowAddForm(false)}
        />
      </Modal>

      {/* Edit Address Modal */}
      <Modal
        title="Edit Address"
        open={!!editingAddress}
        onCancel={() => setEditingAddress(null)}
        footer={null}
        width={800}
      >
        <AddressForm
          address={editingAddress}
          onSuccess={() => setEditingAddress(null)}
          onCancel={() => setEditingAddress(null)}
        />
      </Modal>
    </div>
  );
};

export default AddressList;
