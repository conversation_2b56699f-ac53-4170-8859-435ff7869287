const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const productVariantService = require('../services/productVariantService');
const {
  VariantAttribute,
  VariantAttributeValue,
  ProductVariant
} = require('../models/ProductVariant');

const router = express.Router();

// @route   POST /api/v1/product-variants/attributes
// @desc    Create variant attribute (Admin only)
// @access  Private (Admin)
router.post('/attributes', [
  authenticate,
  requireAdmin,
  body('name').isString().isLength({ min: 1, max: 100 }).withMessage('Name is required'),
  body('displayName').isString().isLength({ min: 1, max: 100 }).withMessage('Display name is required'),
  body('type').optional().isIn(['text', 'color', 'image', 'number']).withMessage('Invalid type'),
  body('isRequired').optional().isBoolean().withMessage('isRequired must be boolean'),
  body('sortOrder').optional().isInt().withMessage('Sort order must be integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await productVariantService.createVariantAttribute(req.body);

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Variant attribute created successfully',
        data: { attribute: result.attribute }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create variant attribute error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/product-variants/attributes/:id/values
// @desc    Create variant attribute value (Admin only)
// @access  Private (Admin)
router.post('/attributes/:id/values', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid attribute ID required'),
  body('value').isString().isLength({ min: 1, max: 200 }).withMessage('Value is required'),
  body('displayValue').isString().isLength({ min: 1, max: 200 }).withMessage('Display value is required'),
  body('colorCode').optional().matches(/^#[0-9A-F]{6}$/i).withMessage('Invalid color code'),
  body('imageUrl').optional().isURL().withMessage('Invalid image URL'),
  body('sortOrder').optional().isInt().withMessage('Sort order must be integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await productVariantService.createVariantAttributeValue({
      attributeId: req.params.id,
      ...req.body
    });

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Variant attribute value created successfully',
        data: { attributeValue: result.attributeValue }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create variant attribute value error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/product-variants/attributes
// @desc    Get all variant attributes
// @access  Public
router.get('/attributes', async (req, res) => {
  try {
    const attributes = await productVariantService.getAllVariantAttributes();

    res.json({
      success: true,
      data: { attributes }
    });
  } catch (error) {
    console.error('Get variant attributes error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/product-variants
// @desc    Create product variant (Admin only)
// @access  Private (Admin)
router.post('/', [
  authenticate,
  requireAdmin,
  body('productId').isUUID().withMessage('Valid product ID required'),
  body('sku').isString().isLength({ min: 1, max: 100 }).withMessage('SKU is required'),
  body('price').optional().isFloat({ min: 0 }).withMessage('Price must be positive'),
  body('inventoryQuantity').optional().isInt({ min: 0 }).withMessage('Inventory quantity must be non-negative'),
  body('attributes').optional().isArray().withMessage('Attributes must be array'),
  body('attributes.*.attributeId').optional().isUUID().withMessage('Valid attribute ID required'),
  body('attributes.*.attributeValueId').optional().isUUID().withMessage('Valid attribute value ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await productVariantService.createProductVariant(req.body);

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Product variant created successfully',
        data: { variant: result.variant }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create product variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/product-variants/product/:productId
// @desc    Get variants for a product
// @access  Public
router.get('/product/:productId', [
  param('productId').isUUID().withMessage('Valid product ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const variants = await productVariantService.getProductVariants(req.params.productId);

    res.json({
      success: true,
      data: { variants }
    });
  } catch (error) {
    console.error('Get product variants error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/product-variants/:id
// @desc    Get variant by ID
// @access  Public
router.get('/:id', [
  param('id').isUUID().withMessage('Valid variant ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const variant = await productVariantService.getVariantById(req.params.id);

    if (!variant) {
      return res.status(404).json({
        success: false,
        message: 'Variant not found'
      });
    }

    res.json({
      success: true,
      data: { variant }
    });
  } catch (error) {
    console.error('Get variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/product-variants/find
// @desc    Find variant by attributes
// @access  Public
router.post('/find', [
  body('productId').isUUID().withMessage('Valid product ID required'),
  body('attributes').optional().isArray().withMessage('Attributes must be array'),
  body('attributes.*.attributeId').optional().isUUID().withMessage('Valid attribute ID required'),
  body('attributes.*.valueId').optional().isUUID().withMessage('Valid value ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { productId, attributes } = req.body;

    const variant = await productVariantService.findVariantByAttributes(productId, attributes);

    if (!variant) {
      return res.status(404).json({
        success: false,
        message: 'No matching variant found'
      });
    }

    res.json({
      success: true,
      data: { variant }
    });
  } catch (error) {
    console.error('Find variant error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/product-variants/:id/inventory
// @desc    Update variant inventory (Admin only)
// @access  Private (Admin)
router.post('/:id/inventory', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid variant ID required'),
  body('quantityChange').isInt().withMessage('Quantity change must be integer'),
  body('changeType').isIn([
    'sale', 'return', 'restock', 'adjustment', 'damaged', 'lost', 'transfer'
  ]).withMessage('Invalid change type'),
  body('reason').optional().isString().withMessage('Reason must be string'),
  body('notes').optional().isString().withMessage('Notes must be string'),
  body('location').optional().isString().withMessage('Location must be string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await productVariantService.updateVariantInventory({
      variantId: req.params.id,
      userId: req.user.id,
      ...req.body
    });

    if (result.success) {
      res.json({
        success: true,
        message: 'Inventory updated successfully',
        data: {
          variant: result.variant,
          inventoryChange: result.inventoryChange
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Update variant inventory error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/product-variants/:id/availability
// @desc    Check variant availability
// @access  Public
router.get('/:id/availability', [
  param('id').isUUID().withMessage('Valid variant ID required'),
  query('quantity').optional().isInt({ min: 1 }).withMessage('Quantity must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { quantity = 1 } = req.query;

    const availability = await productVariantService.checkVariantAvailability(
      req.params.id,
      parseInt(quantity)
    );

    res.json({
      success: true,
      data: { availability }
    });
  } catch (error) {
    console.error('Check variant availability error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/product-variants/:id/inventory-logs
// @desc    Get variant inventory logs (Admin only)
// @access  Private (Admin)
router.get('/:id/inventory-logs', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid variant ID required'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be positive'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('changeType').optional().isString().withMessage('Change type must be string'),
  query('startDate').optional().isISO8601().withMessage('Valid start date required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await productVariantService.getVariantInventoryLogs(
      req.params.id,
      req.query
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Get variant inventory logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
