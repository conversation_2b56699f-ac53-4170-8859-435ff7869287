const express = require('express');
const { body, validationResult, param, query } = require('express-validator');
const { authenticate, requireCustomer, requireAdmin } = require('../middleware/auth');
const { Chat, ChatMessage, ChatParticipant, User, AgentStatus } = require('../models');
const socketService = require('../services/socketService');
const { Op } = require('sequelize');

const router = express.Router();

// @route   POST /api/v1/chat/create
// @desc    Create a new chat session
// @access  Private (Customer)
router.post('/create', [
  authenticate,
  requireCustomer,
  body('category').optional().isIn([
    'general_inquiry',
    'order_support', 
    'payment_issue',
    'shipping_inquiry',
    'product_question',
    'technical_support',
    'complaint',
    'refund_request'
  ]).withMessage('Invalid category'),
  body('priority').optional().isIn(['low', 'normal', 'high', 'urgent']).withMessage('Invalid priority'),
  body('title').optional().isString().isLength({ min: 1, max: 255 }).withMessage('Title must be 1-255 characters'),
  body('description').optional().isString().withMessage('Description must be a string'),
  body('metadata').optional().isObject().withMessage('Metadata must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { category = 'general_inquiry', priority = 'normal', title, description, metadata = {} } = req.body;
    const customerId = req.user.id;

    // Check if customer has an active chat
    const existingChat = await Chat.findOne({
      where: {
        customer_id: customerId,
        status: ['pending', 'active']
      }
    });

    if (existingChat) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active chat session',
        data: { chatId: existingChat.id }
      });
    }

    // Create new chat
    const chat = await Chat.create({
      customer_id: customerId,
      category,
      priority,
      title: title || `${category.replace('_', ' ')} - ${new Date().toLocaleDateString()}`,
      description,
      metadata: {
        ...metadata,
        user_agent: req.headers['user-agent'],
        ip_address: req.ip,
        created_from: 'web'
      }
    });

    // Add customer as participant
    await ChatParticipant.create({
      chat_id: chat.id,
      user_id: customerId,
      role: 'customer',
      permissions: {
        can_send_messages: true,
        can_send_files: true,
        can_view_history: true,
        can_close_chat: true,
        can_transfer_chat: false
      }
    });

    // Try to assign an available agent
    const availableAgent = await findAvailableAgent(category);
    if (availableAgent) {
      await assignChatToAgent(chat.id, availableAgent.id);
    }

    // Load chat with associations
    const chatWithDetails = await Chat.findByPk(chat.id, {
      include: [
        { model: User, as: 'customer', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: User, as: 'assignedAgent', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: ChatParticipant, as: 'participants', include: [
          { model: User, as: 'user', attributes: ['id', 'first_name', 'last_name', 'email'] }
        ]}
      ]
    });

    // Notify available agents about new chat
    if (!availableAgent) {
      socketService.sendToRoom('agents', 'new_chat_pending', {
        chat: chatWithDetails,
        timestamp: new Date().toISOString()
      });
    }

    res.status(201).json({
      success: true,
      message: 'Chat created successfully',
      data: { chat: chatWithDetails }
    });

  } catch (error) {
    console.error('Create chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create chat',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/chat/my-chats
// @desc    Get user's chat sessions
// @access  Private
router.get('/my-chats', [
  authenticate,
  query('status').optional().isIn(['pending', 'active', 'closed', 'transferred']).withMessage('Invalid status'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be >= 0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, limit = 20, offset = 0 } = req.query;
    const userId = req.user.id;
    const userRole = req.user.role;

    let whereClause = {};
    
    // Filter by user role
    if (userRole === 'customer') {
      whereClause.customer_id = userId;
    } else if (userRole === 'admin' || userRole === 'agent') {
      whereClause.assigned_agent_id = userId;
    }

    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    const chats = await Chat.findAndCountAll({
      where: whereClause,
      include: [
        { model: User, as: 'customer', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: User, as: 'assignedAgent', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ],
      order: [['last_message_at', 'DESC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        chats: chats.rows,
        total: chats.count,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Get my chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chats',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Helper function to find available agent
async function findAvailableAgent(category) {
  try {
    const agents = await AgentStatus.findAll({
      where: {
        status: 'online',
        availability: true,
        current_chat_count: {
          [Op.lt]: sequelize.col('max_concurrent_chats')
        }
      },
      include: [
        { model: User, as: 'agent', where: { role: 'agent', status: 'active' } }
      ],
      order: [
        ['priority_score', 'DESC'],
        ['current_chat_count', 'ASC']
      ]
    });

    // Find agent with matching specialty
    const specialistAgent = agents.find(agent => 
      agent.specialties.includes(category) || agent.specialties.includes('general')
    );

    return specialistAgent || agents[0] || null;
  } catch (error) {
    console.error('Find available agent error:', error);
    return null;
  }
}

// Helper function to assign chat to agent
async function assignChatToAgent(chatId, agentId) {
  try {
    // Update chat
    await Chat.update(
      { 
        assigned_agent_id: agentId,
        status: 'active'
      },
      { where: { id: chatId } }
    );

    // Add agent as participant
    await ChatParticipant.create({
      chat_id: chatId,
      user_id: agentId,
      role: 'agent',
      permissions: {
        can_send_messages: true,
        can_send_files: true,
        can_view_history: true,
        can_close_chat: true,
        can_transfer_chat: true
      }
    });

    // Update agent status
    await AgentStatus.increment('current_chat_count', { where: { agent_id: agentId } });

    return true;
  } catch (error) {
    console.error('Assign chat to agent error:', error);
    return false;
  }
}

// @route   GET /api/v1/chat/:id
// @desc    Get chat details with messages
// @access  Private
router.get('/:id', [
  authenticate,
  param('id').isUUID().withMessage('Invalid chat ID'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be >= 0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    const userId = req.user.id;

    // Check if user is participant in this chat
    const participant = await ChatParticipant.findOne({
      where: {
        chat_id: id,
        user_id: userId,
        status: 'active'
      }
    });

    if (!participant && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this chat'
      });
    }

    // Get chat details
    const chat = await Chat.findByPk(id, {
      include: [
        { model: User, as: 'customer', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] },
        { model: User, as: 'assignedAgent', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] },
        { model: ChatParticipant, as: 'participants', include: [
          { model: User, as: 'user', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] }
        ]}
      ]
    });

    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    // Get messages
    const messages = await ChatMessage.findAndCountAll({
      where: { chat_id: id },
      include: [
        { model: User, as: 'sender', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] },
        { model: ChatMessage, as: 'replyTo', attributes: ['id', 'content', 'sender_id'] }
      ],
      order: [['created_at', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Mark messages as read for current user
    await ChatMessage.update(
      { is_read: true, read_at: new Date() },
      {
        where: {
          chat_id: id,
          sender_id: { [Op.ne]: userId },
          is_read: false
        }
      }
    );

    // Update unread count
    const unreadField = participant?.role === 'customer' ? 'unread_count_customer' : 'unread_count_agent';
    await Chat.update(
      { [unreadField]: 0 },
      { where: { id } }
    );

    res.json({
      success: true,
      data: {
        chat,
        messages: messages.rows,
        total_messages: messages.count,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Get chat details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get chat details',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/chat/:id/messages
// @desc    Send a message in chat
// @access  Private
router.post('/:id/messages', [
  authenticate,
  param('id').isUUID().withMessage('Invalid chat ID'),
  body('content').isString().isLength({ min: 1, max: 5000 }).withMessage('Content must be 1-5000 characters'),
  body('message_type').optional().isIn(['text', 'image', 'file', 'quick_reply']).withMessage('Invalid message type'),
  body('reply_to_id').optional().isUUID().withMessage('Invalid reply message ID'),
  body('file_url').optional().isURL().withMessage('Invalid file URL'),
  body('file_name').optional().isString().withMessage('File name must be a string'),
  body('file_size').optional().isInt({ min: 0 }).withMessage('File size must be a positive integer'),
  body('file_type').optional().isString().withMessage('File type must be a string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const {
      content,
      message_type = 'text',
      reply_to_id,
      file_url,
      file_name,
      file_size,
      file_type
    } = req.body;
    const userId = req.user.id;

    // Check if user is participant in this chat
    const participant = await ChatParticipant.findOne({
      where: {
        chat_id: id,
        user_id: userId,
        status: 'active'
      }
    });

    if (!participant) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this chat'
      });
    }

    // Check if user has permission to send messages
    if (!participant.permissions.can_send_messages) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to send messages in this chat'
      });
    }

    // Get chat to check status
    const chat = await Chat.findByPk(id);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    if (chat.status === 'closed') {
      return res.status(400).json({
        success: false,
        message: 'Cannot send messages to a closed chat'
      });
    }

    // Determine sender type
    const senderType = participant.role === 'customer' ? 'customer' : 'agent';

    // Create message
    const message = await ChatMessage.create({
      chat_id: id,
      sender_id: userId,
      sender_type: senderType,
      message_type,
      content,
      reply_to_id,
      file_url,
      file_name,
      file_size,
      file_type,
      delivery_status: 'sent'
    });

    // Update chat last message info
    await Chat.update({
      last_message_at: new Date(),
      last_message_preview: content.substring(0, 500),
      // Increment unread count for the other party
      ...(senderType === 'customer'
        ? { unread_count_agent: chat.unread_count_agent + 1 }
        : { unread_count_customer: chat.unread_count_customer + 1 }
      )
    }, { where: { id } });

    // Load message with sender details
    const messageWithSender = await ChatMessage.findByPk(message.id, {
      include: [
        { model: User, as: 'sender', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] },
        { model: ChatMessage, as: 'replyTo', attributes: ['id', 'content', 'sender_id'] }
      ]
    });

    // Send real-time notification
    socketService.sendToRoom(`chat_${id}`, 'new_message', {
      message: messageWithSender,
      chat_id: id,
      timestamp: new Date().toISOString()
    });

    // Notify the other party if they're not in the chat room
    const otherParticipants = await ChatParticipant.findAll({
      where: {
        chat_id: id,
        user_id: { [Op.ne]: userId },
        status: 'active'
      }
    });

    otherParticipants.forEach(participant => {
      socketService.sendToUser(participant.user_id, 'chat_message_notification', {
        chat_id: id,
        message: messageWithSender,
        sender_name: `${req.user.first_name} ${req.user.last_name}`,
        timestamp: new Date().toISOString()
      });
    });

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: { message: messageWithSender }
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
