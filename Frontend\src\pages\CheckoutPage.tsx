import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Typography,
  Steps,
  Button,
  Space,
  Row,
  Col,
  Divider,
  Radio,
  message,
  Spin,
  Image,
  Tag,
} from 'antd';
import {
  EnvironmentOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  TruckOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchAddresses } from '../store/slices/addressSlice';
import { createOrder } from '../store/slices/orderSlice';
import { clearCart } from '../store/slices/cartSlice';
import { clearPaymentData } from '../store/slices/paymentSlice';
import AddressList from '../components/AddressList/AddressList';
import PayPalButton from '../components/PaymentMethods/PayPalButton';
import StripePayment from '../components/PaymentMethods/StripePayment';

const { Title, Text } = Typography;
const { Step } = Steps;

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedShipping, setSelectedShipping] = useState('standard');
  const [selectedPayment, setSelectedPayment] = useState('credit-card');
  const [isProcessing, setIsProcessing] = useState(false);

  const { items, totalAmount, shippingCost, currency } = useSelector((state: RootState) => state.cart);
  const { addresses, selectedAddress, loading: addressLoading } = useSelector((state: RootState) => state.addresses);
  const { loading: orderLoading } = useSelector((state: RootState) => state.orders);
  const { loading: paymentLoading } = useSelector((state: RootState) => state.payments);

  useEffect(() => {
    // Redirect if cart is empty
    if (items.length === 0) {
      message.warning('Your cart is empty');
      navigate('/cart');
      return;
    }

    // Fetch addresses
    dispatch(fetchAddresses());
  }, [dispatch, items.length, navigate]);

  const shippingOptions = [
    {
      id: 'standard',
      name: 'Standard Shipping',
      description: '5-7 business days',
      cost: 0,
      icon: <TruckOutlined />,
    },
    {
      id: 'express',
      name: 'Express Shipping',
      description: '2-3 business days',
      cost: 15.99,
      icon: <TruckOutlined />,
    },
    {
      id: 'overnight',
      name: 'Overnight Shipping',
      description: 'Next business day',
      cost: 29.99,
      icon: <TruckOutlined />,
    },
  ];

  const paymentMethods = [
    {
      id: 'credit-card',
      name: 'Credit Card',
      description: 'Visa, MasterCard, American Express',
      icon: <CreditCardOutlined />,
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Pay with your PayPal account',
      icon: '💳',
    },
    {
      id: 'apple-pay',
      name: 'Apple Pay',
      description: 'Pay with Touch ID or Face ID',
      icon: '🍎',
    },
  ];

  const selectedShippingOption = shippingOptions.find(option => option.id === selectedShipping);
  const selectedPaymentMethod = paymentMethods.find(method => method.id === selectedPayment);

  const subtotal = totalAmount;
  const shipping = selectedShippingOption?.cost || 0;
  const tax = subtotal * 0.08; // 8% tax
  const total = subtotal + shipping + tax;

  const handleNext = () => {
    if (currentStep === 0) {
      // Validate address selection
      if (!selectedAddress) {
        message.error('Please select a shipping address');
        return;
      }
    }

    if (currentStep === 1) {
      // Validate payment method
      if (!selectedPayment) {
        message.error('Please select a payment method');
        return;
      }
    }

    setCurrentStep(currentStep + 1);
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handlePlaceOrder = async () => {
    if (!selectedAddress) {
      message.error('Please select a shipping address');
      return;
    }

    setIsProcessing(true);

    try {
      const orderData = {
        items: items.map(item => ({
          id: item.id,
          name: item.name,
          image: item.image,
          price: item.price,
          quantity: item.quantity,
          variant: item.variant,
        })),
        shippingAddress: selectedAddress,
        paymentMethod: selectedPaymentMethod?.name || 'Credit Card',
        notes: `Shipping: ${selectedShippingOption?.name}`,
      };

      const order = await dispatch(createOrder(orderData)).unwrap();

      // Clear cart and payment data
      dispatch(clearCart());
      dispatch(clearPaymentData());

      message.success('Order placed successfully!');
      navigate(`/orders/${order.id}`);
    } catch (error: any) {
      message.error(error.message || 'Failed to place order');
    } finally {
      setIsProcessing(false);
    }
  };

  const steps = [
    {
      title: 'Shipping',
      description: 'Address information',
      icon: <EnvironmentOutlined />,
    },
    {
      title: 'Payment',
      description: 'Payment method',
      icon: <CreditCardOutlined />,
    },
    {
      title: 'Review',
      description: 'Order confirmation',
      icon: <CheckCircleOutlined />,
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div>
            <Title level={4} style={{ marginBottom: '24px' }}>
              <EnvironmentOutlined style={{ marginRight: '8px' }} />
              Select Shipping Address
            </Title>
            {addressLoading ? (
              <div style={{ textAlign: 'center', padding: '48px' }}>
                <Spin size="large" />
              </div>
            ) : (
              <AddressList selectable showActions />
            )}
          </div>
        );

      case 1:
        return (
          <div>
            <Title level={4} style={{ marginBottom: '24px' }}>
              <TruckOutlined style={{ marginRight: '8px' }} />
              Select Shipping Method
            </Title>
            <Radio.Group
              value={selectedShipping}
              onChange={(e) => setSelectedShipping(e.target.value)}
              style={{ width: '100%', marginBottom: '32px' }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {shippingOptions.map((option) => (
                  <Radio key={option.id} value={option.id}>
                    <Card
                      size="small"
                      style={{
                        width: '100%',
                        border: selectedShipping === option.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                      }}
                    >
                      <Row justify="space-between" align="middle">
                        <Col>
                          <Space>
                            {option.icon}
                            <div>
                              <Text strong>{option.name}</Text>
                              <br />
                              <Text type="secondary">{option.description}</Text>
                            </div>
                          </Space>
                        </Col>
                        <Col>
                          <Text strong>
                            {option.cost === 0 ? 'Free' : `$${option.cost.toFixed(2)}`}
                          </Text>
                        </Col>
                      </Row>
                    </Card>
                  </Radio>
                ))}
              </Space>
            </Radio.Group>

            <Divider />

            <Title level={4} style={{ marginBottom: '24px' }}>
              <CreditCardOutlined style={{ marginRight: '8px' }} />
              Select Payment Method
            </Title>
            <Radio.Group
              value={selectedPayment}
              onChange={(e) => setSelectedPayment(e.target.value)}
              style={{ width: '100%', marginBottom: '24px' }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {paymentMethods.map((method) => (
                  <Radio key={method.id} value={method.id}>
                    <Card
                      size="small"
                      style={{
                        width: '100%',
                        border: selectedPayment === method.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                      }}
                    >
                      <Space>
                        <span style={{ fontSize: '20px' }}>{method.icon}</span>
                        <div>
                          <Text strong>{method.name}</Text>
                          <br />
                          <Text type="secondary">{method.description}</Text>
                        </div>
                      </Space>
                    </Card>
                  </Radio>
                ))}
              </Space>
            </Radio.Group>

            {/* Payment Method Components */}
            {selectedPayment === 'paypal' && (
              <div style={{ marginTop: '24px' }}>
                <PayPalButton
                  orderId="temp-order-id"
                  amount={total}
                  currency={currency}
                  onSuccess={(payment) => {
                    message.success('PayPal payment completed!');
                    // Handle successful payment
                  }}
                  onError={(error) => {
                    message.error('PayPal payment failed');
                  }}
                  onCancel={() => {
                    message.info('PayPal payment cancelled');
                  }}
                />
              </div>
            )}

            {selectedPayment === 'credit-card' && (
              <div style={{ marginTop: '24px' }}>
                <StripePayment
                  orderId="temp-order-id"
                  amount={total}
                  currency={currency}
                  onSuccess={(payment) => {
                    message.success('Credit card payment completed!');
                    // Handle successful payment
                  }}
                  onError={(error) => {
                    message.error('Credit card payment failed');
                  }}
                />
              </div>
            )}

            {selectedPayment === 'apple-pay' && (
              <div style={{ marginTop: '24px' }}>
                <Card>
                  <div style={{ textAlign: 'center', padding: '40px' }}>
                    <span style={{ fontSize: '48px' }}>🍎</span>
                    <Title level={4} style={{ marginTop: '16px' }}>Apple Pay</Title>
                    <Text type="secondary">
                      Apple Pay integration coming soon
                    </Text>
                  </div>
                </Card>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div>
            <Title level={4} style={{ marginBottom: '24px' }}>
              <CheckCircleOutlined style={{ marginRight: '8px' }} />
              Review Your Order
            </Title>

            {/* Order Items */}
            <Card title="Order Items" style={{ marginBottom: '24px' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {items.map((item) => (
                  <Row key={`${item.id}-${JSON.stringify(item.variant)}`} gutter={16} align="middle">
                    <Col span={4}>
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={60}
                        height={60}
                        style={{ objectFit: 'cover', borderRadius: '8px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Text strong>{item.name}</Text>
                      {item.variant && (
                        <div>
                          {Object.entries(item.variant).map(([key, value]) => (
                            <Tag key={key} style={{ marginTop: '4px', fontSize: '11px', padding: '0 4px' }}>
                              {key}: {value}
                            </Tag>
                          ))}
                        </div>
                      )}
                    </Col>
                    <Col span={4}>
                      <Text>Qty: {item.quantity}</Text>
                    </Col>
                    <Col span={4} style={{ textAlign: 'right' }}>
                      <Text strong>${(item.price * item.quantity).toFixed(2)}</Text>
                    </Col>
                  </Row>
                ))}
              </Space>
            </Card>

            {/* Shipping Address */}
            {selectedAddress && (
              <Card title="Shipping Address" style={{ marginBottom: '24px' }}>
                <Text strong>{selectedAddress.name}</Text>
                <br />
                <Text>{selectedAddress.phone}</Text>
                <br />
                <Text>
                  {selectedAddress.address}
                  <br />
                  {selectedAddress.city}, {selectedAddress.state} {selectedAddress.postalCode}
                  <br />
                  {selectedAddress.country}
                </Text>
              </Card>
            )}

            {/* Payment & Shipping */}
            <Row gutter={16}>
              <Col span={12}>
                <Card title="Payment Method">
                  <Space>
                    {selectedPaymentMethod?.icon}
                    <Text strong>{selectedPaymentMethod?.name}</Text>
                  </Space>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="Shipping Method">
                  <Space>
                    {selectedShippingOption?.icon}
                    <div>
                      <Text strong>{selectedShippingOption?.name}</Text>
                      <br />
                      <Text type="secondary">{selectedShippingOption?.description}</Text>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: '32px' }}>
          <ShoppingCartOutlined style={{ marginRight: '12px' }} />
          Checkout
        </Title>

        <Row gutter={24}>
          <Col xs={24} lg={16}>
            <Card style={{ marginBottom: '24px' }}>
              <Steps current={currentStep} style={{ marginBottom: '32px' }}>
                {steps.map((step, index) => (
                  <Step
                    key={index}
                    title={step.title}
                    description={step.description}
                    icon={step.icon}
                  />
                ))}
              </Steps>

              {renderStepContent()}

              <Divider />

              <div style={{ textAlign: 'right' }}>
                <Space>
                  {currentStep > 0 && (
                    <Button size="large" onClick={handlePrevious}>
                      Previous
                    </Button>
                  )}
                  {currentStep < steps.length - 1 ? (
                    <Button
                      type="primary"
                      size="large"
                      onClick={handleNext}
                      style={{
                        background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                        border: 'none',
                      }}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      size="large"
                      loading={isProcessing || orderLoading}
                      onClick={handlePlaceOrder}
                      style={{
                        background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                        border: 'none',
                      }}
                    >
                      Place Order
                    </Button>
                  )}
                </Space>
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="Order Summary" style={{ position: 'sticky', top: '24px' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Row justify="space-between">
                  <Text>Subtotal ({items.length} items):</Text>
                  <Text>${subtotal.toFixed(2)}</Text>
                </Row>
                <Row justify="space-between">
                  <Text>Shipping:</Text>
                  <Text>{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</Text>
                </Row>
                <Row justify="space-between">
                  <Text>Tax:</Text>
                  <Text>${tax.toFixed(2)}</Text>
                </Row>
                <Divider />
                <Row justify="space-between">
                  <Text strong style={{ fontSize: '18px' }}>Total:</Text>
                  <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
                    ${total.toFixed(2)} {currency}
                  </Text>
                </Row>
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default CheckoutPage;
