// 测试服务器启动脚本
const express = require('express');
const path = require('path');

console.log('🚀 开始测试服务器启动...');

// 测试基本模块加载
console.log('📦 测试模块加载...');

try {
  // 测试基本依赖
  const cors = require('cors');
  const bodyParser = require('body-parser');
  const multer = require('multer');
  console.log('✅ 基本依赖加载成功');
  
  // 测试上传服务
  const uploadService = require('./src/services/uploadService');
  console.log('✅ 上传服务加载成功');
  
  // 测试上传路由
  const uploadRoutes = require('./src/routes/upload');
  console.log('✅ 上传路由加载成功');
  
} catch (error) {
  console.log('❌ 模块加载失败:', error.message);
  console.log('错误详情:', error.stack);
  process.exit(1);
}

// 创建简单的测试服务器
const app = express();
const PORT = process.env.PORT || 3001;

// 基本中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 测试路由
app.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    sharp_available: require('./src/services/uploadService').sharpAvailable || false
  });
});

// 上传路由
try {
  app.use('/api/upload', require('./src/routes/upload'));
  console.log('✅ 上传路由注册成功');
} catch (error) {
  console.log('❌ 上传路由注册失败:', error.message);
}

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 启动服务器
const server = app.listen(PORT, () => {
  console.log(`🎉 测试服务器启动成功!`);
  console.log(`📍 服务器地址: http://localhost:${PORT}`);
  console.log(`🧪 测试地址: http://localhost:${PORT}/test`);
  console.log('');
  console.log('可用的测试端点:');
  console.log(`  GET  /test - 服务器状态检查`);
  console.log(`  POST /api/upload/single - 单文件上传`);
  console.log(`  GET  /api/upload/health - 上传服务健康检查`);
  console.log('');
  console.log('按 Ctrl+C 停止服务器');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});
