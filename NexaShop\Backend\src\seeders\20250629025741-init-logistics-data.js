'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Create shipping carriers with basic data
      const carriers = [
        {
          id: 'b875957a-ae1b-42f4-8704-725c84f57ff9',
          name: 'FedEx',
          code: 'fedex',
          display_name: 'FedEx',
          logo_url: 'https://www.fedex.com/content/dam/fedex/us-united-states/images/logos/fedex-logo.png',
          website_url: 'https://www.fedex.com',
          tracking_url_template: 'https://www.fedex.com/apps/fedextrack/?tracknumbers={tracking_number}',
          api_endpoint: 'https://apis.fedex.com',
          supported_countries: JSON.stringify(['US', 'CA', 'MX', 'GB', 'DE', 'FR', 'IT', 'ES', 'AU', 'JP', 'CN']),
          sort_order: 1,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 'c986068b-bf2c-53e5-9815-836d95e68ffa',
          name: 'UPS',
          code: 'ups',
          display_name: 'UPS',
          logo_url: 'https://www.ups.com/assets/resources/images/ups-logo.png',
          website_url: 'https://www.ups.com',
          tracking_url_template: 'https://www.ups.com/track?tracknum={tracking_number}',
          api_endpoint: 'https://onlinetools.ups.com',
          supported_countries: JSON.stringify(['US', 'CA', 'MX', 'GB', 'DE', 'FR', 'IT', 'ES', 'AU', 'JP']),
          sort_order: 2,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      await queryInterface.bulkInsert('shipping_carriers', carriers, { transaction });

      // Create basic variant attributes
      const variantAttributes = [
        {
          id: 'a1b2c3d4-e5f6-4788-8900-a1b2c3d4e5f6',
          name: 'Size',
          display_name: 'Size',
          type: 'text',
          is_required: true,
          sort_order: 1,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 'a2c3d4e5-f6a7-4899-9001-b2c3d4e5f6a7',
          name: 'Color',
          display_name: 'Color',
          type: 'color',
          is_required: true,
          sort_order: 2,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ];

      await queryInterface.bulkInsert('variant_attributes', variantAttributes, { transaction });

      await transaction.commit();
      console.log('✅ Logistics seed data inserted successfully');
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error inserting logistics seed data:', error);
      throw error;
    }
  },

  async down (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.bulkDelete('variant_attributes', null, { transaction });
      await queryInterface.bulkDelete('shipping_carriers', null, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
