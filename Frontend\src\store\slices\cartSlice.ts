import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import cartService, { AddToCartData, UpdateCartItemData } from '../../services/cartService';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  quantity: number;
  variant?: {
    size?: string;
    color?: string;
    style?: string;
  };
  shipping: {
    cost: number;
    estimatedDays: number;
    carrier: string;
  };
}

interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  currency: string;
  isOpen: boolean;
  loading: boolean;
  error: string | null;
  coupon: {
    code: string;
    discount: number;
    discountType: 'percentage' | 'fixed';
    description: string;
  } | null;
}

const initialState: CartState = {
  items: JSON.parse(localStorage.getItem('cartItems') || '[]'),
  totalItems: 0,
  totalAmount: 0,
  subtotal: 0,
  shippingCost: 0,
  tax: 0,
  discount: 0,
  currency: localStorage.getItem('currency') || 'USD',
  isOpen: false,
  loading: false,
  error: null,
  coupon: null,
};

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartService.getCart();
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart');
    }
  }
);

export const addToCartAsync = createAsyncThunk(
  'cart/addToCart',
  async (data: AddToCartData, { rejectWithValue }) => {
    try {
      const response = await cartService.addToCart(data);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to add item to cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItemAsync = createAsyncThunk(
  'cart/updateCartItem',
  async ({ itemId, data }: { itemId: string; data: UpdateCartItemData }, { rejectWithValue }) => {
    try {
      const response = await cartService.updateCartItem(itemId, data);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update cart item');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCartAsync = createAsyncThunk(
  'cart/removeFromCart',
  async (itemId: string, { rejectWithValue }) => {
    try {
      const response = await cartService.removeFromCart(itemId);
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to remove item from cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCartAsync = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartService.clearCart();
      if (response.success) {
        return;
      } else {
        return rejectWithValue(response.error || 'Failed to clear cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to clear cart');
    }
  }
);

export const applyCouponAsync = createAsyncThunk(
  'cart/applyCoupon',
  async (code: string, { rejectWithValue }) => {
    try {
      const response = await cartService.applyCoupon({ code });
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to apply coupon');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to apply coupon');
    }
  }
);

export const removeCouponAsync = createAsyncThunk(
  'cart/removeCoupon',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartService.removeCoupon();
      if (response.success && response.data) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to remove coupon');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove coupon');
    }
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    initializeCart: (state) => {
      const savedItems = JSON.parse(localStorage.getItem('cartItems') || '[]');
      state.items = savedItems;
      cartSlice.caseReducers.calculateTotals(state);
    },
    addToCart: (state, action: PayloadAction<Omit<CartItem, 'quantity'> & { quantity?: number }>) => {
      const existingItem = state.items.find(
        item => 
          item.id === action.payload.id && 
          JSON.stringify(item.variant) === JSON.stringify(action.payload.variant)
      );

      if (existingItem) {
        existingItem.quantity += action.payload.quantity || 1;
      } else {
        state.items.push({
          ...action.payload,
          quantity: action.payload.quantity || 1,
        });
      }
      
      cartSlice.caseReducers.calculateTotals(state);
      cartSlice.caseReducers.saveToLocalStorage(state);
    },
    removeFromCart: (state, action: PayloadAction<{ id: string; variant?: CartItem['variant'] }>) => {
      state.items = state.items.filter(
        item => !(
          item.id === action.payload.id && 
          JSON.stringify(item.variant) === JSON.stringify(action.payload.variant)
        )
      );
      cartSlice.caseReducers.calculateTotals(state);
      cartSlice.caseReducers.saveToLocalStorage(state);
    },
    updateQuantity: (state, action: PayloadAction<{ id: string; variant?: CartItem['variant']; quantity: number }>) => {
      const item = state.items.find(
        item => 
          item.id === action.payload.id && 
          JSON.stringify(item.variant) === JSON.stringify(action.payload.variant)
      );
      
      if (item) {
        if (action.payload.quantity <= 0) {
          state.items = state.items.filter(i => i !== item);
        } else {
          item.quantity = action.payload.quantity;
        }
      }
      
      cartSlice.caseReducers.calculateTotals(state);
      cartSlice.caseReducers.saveToLocalStorage(state);
    },
    clearCart: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalAmount = 0;
      state.shippingCost = 0;
      localStorage.removeItem('cartItems');
    },
    toggleCart: (state) => {
      state.isOpen = !state.isOpen;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
      localStorage.setItem('currency', action.payload);
    },
    calculateTotals: (state) => {
      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.subtotal = state.items.reduce((total, item) => total + (item.price * item.quantity), 0);
      state.shippingCost = state.items.reduce((total, item) => total + item.shipping.cost, 0);
      state.totalAmount = state.subtotal + state.shippingCost + state.tax - state.discount;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    saveToLocalStorage: (state) => {
      localStorage.setItem('cartItems', JSON.stringify(state.items));
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cart
      .addCase(fetchCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        state.currency = action.payload.currency;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch cart';
      })
      // Add to cart
      .addCase(addToCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        cartSlice.caseReducers.saveToLocalStorage(state);
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to add item to cart';
      })
      // Update cart item
      .addCase(updateCartItemAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCartItemAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        cartSlice.caseReducers.saveToLocalStorage(state);
      })
      .addCase(updateCartItemAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to update cart item';
      })
      // Remove from cart
      .addCase(removeFromCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeFromCartAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        cartSlice.caseReducers.saveToLocalStorage(state);
      })
      .addCase(removeFromCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to remove item from cart';
      })
      // Clear cart
      .addCase(clearCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(clearCartAsync.fulfilled, (state) => {
        state.loading = false;
        state.items = [];
        state.totalItems = 0;
        state.subtotal = 0;
        state.shippingCost = 0;
        state.tax = 0;
        state.discount = 0;
        state.totalAmount = 0;
        state.coupon = null;
        localStorage.removeItem('cartItems');
      })
      .addCase(clearCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to clear cart';
      })
      // Apply coupon
      .addCase(applyCouponAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(applyCouponAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        state.coupon = action.payload.coupon;
        state.discount = action.payload.coupon.discount;
      })
      .addCase(applyCouponAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to apply coupon';
      })
      // Remove coupon
      .addCase(removeCouponAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeCouponAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items;
        state.subtotal = action.payload.subtotal;
        state.shippingCost = action.payload.shipping;
        state.tax = action.payload.tax;
        state.totalAmount = action.payload.total;
        state.totalItems = action.payload.itemCount;
        state.coupon = null;
        state.discount = 0;
      })
      .addCase(removeCouponAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to remove coupon';
      });
  },
});

export const {
  initializeCart,
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  toggleCart,
  setCurrency,
  setError,
  setLoading,
} = cartSlice.actions;



export default cartSlice.reducer;
