import { api, ApiResponse } from './api';
import { Product, FilterParams, PaginationParams } from '../types';

export interface ProductSearchParams extends PaginationParams, FilterParams {
  search?: string;
  sortBy?: 'name' | 'price' | 'rating' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductsResponse {
  products: Product[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export const productService = {
  // Get all products with filters and pagination
  getProducts: async (params: ProductSearchParams = {}): Promise<ApiResponse<ProductsResponse>> => {
    const queryParams = new URLSearchParams();
    
    // Add pagination params
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    
    // Add filter params
    if (params.category) queryParams.append('category', params.category);
    if (params.brand) queryParams.append('brand', params.brand);
    if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());
    if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());
    if (params.rating) queryParams.append('rating', params.rating.toString());
    if (params.inStock !== undefined) queryParams.append('inStock', params.inStock.toString());
    if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());
    if (params.search) queryParams.append('search', params.search);
    
    // Add sorting params
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<ProductsResponse>(url);
  },

  // Get single product by ID
  getProduct: async (id: string): Promise<ApiResponse<Product>> => {
    return api.get<Product>(`/products/${id}`);
  },

  // Get featured products
  getFeaturedProducts: async (limit: number = 8): Promise<ApiResponse<Product[]>> => {
    return api.get<Product[]>(`/products/featured?limit=${limit}`);
  },

  // Get products by category
  getProductsByCategory: async (category: string, params: PaginationParams = {}): Promise<ApiResponse<ProductsResponse>> => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    
    const url = `/products/category/${category}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<ProductsResponse>(url);
  },

  // Search products
  searchProducts: async (query: string, params: ProductSearchParams = {}): Promise<ApiResponse<ProductsResponse>> => {
    const queryParams = new URLSearchParams();
    queryParams.append('search', query);

    // Add pagination params
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    // Add filter params
    if (params.category) queryParams.append('category', params.category);
    if (params.brand) queryParams.append('brand', params.brand);
    if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());
    if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());
    if (params.rating) queryParams.append('rating', params.rating.toString());
    if (params.inStock !== undefined) queryParams.append('inStock', params.inStock.toString());
    if (params.featured !== undefined) queryParams.append('featured', params.featured.toString());

    // Add sorting params
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    return api.get<ProductsResponse>(`/products/search?${queryParams.toString()}`);
  },

  // Get product recommendations
  getRecommendations: async (productId: string, limit: number = 4): Promise<ApiResponse<Product[]>> => {
    return api.get<Product[]>(`/products/${productId}/recommendations?limit=${limit}`);
  },

  // Get related products
  getRelatedProducts: async (productId: string, limit: number = 4): Promise<ApiResponse<Product[]>> => {
    return api.get<Product[]>(`/products/${productId}/related?limit=${limit}`);
  },

  // Check product availability
  checkAvailability: async (productId: string, quantity: number = 1): Promise<ApiResponse<{ available: boolean; stock: number }>> => {
    return api.get<{ available: boolean; stock: number }>(`/products/${productId}/availability?quantity=${quantity}`);
  },

  // Get product variants
  getProductVariants: async (productId: string): Promise<ApiResponse<any[]>> => {
    return api.get<any[]>(`/products/${productId}/variants`);
  },

  // Admin functions (if user has admin role)
  admin: {
    // Create new product
    createProduct: async (productData: Partial<Product>): Promise<ApiResponse<Product>> => {
      return api.post<Product>('/products', productData);
    },

    // Update product
    updateProduct: async (id: string, productData: Partial<Product>): Promise<ApiResponse<Product>> => {
      return api.put<Product>(`/products/${id}`, productData);
    },

    // Delete product
    deleteProduct: async (id: string): Promise<ApiResponse<void>> => {
      return api.delete<void>(`/products/${id}`);
    },

    // Update product stock
    updateStock: async (id: string, quantity: number): Promise<ApiResponse<Product>> => {
      return api.patch<Product>(`/products/${id}/stock`, { quantity });
    },

    // Bulk update products
    bulkUpdate: async (updates: Array<{ id: string; data: Partial<Product> }>): Promise<ApiResponse<Product[]>> => {
      return api.post<Product[]>('/products/bulk-update', { updates });
    },
  },
};

export default productService;
