const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// Test configuration
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Test data
const testData = {
  user: {
    email: '<EMAIL>',
    password: 'password123'
  },
  product: {
    name: 'Test Product',
    description: 'Test product for high priority features',
    price: 99.99,
    category_id: null // Will be set during test
  },
  coupon: {
    code: 'TEST10',
    name: 'Test 10% Off',
    description: 'Test coupon for 10% discount',
    type: 'percentage',
    percentage: 10.00,
    minimum_amount: 50.00,
    usage_limit: 100,
    applicable_to: 'all',
    is_active: true
  },
  order: {
    total_amount: 99.99,
    status: 'completed'
  }
};

class HighPriorityFeaturesTest {
  constructor() {
    this.authToken = null;
    this.testResults = {
      refunds: { passed: 0, failed: 0, tests: [] },
      coupons: { passed: 0, failed: 0, tests: [] },
      variants: { passed: 0, failed: 0, tests: [] },
      logistics: { passed: 0, failed: 0, tests: [] }
    };
  }

  async runTest(testName, testFunction) {
    try {
      console.log(`\n🧪 Running test: ${testName}`);
      await testFunction();
      console.log(`✅ ${testName} - PASSED`);
      return true;
    } catch (error) {
      console.log(`❌ ${testName} - FAILED: ${error.message}`);
      return false;
    }
  }

  async makeRequest(method, endpoint, data = null) {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      ...testConfig
    };

    if (this.authToken) {
      config.headers.Authorization = `Bearer ${this.authToken}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  }

  async testRefundSystem() {
    console.log('\n🔄 Testing Refund System...');

    // Test 1: Get refunds (should return empty array initially)
    const passed1 = await this.runTest('Get refunds list', async () => {
      const response = await this.makeRequest('GET', '/refunds');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 2: Create refund (will fail without valid order, but should validate input)
    const passed2 = await this.runTest('Create refund validation', async () => {
      try {
        await this.makeRequest('POST', '/refunds', {
          order_id: 'invalid-uuid',
          amount: 50.00,
          reason: 'customer_request'
        });
      } catch (error) {
        if (error.response && error.response.status === 400) {
          // Expected validation error
          return;
        }
        throw error;
      }
    });

    this.testResults.refunds.passed = [passed1, passed2].filter(Boolean).length;
    this.testResults.refunds.failed = 2 - this.testResults.refunds.passed;
  }

  async testCouponSystem() {
    console.log('\n🎫 Testing Coupon System...');

    let couponId = null;

    // Test 1: Create coupon
    const passed1 = await this.runTest('Create coupon', async () => {
      const response = await this.makeRequest('POST', '/coupons', testData.coupon);
      if (!response.success || !response.data.id) {
        throw new Error('Failed to create coupon');
      }
      couponId = response.data.id;
    });

    // Test 2: Get coupons list
    const passed2 = await this.runTest('Get coupons list', async () => {
      const response = await this.makeRequest('GET', '/coupons');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 3: Validate coupon
    const passed3 = await this.runTest('Validate coupon', async () => {
      const response = await this.makeRequest('POST', '/coupons/validate', {
        code: testData.coupon.code,
        cart_total: 75.00
      });
      if (!response.success) {
        throw new Error('Coupon validation failed');
      }
    });

    // Test 4: Get coupon by ID
    const passed4 = await this.runTest('Get coupon by ID', async () => {
      if (!couponId) return;
      const response = await this.makeRequest('GET', `/coupons/${couponId}`);
      if (!response.success || response.data.code !== testData.coupon.code) {
        throw new Error('Failed to get coupon by ID');
      }
    });

    this.testResults.coupons.passed = [passed1, passed2, passed3, passed4].filter(Boolean).length;
    this.testResults.coupons.failed = 4 - this.testResults.coupons.passed;
  }

  async testProductVariants() {
    console.log('\n🎨 Testing Product Variants...');

    // Test 1: Get variant attributes
    const passed1 = await this.runTest('Get variant attributes', async () => {
      const response = await this.makeRequest('GET', '/product-variants/attributes');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 2: Get attribute values
    const passed2 = await this.runTest('Get attribute values', async () => {
      const response = await this.makeRequest('GET', '/product-variants/attribute-values');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 3: Create variant (will fail without valid product, but should validate input)
    const passed3 = await this.runTest('Create variant validation', async () => {
      try {
        await this.makeRequest('POST', '/product-variants', {
          product_id: 'invalid-uuid',
          sku: 'TEST-SKU-001',
          price: 99.99,
          inventory_quantity: 10
        });
      } catch (error) {
        if (error.response && error.response.status === 400) {
          // Expected validation error
          return;
        }
        throw error;
      }
    });

    this.testResults.variants.passed = [passed1, passed2, passed3].filter(Boolean).length;
    this.testResults.variants.failed = 3 - this.testResults.variants.passed;
  }

  async testLogisticsSystem() {
    console.log('\n🚚 Testing Logistics System...');

    // Test 1: Get shipping carriers
    const passed1 = await this.runTest('Get shipping carriers', async () => {
      const response = await this.makeRequest('GET', '/logistics/carriers');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 2: Get shipping services
    const passed2 = await this.runTest('Get shipping services', async () => {
      const response = await this.makeRequest('GET', '/logistics/services');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 3: Calculate shipping rates
    const passed3 = await this.runTest('Calculate shipping rates', async () => {
      const response = await this.makeRequest('POST', '/logistics/calculate-rates', {
        origin: {
          country: 'US',
          state: 'CA',
          city: 'Los Angeles',
          postal_code: '90210'
        },
        destination: {
          country: 'US',
          state: 'NY',
          city: 'New York',
          postal_code: '10001'
        },
        packages: [{
          weight: 2.5,
          dimensions: {
            length: 10,
            width: 8,
            height: 6
          }
        }]
      });
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    // Test 4: Get shipments (should return empty array initially)
    const passed4 = await this.runTest('Get shipments list', async () => {
      const response = await this.makeRequest('GET', '/logistics/shipments');
      if (!Array.isArray(response.data)) {
        throw new Error('Response should be an array');
      }
    });

    this.testResults.logistics.passed = [passed1, passed2, passed3, passed4].filter(Boolean).length;
    this.testResults.logistics.failed = 4 - this.testResults.logistics.passed;
  }

  async runAllTests() {
    console.log('🚀 Starting High Priority Features Test Suite...');
    console.log(`📍 Testing against: ${BASE_URL}`);

    try {
      await this.testRefundSystem();
      await this.testCouponSystem();
      await this.testProductVariants();
      await this.testLogisticsSystem();

      this.printResults();
    } catch (error) {
      console.error('\n💥 Test suite failed:', error.message);
    }
  }

  printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));

    const categories = ['refunds', 'coupons', 'variants', 'logistics'];
    let totalPassed = 0;
    let totalFailed = 0;

    categories.forEach(category => {
      const result = this.testResults[category];
      totalPassed += result.passed;
      totalFailed += result.failed;
      
      const total = result.passed + result.failed;
      const percentage = total > 0 ? ((result.passed / total) * 100).toFixed(1) : '0.0';
      
      console.log(`${category.toUpperCase().padEnd(12)} | ${result.passed}/${total} passed (${percentage}%)`);
    });

    console.log('=' .repeat(50));
    const overallTotal = totalPassed + totalFailed;
    const overallPercentage = overallTotal > 0 ? ((totalPassed / overallTotal) * 100).toFixed(1) : '0.0';
    console.log(`OVERALL      | ${totalPassed}/${overallTotal} passed (${overallPercentage}%)`);

    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! High priority features are working correctly.');
    } else {
      console.log(`\n⚠️  ${totalFailed} test(s) failed. Please check the server logs and ensure all services are running.`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new HighPriorityFeaturesTest();
  tester.runAllTests().catch(console.error);
}

module.exports = HighPriorityFeaturesTest;
