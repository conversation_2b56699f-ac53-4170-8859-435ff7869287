'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Create Product Variant tables
      await queryInterface.createTable('VariantAttributes', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        name: {
          type: Sequelize.STRING(100),
          allowNull: false
        },
        display_name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        type: {
          type: Sequelize.ENUM('text', 'color', 'size', 'number', 'boolean'),
          allowNull: false,
          defaultValue: 'text'
        },
        is_required: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('VariantAttributeValues', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        attribute_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'variant_attributes',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        value: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        display_value: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        color_code: {
          type: Sequelize.STRING(7),
          allowNull: true
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('ProductVariants', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        product_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'products',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        sku: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true
        },
        barcode: {
          type: Sequelize.STRING(100),
          allowNull: true
        },
        price: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        compare_at_price: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        cost_price: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        weight: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: true
        },
        dimensions: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        inventory_quantity: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        inventory_policy: {
          type: Sequelize.ENUM('deny', 'continue'),
          allowNull: false,
          defaultValue: 'deny'
        },
        inventory_tracked: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        low_stock_threshold: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        image_url: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('ProductVariantAttributeValues', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        variant_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'product_variants',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        attribute_value_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'variant_attribute_values',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('InventoryLogs', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        variant_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'product_variants',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        change_type: {
          type: Sequelize.ENUM('sale', 'restock', 'adjustment', 'return', 'damage', 'transfer'),
          allowNull: false
        },
        quantity_change: {
          type: Sequelize.INTEGER,
          allowNull: false
        },
        quantity_before: {
          type: Sequelize.INTEGER,
          allowNull: false
        },
        quantity_after: {
          type: Sequelize.INTEGER,
          allowNull: false
        },
        reference_type: {
          type: Sequelize.ENUM('order', 'manual', 'system', 'return', 'transfer'),
          allowNull: true
        },
        reference_id: {
          type: Sequelize.UUID,
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        created_by: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL'
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // Create Logistics tables
      await queryInterface.createTable('ShippingCarriers', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        code: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true
        },
        display_name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        logo_url: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        website_url: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        tracking_url_template: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        api_endpoint: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        api_credentials: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        supported_countries: {
          type: Sequelize.JSONB,
          allowNull: false,
          defaultValue: []
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('ShippingServices', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        carrier_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_carriers',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        code: {
          type: Sequelize.STRING(50),
          allowNull: false
        },
        display_name: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        service_type: {
          type: Sequelize.ENUM('standard', 'express', 'overnight', 'economy', 'priority'),
          allowNull: false,
          defaultValue: 'standard'
        },
        delivery_time_min: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        delivery_time_max: {
          type: Sequelize.INTEGER,
          allowNull: true
        },
        rate_calculation: {
          type: Sequelize.ENUM('flat', 'weight_based', 'dimension_based', 'api_calculated'),
          allowNull: false,
          defaultValue: 'flat'
        },
        base_rate: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true
        },
        max_weight: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: true
        },
        max_dimensions: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        is_tracked: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        is_insured: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Drop tables in reverse order
      await queryInterface.dropTable('ShippingServices', { transaction });
      await queryInterface.dropTable('ShippingCarriers', { transaction });
      
      await queryInterface.dropTable('InventoryLogs', { transaction });
      await queryInterface.dropTable('ProductVariantAttributeValues', { transaction });
      await queryInterface.dropTable('ProductVariants', { transaction });
      await queryInterface.dropTable('VariantAttributeValues', { transaction });
      await queryInterface.dropTable('VariantAttributes', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
