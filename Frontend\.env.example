# API Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_API_TIMEOUT=30000

# App Configuration
REACT_APP_NAME=Cross-Border E-commerce Platform
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Payment Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
REACT_APP_PAYPAL_CLIENT_ID=your_paypal_client_id

# Google Services
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
REACT_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Social Login
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
REACT_APP_FACEBOOK_APP_ID=your_facebook_app_id

# CDN and Storage
REACT_APP_CDN_URL=https://your-cdn-domain.com
REACT_APP_UPLOAD_URL=http://localhost:5000/api/v1/upload

# Feature Flags
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_CHAT_SUPPORT=false
REACT_APP_ENABLE_DARK_MODE=true

# Currency and Localization
REACT_APP_DEFAULT_CURRENCY=USD
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_SUPPORTED_CURRENCIES=USD,EUR,GBP,CNY,JPY
REACT_APP_SUPPORTED_LANGUAGES=en,zh

# External Services
REACT_APP_CURRENCY_API_URL=https://api.exchangerate-api.com/v4/latest
REACT_APP_SHIPPING_CALCULATOR_URL=http://localhost:5000/api/v1/shipping/calculate

# Development
REACT_APP_DEBUG=true
REACT_APP_MOCK_API=false
GENERATE_SOURCEMAP=true

# Build Configuration
REACT_APP_BUILD_VERSION=$npm_package_version
REACT_APP_BUILD_DATE=$BUILD_DATE
