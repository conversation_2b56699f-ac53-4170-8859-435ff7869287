const { Sequelize } = require('sequelize');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔍 NexaShop System Comprehensive Test');
console.log('=====================================');

// Test results storage
const testResults = {
  database: { status: 'pending', details: null },
  sharp: { status: 'pending', details: null },
  models: { status: 'pending', details: null },
  routes: { status: 'pending', details: null },
  services: { status: 'pending', details: null },
  uploads: { status: 'pending', details: null },
  environment: { status: 'pending', details: null }
};

// 1. Test Environment Configuration
async function testEnvironment() {
  console.log('\n📋 Testing Environment Configuration...');

  try {
    const requiredEnvVars = [
      'NODE_ENV', 'PORT', 'DB_HOST', 'DB_PORT', 'DB_NAME',
      'DB_USER', 'DB_PASSWORD', 'JWT_SECRET'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    const presentVars = requiredEnvVars.filter(varName => process.env[varName]);

    if (missingVars.length > 0) {
      testResults.environment = {
        status: 'warning',
        details: `Missing: ${missingVars.join(', ')} | Present: ${presentVars.length}/${requiredEnvVars.length}`
      };
    } else {
      testResults.environment = {
        status: 'success',
        details: `All ${requiredEnvVars.length} required environment variables are set`
      };
    }

    console.log(`✅ Environment: ${testResults.environment.status}`);
    if (testResults.environment.details) {
      console.log(`   ${testResults.environment.details}`);
    }
  } catch (error) {
    testResults.environment = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Environment test failed: ${error.message}`);
  }
}

// 2. Test Database Connection
async function testDatabase() {
  console.log('\n🗄️ Testing Database Connection...');

  try {
    const config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 5432,
      database: process.env.DB_NAME || 'nexashop_dev',
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'wasd080980!',
      dialect: 'postgres',
      logging: false
    };

    const sequelize = new Sequelize(config.database, config.username, config.password, {
      host: config.host,
      port: config.port,
      dialect: config.dialect,
      logging: config.logging
    });
    
    await sequelize.authenticate();
    
    // Test a simple query
    const result = await sequelize.query('SELECT version();');
    const version = result[0][0].version;
    
    await sequelize.close();
    
    testResults.database = {
      status: 'success',
      details: `Connected to PostgreSQL: ${version.split(' ')[1]}`
    };
    
    console.log(`✅ Database: ${testResults.database.details}`);
  } catch (error) {
    testResults.database = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Database connection failed: ${error.message}`);
  }
}

// 3. Test Sharp Package
async function testSharp() {
  console.log('\n🖼️ Testing Sharp Package...');
  
  try {
    const sharp = require('sharp');
    
    // Test basic Sharp functionality
    const testImage = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    }).png().toBuffer();
    
    const metadata = await sharp(testImage).metadata();
    
    testResults.sharp = {
      status: 'success',
      details: `Sharp v${sharp.versions.sharp} - Test image: ${metadata.width}x${metadata.height} ${metadata.format}`
    };
    
    console.log(`✅ Sharp: ${testResults.sharp.details}`);
  } catch (error) {
    testResults.sharp = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Sharp test failed: ${error.message}`);
  }
}

// 4. Test Models Loading
async function testModels() {
  console.log('\n📊 Testing Models...');
  
  try {
    const modelsPath = path.join(__dirname, 'src/models');
    const modelFiles = fs.readdirSync(modelsPath).filter(file => 
      file.endsWith('.js') && file !== 'index.js'
    );
    
    // Try to load models index
    const models = require('./src/models');
    
    const loadedModels = Object.keys(models).filter(key => 
      key !== 'sequelize' && typeof models[key] === 'function'
    );
    
    testResults.models = {
      status: 'success',
      details: `Loaded ${loadedModels.length} models: ${loadedModels.slice(0, 5).join(', ')}${loadedModels.length > 5 ? '...' : ''}`
    };
    
    console.log(`✅ Models: ${testResults.models.details}`);
  } catch (error) {
    testResults.models = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Models loading failed: ${error.message}`);
  }
}

// 5. Test Routes
async function testRoutes() {
  console.log('\n🛣️ Testing Routes...');
  
  try {
    const routesPath = path.join(__dirname, 'src/routes');
    const routeFiles = fs.readdirSync(routesPath).filter(file => file.endsWith('.js'));
    
    const routeTests = [];
    
    for (const file of routeFiles) {
      try {
        const routePath = path.join(routesPath, file);
        require(routePath);
        routeTests.push({ file, status: 'success' });
      } catch (error) {
        routeTests.push({ file, status: 'error', error: error.message });
      }
    }
    
    const successCount = routeTests.filter(t => t.status === 'success').length;
    const errorCount = routeTests.filter(t => t.status === 'error').length;
    
    if (errorCount === 0) {
      testResults.routes = {
        status: 'success',
        details: `All ${successCount} route files loaded successfully`
      };
    } else {
      testResults.routes = {
        status: 'warning',
        details: `${successCount} routes loaded, ${errorCount} failed`
      };
    }
    
    console.log(`✅ Routes: ${testResults.routes.details}`);
    
    if (errorCount > 0) {
      const errorRoutes = routeTests.filter(t => t.status === 'error');
      errorRoutes.forEach(route => {
        console.log(`   ❌ ${route.file}: ${route.error}`);
      });
    }
  } catch (error) {
    testResults.routes = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Routes test failed: ${error.message}`);
  }
}

// 6. Test Services
async function testServices() {
  console.log('\n🔧 Testing Services...');
  
  try {
    const servicesPath = path.join(__dirname, 'src/services');
    const serviceFiles = fs.readdirSync(servicesPath).filter(file => file.endsWith('.js'));
    
    const serviceTests = [];
    
    for (const file of serviceFiles) {
      try {
        const servicePath = path.join(servicesPath, file);
        require(servicePath);
        serviceTests.push({ file, status: 'success' });
      } catch (error) {
        serviceTests.push({ file, status: 'error', error: error.message });
      }
    }
    
    const successCount = serviceTests.filter(t => t.status === 'success').length;
    const errorCount = serviceTests.filter(t => t.status === 'error').length;
    
    if (errorCount === 0) {
      testResults.services = {
        status: 'success',
        details: `All ${successCount} service files loaded successfully`
      };
    } else {
      testResults.services = {
        status: 'warning',
        details: `${successCount} services loaded, ${errorCount} failed`
      };
    }
    
    console.log(`✅ Services: ${testResults.services.details}`);
    
    if (errorCount > 0) {
      const errorServices = serviceTests.filter(t => t.status === 'error');
      errorServices.forEach(service => {
        console.log(`   ❌ ${service.file}: ${service.error}`);
      });
    }
  } catch (error) {
    testResults.services = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Services test failed: ${error.message}`);
  }
}

// 7. Test Upload Directories
async function testUploads() {
  console.log('\n📁 Testing Upload System...');
  
  try {
    const uploadDirs = [
      'uploads',
      'uploads/temp',
      'uploads/images',
      'uploads/images/thumbnails',
      'uploads/images/small',
      'uploads/images/medium',
      'uploads/images/large',
      'uploads/documents'
    ];
    
    const dirStatus = {};
    
    uploadDirs.forEach(dir => {
      const fullPath = path.join(__dirname, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        dirStatus[dir] = 'created';
      } else {
        dirStatus[dir] = 'exists';
      }
    });
    
    testResults.uploads = {
      status: 'success',
      details: `Upload directories ready: ${Object.keys(dirStatus).length} directories`
    };
    
    console.log(`✅ Uploads: ${testResults.uploads.details}`);
  } catch (error) {
    testResults.uploads = {
      status: 'error',
      details: error.message
    };
    console.log(`❌ Upload system test failed: ${error.message}`);
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting comprehensive system test...\n');
  
  await testEnvironment();
  await testDatabase();
  await testSharp();
  await testModels();
  await testRoutes();
  await testServices();
  await testUploads();
  
  // Summary
  console.log('\n📊 Test Summary');
  console.log('===============');
  
  const categories = Object.keys(testResults);
  const successCount = categories.filter(cat => testResults[cat].status === 'success').length;
  const warningCount = categories.filter(cat => testResults[cat].status === 'warning').length;
  const errorCount = categories.filter(cat => testResults[cat].status === 'error').length;
  
  console.log(`✅ Success: ${successCount}`);
  console.log(`⚠️  Warning: ${warningCount}`);
  console.log(`❌ Error: ${errorCount}`);
  
  if (errorCount === 0 && warningCount === 0) {
    console.log('\n🎉 All systems are functioning correctly!');
  } else if (errorCount === 0) {
    console.log('\n✅ System is functional with minor warnings.');
  } else {
    console.log('\n⚠️ System has critical issues that need attention.');
  }
  
  return testResults;
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testResults };
