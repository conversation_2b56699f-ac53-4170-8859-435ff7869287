{"development": {"username": "postgres", "password": "wasd080980!", "database": "nexashop_dev", "host": "127.0.0.1", "port": 5432, "dialect": "postgres", "logging": false, "pool": {"max": 5, "min": 0, "acquire": 30000, "idle": 10000}, "migrations-path": "src/migrations", "seeders-path": "src/seeders", "models-path": "src/models"}, "test": {"username": "postgres", "password": "wasd080980!", "database": "nexashop_test", "host": "127.0.0.1", "port": 5432, "dialect": "postgres", "logging": false}, "production": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}, "logging": false, "pool": {"max": 10, "min": 2, "acquire": 30000, "idle": 10000}}}