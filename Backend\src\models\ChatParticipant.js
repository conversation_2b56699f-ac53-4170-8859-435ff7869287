module.exports = (sequelize, DataTypes) => {
  const ChatParticipant = sequelize.define('ChatParticipant', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    chat_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'chats',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    role: {
      type: DataTypes.STRING(20),
      allowNull: false,
      validate: {
        isIn: [['customer', 'agent', 'supervisor', 'observer']]
      },
      comment: 'Role of the participant in the chat'
    },
    status: {
      type: DataTypes.STRING(20),
      defaultValue: 'active',
      allowNull: false,
      validate: {
        isIn: [['active', 'inactive', 'left', 'removed']]
      }
    },
    permissions: {
      type: DataTypes.JSONB,
      defaultValue: {
        can_send_messages: true,
        can_send_files: true,
        can_view_history: true,
        can_close_chat: false,
        can_transfer_chat: false
      },
      comment: 'Participant permissions'
    },
    joined_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    left_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_seen_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_read_message_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'chat_messages',
        key: 'id'
      },
      comment: 'Last message read by this participant'
    },
    notification_settings: {
      type: DataTypes.JSONB,
      defaultValue: {
        email_notifications: true,
        push_notifications: true,
        sound_notifications: true
      },
      comment: 'Notification preferences for this chat'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'chat_participants',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['chat_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['role']
      },
      {
        fields: ['status']
      },
      {
        unique: true,
        fields: ['chat_id', 'user_id']
      }
    ]
  });

  return ChatParticipant;
};
