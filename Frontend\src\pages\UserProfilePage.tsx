import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Typography,
  Button,
  Space,
  Tabs,
  Form,
  Input,
  Upload,
  Avatar,
  Row,
  Col,
  Divider,
  message,
  Modal,
  Select,
  DatePicker,
  Switch,
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  LockOutlined,
  UploadOutlined,
  CameraOutlined,
  SettingOutlined,
  EnvironmentOutlined,
  ShoppingOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../store';
import { fetchAddresses } from '../store/slices/addressSlice';
import { fetchOrders } from '../store/slices/orderSlice';
import AddressList from '../components/AddressList/AddressList';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { confirm } = Modal;

const UserProfilePage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { addresses } = useSelector((state: RootState) => state.addresses);
  const { orders } = useSelector((state: RootState) => state.orders);

  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || '');
  const [preferences, setPreferences] = useState({
    language: 'en',
    currency: 'USD',
    notifications: {
      email: true,
      sms: false,
      push: true,
      orderUpdates: true,
      promotions: false,
    },
    privacy: {
      showProfile: true,
      showOrders: false,
      showWishlist: true,
    },
  });

  useEffect(() => {
    dispatch(fetchAddresses());
    dispatch(fetchOrders({ page: 1, limit: 5 }));

    // Initialize form with user data
    if (user) {
      profileForm.setFieldsValue({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phone: user.phone || '',
        dateOfBirth: user.dateOfBirth ? dayjs(user.dateOfBirth) : null,
        gender: user.gender || '',
        bio: user.bio || '',
      });
    }
  }, [dispatch, user, profileForm]);

  const handleProfileUpdate = async (values: any) => {
    try {
      // In real app, call API to update user profile
      console.log('Updating profile:', values);
      message.success('Profile updated successfully!');
      setIsEditingProfile(false);
    } catch (error: any) {
      message.error(error.message || 'Failed to update profile');
    }
  };

  const handlePasswordChange = async (values: any) => {
    try {
      // In real app, call API to change password
      console.log('Changing password');
      message.success('Password changed successfully!');
      setIsChangingPassword(false);
      passwordForm.resetFields();
    } catch (error: any) {
      message.error(error.message || 'Failed to change password');
    }
  };

  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      // In real app, get URL from server response
      const newAvatarUrl = URL.createObjectURL(info.file.originFileObj);
      setAvatarUrl(newAvatarUrl);
      message.success('Avatar updated successfully!');
    } else if (info.file.status === 'error') {
      message.error('Failed to upload avatar');
    }
  };

  const handleDeleteAccount = () => {
    confirm({
      title: 'Delete Account',
      content: 'Are you sure you want to delete your account? This action cannot be undone.',
      okText: 'Delete Account',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          // In real app, call API to delete account
          message.success('Account deletion request submitted');
        } catch (error: any) {
          message.error(error.message || 'Failed to delete account');
        }
      },
    });
  };

  const renderProfileTab = () => (
    <Card>
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <div style={{ position: 'relative', display: 'inline-block' }}>
          <Avatar
            size={120}
            src={avatarUrl}
            icon={<UserOutlined />}
            style={{ marginBottom: '16px' }}
          />
          <Upload
            name="avatar"
            showUploadList={false}
            beforeUpload={() => false}
            onChange={handleAvatarUpload}
            accept="image/*"
          >
            <Button
              type="primary"
              shape="circle"
              icon={<CameraOutlined />}
              size="small"
              style={{
                position: 'absolute',
                bottom: '16px',
                right: '0px',
                background: '#1890ff',
                border: '2px solid white',
              }}
            />
          </Upload>
        </div>
        <div>
          <Title level={3} style={{ margin: '8px 0 4px 0' }}>
            {user?.firstName} {user?.lastName}
          </Title>
          <Text type="secondary">{user?.email}</Text>
        </div>
      </div>

      <Divider />

      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={4} style={{ margin: 0 }}>Personal Information</Title>
        <Button
          type={isEditingProfile ? 'default' : 'primary'}
          icon={<EditOutlined />}
          onClick={() => setIsEditingProfile(!isEditingProfile)}
        >
          {isEditingProfile ? 'Cancel' : 'Edit Profile'}
        </Button>
      </div>

      <Form
        form={profileForm}
        layout="vertical"
        onFinish={handleProfileUpdate}
        disabled={!isEditingProfile}
      >
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="firstName"
              label="First Name"
              rules={[{ required: true, message: 'Please enter your first name' }]}
            >
              <Input prefix={<UserOutlined />} placeholder="Enter first name" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="lastName"
              label="Last Name"
              rules={[{ required: true, message: 'Please enter your last name' }]}
            >
              <Input prefix={<UserOutlined />} placeholder="Enter last name" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
            >
              <Input prefix={<MailOutlined />} placeholder="Enter email" />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="phone"
              label="Phone Number"
              rules={[{ pattern: /^[+]?[\d\s\-\(\)]+$/, message: 'Please enter valid phone number' }]}
            >
              <Input prefix={<PhoneOutlined />} placeholder="Enter phone number" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <Form.Item name="dateOfBirth" label="Date of Birth">
              <DatePicker
                style={{ width: '100%' }}
                placeholder="Select date of birth"
                prefix={<CalendarOutlined />}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item name="gender" label="Gender">
              <Select placeholder="Select gender">
                <Option value="male">Male</Option>
                <Option value="female">Female</Option>
                <Option value="other">Other</Option>
                <Option value="prefer-not-to-say">Prefer not to say</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="bio" label="Bio">
          <Input.TextArea
            rows={3}
            placeholder="Tell us about yourself..."
            maxLength={500}
            showCount
          />
        </Form.Item>

        {isEditingProfile && (
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                style={{
                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                  border: 'none',
                }}
              >
                Save Changes
              </Button>
              <Button onClick={() => setIsEditingProfile(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        )}
      </Form>
    </Card>
  );

  const renderSecurityTab = () => (
    <Card>
      <Title level={4} style={{ marginBottom: '24px' }}>
        <LockOutlined style={{ marginRight: '8px' }} />
        Security Settings
      </Title>

      <div style={{ marginBottom: '32px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div>
            <Text strong>Password</Text>
            <br />
            <Text type="secondary">Last changed 30 days ago</Text>
          </div>
          <Button
            type={isChangingPassword ? 'default' : 'primary'}
            onClick={() => setIsChangingPassword(!isChangingPassword)}
          >
            {isChangingPassword ? 'Cancel' : 'Change Password'}
          </Button>
        </div>

        {isChangingPassword && (
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handlePasswordChange}
            style={{ marginTop: '16px', padding: '16px', background: '#fafafa', borderRadius: '8px' }}
          >
            <Form.Item
              name="currentPassword"
              label="Current Password"
              rules={[{ required: true, message: 'Please enter your current password' }]}
            >
              <Input.Password placeholder="Enter current password" />
            </Form.Item>
            <Form.Item
              name="newPassword"
              label="New Password"
              rules={[
                { required: true, message: 'Please enter new password' },
                { min: 8, message: 'Password must be at least 8 characters' }
              ]}
            >
              <Input.Password placeholder="Enter new password" />
            </Form.Item>
            <Form.Item
              name="confirmPassword"
              label="Confirm New Password"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Please confirm your new password' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Passwords do not match'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder="Confirm new password" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  Update Password
                </Button>
                <Button onClick={() => setIsChangingPassword(false)}>
                  Cancel
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </div>

      <Divider />

      <div style={{ marginBottom: '32px' }}>
        <Text strong style={{ color: '#ff4d4f' }}>Danger Zone</Text>
        <div style={{ marginTop: '16px', padding: '16px', border: '1px solid #ff4d4f', borderRadius: '8px' }}>
          <div style={{ marginBottom: '12px' }}>
            <Text strong>Delete Account</Text>
            <br />
            <Text type="secondary">
              Once you delete your account, there is no going back. Please be certain.
            </Text>
          </div>
          <Button danger onClick={handleDeleteAccount}>
            Delete Account
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: '32px' }}>
          <UserOutlined style={{ marginRight: '12px' }} />
          My Account
        </Title>

        <Tabs defaultActiveKey="profile" size="large">
          <TabPane
            tab={
              <span>
                <UserOutlined />
                Profile
              </span>
            }
            key="profile"
          >
            {renderProfileTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <EnvironmentOutlined />
                Addresses
              </span>
            }
            key="addresses"
          >
            <Card>
              <Title level={4} style={{ marginBottom: '24px' }}>
                <EnvironmentOutlined style={{ marginRight: '8px' }} />
                Manage Addresses
              </Title>
              <AddressList showActions />
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <ShoppingOutlined />
                Orders
              </span>
            }
            key="orders"
          >
            <Card>
              <Title level={4} style={{ marginBottom: '24px' }}>
                <ShoppingOutlined style={{ marginRight: '8px' }} />
                Recent Orders
              </Title>
              {orders.length > 0 ? (
                <div>
                  {orders.slice(0, 5).map((order) => (
                    <div key={order.id} style={{
                      padding: '16px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '8px',
                      marginBottom: '16px'
                    }}>
                      <Row justify="space-between" align="middle">
                        <Col>
                          <Text strong>{order.orderNumber}</Text>
                          <br />
                          <Text type="secondary">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </Text>
                        </Col>
                        <Col>
                          <Text strong>${order.totalAmount.toFixed(2)}</Text>
                        </Col>
                      </Row>
                    </div>
                  ))}
                  <div style={{ textAlign: 'center', marginTop: '24px' }}>
                    <Button type="primary">View All Orders</Button>
                  </div>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '48px' }}>
                  <Text type="secondary">No orders found</Text>
                </div>
              )}
            </Card>
          </TabPane>

          <TabPane
            tab={
              <span>
                <LockOutlined />
                Security
              </span>
            }
            key="security"
          >
            {renderSecurityTab()}
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default UserProfilePage;
