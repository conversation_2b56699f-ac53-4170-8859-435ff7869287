const { Server } = require('socket.io');
const jwtService = require('../utils/jwt');
const { User } = require('../models');
const { socketConfig } = require('../config/notifications');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId mapping
  }

  /**
   * Initialize Socket.IO server
   * @param {Object} server - HTTP server instance
   */
  initialize(server) {
    this.io = new Server(server, socketConfig);

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwtService.verifyAccessToken(token);
        const user = await User.findByPk(decoded.id);

        if (!user || user.status !== 'active') {
          return next(new Error('Invalid user'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    console.log('✅ Socket.IO service initialized');
    return this.io;
  }

  /**
   * Handle new socket connection
   * @param {Object} socket - Socket instance
   */
  handleConnection(socket) {
    const userId = socket.userId;
    const user = socket.user;

    console.log(`🔌 User ${user.email} connected (${socket.id})`);

    // Join user to their personal room
    socket.join(`user_${userId}`);

    // Store connection
    this.connectedUsers.set(userId, socket.id);

    // Handle events
    this.setupEventHandlers(socket);

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to NexaShop notifications',
      userId: userId,
      timestamp: new Date().toISOString()
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`🔌 User ${user.email} disconnected (${socket.id})`);
      this.connectedUsers.delete(userId);
    });
  }

  /**
   * Setup event handlers for socket
   * @param {Object} socket - Socket instance
   */
  setupEventHandlers(socket) {
    const userId = socket.userId;

    // Join specific rooms
    socket.on('join_room', (roomName) => {
      if (this.isValidRoom(roomName, userId)) {
        socket.join(roomName);
        socket.emit('joined_room', { room: roomName });
      }
    });

    // Leave specific rooms
    socket.on('leave_room', (roomName) => {
      socket.leave(roomName);
      socket.emit('left_room', { room: roomName });
    });

    // Mark notification as read
    socket.on('mark_notification_read', async (data) => {
      try {
        const { notificationId } = data;
        const notificationService = require('./notificationService');
        
        const result = await notificationService.markAsRead(notificationId, userId);
        
        if (result.success) {
          socket.emit('notification_marked_read', {
            notificationId,
            unreadCount: await notificationService.getUnreadCount(userId)
          });
        }
      } catch (error) {
        console.error('Mark notification read error:', error);
        socket.emit('error', { message: 'Failed to mark notification as read' });
      }
    });

    // Get unread count
    socket.on('get_unread_count', async () => {
      try {
        const notificationService = require('./notificationService');
        const unreadCount = await notificationService.getUnreadCount(userId);
        
        socket.emit('unread_count', { count: unreadCount });
      } catch (error) {
        console.error('Get unread count error:', error);
        socket.emit('error', { message: 'Failed to get unread count' });
      }
    });

    // Chat functionality
    socket.on('join_chat', async (data) => {
      try {
        const { chatId } = data;

        // Verify user is participant in this chat
        const { ChatParticipant } = require('../models');
        const participant = await ChatParticipant.findOne({
          where: {
            chat_id: chatId,
            user_id: userId,
            status: 'active'
          }
        });

        if (participant) {
          socket.join(`chat_${chatId}`);
          socket.emit('joined_chat', { chatId, timestamp: new Date().toISOString() });

          // Notify other participants that user joined
          socket.to(`chat_${chatId}`).emit('user_joined_chat', {
            userId: userId,
            userName: `${socket.user.first_name} ${socket.user.last_name}`,
            chatId,
            timestamp: new Date().toISOString()
          });
        } else {
          socket.emit('error', { message: 'Access denied to this chat' });
        }
      } catch (error) {
        console.error('Join chat error:', error);
        socket.emit('error', { message: 'Failed to join chat' });
      }
    });

    socket.on('leave_chat', (data) => {
      const { chatId } = data;
      socket.leave(`chat_${chatId}`);
      socket.to(`chat_${chatId}`).emit('user_left_chat', {
        userId: userId,
        userName: `${socket.user.first_name} ${socket.user.last_name}`,
        chatId,
        timestamp: new Date().toISOString()
      });
    });

    // Typing indicators for chat
    socket.on('typing_start', (data) => {
      const { chatId } = data;
      socket.to(`chat_${chatId}`).emit('user_typing', {
        userId: userId,
        userName: socket.user.first_name,
        chatId,
        timestamp: new Date().toISOString()
      });
    });

    socket.on('typing_stop', (data) => {
      const { chatId } = data;
      socket.to(`chat_${chatId}`).emit('user_stopped_typing', {
        userId: userId,
        chatId,
        timestamp: new Date().toISOString()
      });
    });

    // Message delivery confirmation
    socket.on('message_delivered', async (data) => {
      try {
        const { messageId, chatId } = data;

        // Update message delivery status
        const { ChatMessage } = require('../models');
        await ChatMessage.update(
          { delivery_status: 'delivered' },
          { where: { id: messageId } }
        );

        // Notify sender
        socket.to(`chat_${chatId}`).emit('message_delivery_confirmed', {
          messageId,
          status: 'delivered',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Message delivered error:', error);
      }
    });

    // Message read confirmation
    socket.on('message_read', async (data) => {
      try {
        const { messageId, chatId } = data;

        // Update message read status
        const { ChatMessage } = require('../models');
        await ChatMessage.update(
          {
            delivery_status: 'read',
            is_read: true,
            read_at: new Date()
          },
          { where: { id: messageId } }
        );

        // Notify sender
        socket.to(`chat_${chatId}`).emit('message_read_confirmed', {
          messageId,
          status: 'read',
          readBy: userId,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Message read error:', error);
      }
    });

    // Agent status updates (for agents only)
    if (socket.user.role === 'agent' || socket.user.role === 'admin') {
      socket.join('agents');

      socket.on('update_agent_status', async (data) => {
        try {
          const { status, availability, statusMessage } = data;

          const { AgentStatus } = require('../models');
          await AgentStatus.upsert({
            agent_id: userId,
            status: status || 'online',
            availability: availability !== undefined ? availability : true,
            status_message: statusMessage,
            last_activity_at: new Date()
          });

          // Notify other agents
          socket.to('agents').emit('agent_status_updated', {
            agentId: userId,
            status,
            availability,
            statusMessage,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Update agent status error:', error);
          socket.emit('error', { message: 'Failed to update status' });
        }
      });
    }

    // Heartbeat for connection monitoring
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: new Date().toISOString() });
    });
  }

  /**
   * Validate if user can join a room
   * @param {string} roomName - Room name
   * @param {string} userId - User ID
   * @returns {boolean} Is valid
   */
  isValidRoom(roomName, userId) {
    // User's personal room
    if (roomName === `user_${userId}`) {
      return true;
    }

    // Order-specific rooms (user can only join their own order rooms)
    if (roomName.startsWith('order_')) {
      // In a real implementation, verify the user owns this order
      return true;
    }

    // Admin rooms (only for admin users)
    if (roomName.startsWith('admin_')) {
      // Check if user is admin
      return false; // For now, disable admin rooms
    }

    // Public announcement room
    if (roomName === 'announcements') {
      return true;
    }

    return false;
  }

  /**
   * Send notification to specific user
   * @param {string} userId - User ID
   * @param {Object} notification - Notification data
   */
  sendToUser(userId, notification) {
    if (this.io) {
      this.io.to(`user_${userId}`).emit('notification', notification);
    }
  }

  /**
   * Send notification to multiple users
   * @param {Array} userIds - Array of user IDs
   * @param {Object} notification - Notification data
   */
  sendToUsers(userIds, notification) {
    if (this.io) {
      userIds.forEach(userId => {
        this.io.to(`user_${userId}`).emit('notification', notification);
      });
    }
  }

  /**
   * Broadcast to all connected users
   * @param {Object} data - Data to broadcast
   */
  broadcast(data) {
    if (this.io) {
      this.io.emit('broadcast', data);
    }
  }

  /**
   * Send to specific room
   * @param {string} room - Room name
   * @param {string} event - Event name
   * @param {Object} data - Data to send
   */
  sendToRoom(room, event, data) {
    if (this.io) {
      this.io.to(room).emit(event, data);
    }
  }

  /**
   * Get connected users count
   * @returns {number} Connected users count
   */
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  /**
   * Check if user is online
   * @param {string} userId - User ID
   * @returns {boolean} Is online
   */
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  /**
   * Get online users list
   * @returns {Array} Array of online user IDs
   */
  getOnlineUsers() {
    return Array.from(this.connectedUsers.keys());
  }

  /**
   * Disconnect user
   * @param {string} userId - User ID
   */
  disconnectUser(userId) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId && this.io) {
      const socket = this.io.sockets.sockets.get(socketId);
      if (socket) {
        socket.disconnect(true);
      }
    }
  }

  /**
   * Send system announcement
   * @param {Object} announcement - Announcement data
   */
  sendSystemAnnouncement(announcement) {
    if (this.io) {
      this.io.emit('system_announcement', {
        ...announcement,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Send order update to user
   * @param {string} userId - User ID
   * @param {Object} orderUpdate - Order update data
   */
  sendOrderUpdate(userId, orderUpdate) {
    this.sendToUser(userId, {
      type: 'order_update',
      title: 'Order Status Updated',
      message: `Your order ${orderUpdate.orderNumber} status has been updated to ${orderUpdate.status}`,
      data: orderUpdate,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send payment confirmation
   * @param {string} userId - User ID
   * @param {Object} paymentData - Payment data
   */
  sendPaymentConfirmation(userId, paymentData) {
    this.sendToUser(userId, {
      type: 'payment_confirmation',
      title: 'Payment Confirmed',
      message: `Your payment of $${paymentData.amount} has been confirmed`,
      data: paymentData,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get Socket.IO instance
   * @returns {Object} Socket.IO instance
   */
  getIO() {
    return this.io;
  }
}

module.exports = new SocketService();
