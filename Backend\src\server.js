const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const dotenv = require('dotenv');
const http = require('http');
const { connectDB } = require('./config/database');
const { errorHandler, notFound } = require('./middleware/errorHandler');
const socketService = require('./services/socketService');
const notificationService = require('./services/notificationService');
const cronService = require('./services/cronService');
const { securityConfig } = require('./config/security');
const {
  sanitizeInput,
  detectSQLInjection,
  detectXSS,
  validateRequestSize
} = require('./middleware/validation');
const { initI18n, i18nMiddleware } = require('./config/i18n');
const localizationService = require('./services/localizationService');
const currencyService = require('./services/currencyService');

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

const app = express();

// Security middleware
app.use(helmet());
app.use(cors(securityConfig.cors));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: securityConfig.validation.maxBodySize }));
app.use(express.urlencoded({ extended: true, limit: securityConfig.validation.maxBodySize }));

// Security validation middleware
app.use(validateRequestSize);
app.use(sanitizeInput);
app.use(detectSQLInjection);
app.use(detectXSS);

// Internationalization middleware
app.use(i18nMiddleware);

// Data sanitization
app.use(mongoSanitize()); // Against NoSQL query injection
app.use(xss()); // Against XSS attacks

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (_, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// API Routes
app.use('/api/v1/auth', require('./routes/auth'));
app.use('/api/v1/users', require('./routes/users'));
app.use('/api/v1/products', require('./routes/products'));
app.use('/api/v1/categories', require('./routes/categories'));
app.use('/api/v1/orders', require('./routes/orders'));
app.use('/api/v1/addresses', require('./routes/addresses'));
app.use('/api/v1/cart', require('./routes/cart'));
app.use('/api/v1/reviews', require('./routes/reviews'));
app.use('/api/v1/payments', require('./routes/payments'));
app.use('/api/v1/invoices', require('./routes/invoices'));
app.use('/api/v1/shipping', require('./routes/shipping'));
app.use('/api/v1/inventory', require('./routes/inventory'));
app.use('/api/v1/analytics', require('./routes/analytics'));
app.use('/api/v1/notifications', require('./routes/notifications'));
app.use('/api/v1/security', require('./routes/security'));
app.use('/api/v1/i18n', require('./routes/i18n'));
app.use('/api/v1/admin', require('./routes/admin'));
app.use('/api/v1/upload', require('./routes/upload'));
app.use('/api/v1/refunds', require('./routes/refunds'));
app.use('/api/v1/coupons', require('./routes/coupons'));
app.use('/api/v1/product-variants', require('./routes/productVariants'));
app.use('/api/v1/logistics', require('./routes/logistics'));
app.use('/api/v1/chat', require('./routes/chat'));
app.use('/api/v1/agent', require('./routes/agent'));

// Webhook routes (before body parsing)
app.use('/webhooks', require('./routes/webhooks'));

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO
const io = socketService.initialize(server);

// Set Socket.IO instance for notification service
notificationService.setSocketIO(io);

server.listen(PORT, async () => {
  console.log(`🚀 Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  console.log(`🔌 Socket.IO server ready for real-time notifications`);

  // Initialize services
  try {
    await initI18n();
    await localizationService.initialize();
    await currencyService.initialize();
    cronService.initialize();
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Service initialization failed:', error);
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`❌ Unhandled Rejection: ${err.message}`);
  // Close server & exit process
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log(`❌ Uncaught Exception: ${err.message}`);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received. Shutting down gracefully...');
  server.close(() => {
    console.log('💀 Process terminated');
  });
});

module.exports = app;
