import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Address {
  id: string;
  type: 'home' | 'work' | 'other';
  name: string;
  phone: string;
  country: string;
  state: string;
  city: string;
  address: string;
  postalCode: string;
  isDefault: boolean;
}

export interface Order {
  id: string;
  orderNumber: string;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  items: Array<{
    id: string;
    name: string;
    image: string;
    price: number;
    quantity: number;
    variant?: Record<string, string>;
  }>;
  totalAmount: number;
  shippingCost: number;
  currency: string;
  shippingAddress: Address;
  paymentMethod: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserState {
  profile: {
    id: string;
    email: string;
    name: string;
    phone?: string;
    avatar?: string;
    dateOfBirth?: string;
    gender?: 'male' | 'female' | 'other';
    preferences: {
      language: string;
      currency: string;
      theme: string;
      notifications: {
        email: boolean;
        sms: boolean;
        push: boolean;
      };
    };
  } | null;
  addresses: Address[];
  orders: Order[];
  wishlist: string[];
  recentlyViewed: string[];
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  profile: null,
  addresses: [],
  orders: [],
  wishlist: JSON.parse(localStorage.getItem('wishlist') || '[]'),
  recentlyViewed: JSON.parse(localStorage.getItem('recentlyViewed') || '[]'),
  loading: false,
  error: null,
};

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async () => {
    // Mock API call
    const mockProfile = {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      phone: '+1234567890',
      preferences: {
        language: 'en',
        currency: 'USD',
        theme: 'light',
        notifications: {
          email: true,
          sms: false,
          push: true,
        },
      },
    };
    return mockProfile;
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData: Partial<UserState['profile']>) => {
    // Mock API call
    return profileData;
  }
);

export const fetchUserAddresses = createAsyncThunk(
  'user/fetchAddresses',
  async () => {
    // Mock API call
    const mockAddresses: Address[] = [
      {
        id: '1',
        type: 'home',
        name: 'John Doe',
        phone: '+1234567890',
        country: 'United States',
        state: 'California',
        city: 'San Francisco',
        address: '123 Main St, Apt 4B',
        postalCode: '94102',
        isDefault: true,
      },
    ];
    return mockAddresses;
  }
);

export const fetchUserOrders = createAsyncThunk(
  'user/fetchOrders',
  async () => {
    // Mock API call
    const mockOrders: Order[] = [
      {
        id: '1',
        orderNumber: 'ORD-2024-001',
        status: 'shipped',
        items: [
          {
            id: '1',
            name: 'Premium Wireless Headphones',
            image: '/images/headphones1.jpg',
            price: 299.99,
            quantity: 1,
            variant: { color: 'Black' },
          },
        ],
        totalAmount: 299.99,
        shippingCost: 0,
        currency: 'USD',
        shippingAddress: {
          id: '1',
          type: 'home',
          name: 'John Doe',
          phone: '+1234567890',
          country: 'United States',
          state: 'California',
          city: 'San Francisco',
          address: '123 Main St, Apt 4B',
          postalCode: '94102',
          isDefault: true,
        },
        paymentMethod: 'Credit Card',
        trackingNumber: 'TRK123456789',
        estimatedDelivery: '2024-01-15',
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-12T14:30:00Z',
      },
    ];
    return mockOrders;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    addToWishlist: (state, action: PayloadAction<string>) => {
      if (!state.wishlist.includes(action.payload)) {
        state.wishlist.push(action.payload);
        localStorage.setItem('wishlist', JSON.stringify(state.wishlist));
      }
    },
    removeFromWishlist: (state, action: PayloadAction<string>) => {
      state.wishlist = state.wishlist.filter(id => id !== action.payload);
      localStorage.setItem('wishlist', JSON.stringify(state.wishlist));
    },
    addToRecentlyViewed: (state, action: PayloadAction<string>) => {
      const productId = action.payload;
      state.recentlyViewed = state.recentlyViewed.filter(id => id !== productId);
      state.recentlyViewed.unshift(productId);
      
      // Keep only last 10 items
      if (state.recentlyViewed.length > 10) {
        state.recentlyViewed = state.recentlyViewed.slice(0, 10);
      }
      
      localStorage.setItem('recentlyViewed', JSON.stringify(state.recentlyViewed));
    },
    clearRecentlyViewed: (state) => {
      state.recentlyViewed = [];
      localStorage.removeItem('recentlyViewed');
    },
    addAddress: (state, action: PayloadAction<Address>) => {
      state.addresses.push(action.payload);
    },
    updateAddress: (state, action: PayloadAction<Address>) => {
      const index = state.addresses.findIndex(addr => addr.id === action.payload.id);
      if (index !== -1) {
        state.addresses[index] = action.payload;
      }
    },
    removeAddress: (state, action: PayloadAction<string>) => {
      state.addresses = state.addresses.filter(addr => addr.id !== action.payload);
    },
    setDefaultAddress: (state, action: PayloadAction<string>) => {
      state.addresses.forEach(addr => {
        addr.isDefault = addr.id === action.payload;
      });
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch profile';
      })
      // Update profile
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload };
        }
      })
      // Fetch addresses
      .addCase(fetchUserAddresses.fulfilled, (state, action) => {
        state.addresses = action.payload;
      })
      // Fetch orders
      .addCase(fetchUserOrders.fulfilled, (state, action) => {
        state.orders = action.payload;
      });
  },
});

export const {
  addToWishlist,
  removeFromWishlist,
  addToRecentlyViewed,
  clearRecentlyViewed,
  addAddress,
  updateAddress,
  removeAddress,
  setDefaultAddress,
  clearError,
} = userSlice.actions;

export default userSlice.reducer;
