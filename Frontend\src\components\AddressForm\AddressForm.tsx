import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Switch,
  Space,
  message,
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { createAddress, updateAddress } from '../../store/slices/addressSlice';
import { Address } from '../../store/slices/addressSlice';

const { Option } = Select;

interface AddressFormProps {
  address?: Address | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const AddressForm: React.FC<AddressFormProps> = ({ 
  address, 
  onSuccess, 
  onCancel 
}) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.addresses);

  useEffect(() => {
    if (address) {
      form.setFieldsValue(address);
    } else {
      form.resetFields();
    }
  }, [address, form]);

  const handleSubmit = async (values: any) => {
    try {
      if (address) {
        // Update existing address
        await dispatch(updateAddress({
          addressId: address.id,
          addressData: values,
        })).unwrap();
        message.success('Address updated successfully!');
      } else {
        // Create new address
        await dispatch(createAddress(values)).unwrap();
        message.success('Address created successfully!');
      }
      
      form.resetFields();
      onSuccess?.();
    } catch (error: any) {
      message.error(error.message || 'Failed to save address');
    }
  };

  const countries = [
    'United States',
    'Canada',
    'United Kingdom',
    'Germany',
    'France',
    'Australia',
    'Japan',
    'China',
    'India',
    'Brazil',
  ];

  const addressTypes = [
    { value: 'home', label: 'Home', icon: <HomeOutlined /> },
    { value: 'work', label: 'Work', icon: <EnvironmentOutlined /> },
    { value: 'other', label: 'Other', icon: <EnvironmentOutlined /> },
  ];

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        type: 'home',
        isDefault: false,
        country: 'United States',
      }}
    >
      <Row gutter={16}>
        <Col xs={24} sm={12}>
          <Form.Item
            name="type"
            label="Address Type"
            rules={[{ required: true, message: 'Please select address type' }]}
          >
            <Select placeholder="Select address type">
              {addressTypes.map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    {type.icon}
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            name="name"
            label="Full Name"
            rules={[{ required: true, message: 'Please enter full name' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Enter full name"
              size="large"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} sm={12}>
          <Form.Item
            name="phone"
            label="Phone Number"
            rules={[
              { required: true, message: 'Please enter phone number' },
              { pattern: /^[+]?[\d\s\-\(\)]+$/, message: 'Please enter valid phone number' }
            ]}
          >
            <Input
              prefix={<PhoneOutlined />}
              placeholder="Enter phone number"
              size="large"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12}>
          <Form.Item
            name="country"
            label="Country"
            rules={[{ required: true, message: 'Please select country' }]}
          >
            <Select
              placeholder="Select country"
              size="large"
              showSearch
              filterOption={(input, option) =>
                option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
              }
            >
              {countries.map(country => (
                <Option key={country} value={country}>
                  {country}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col xs={24} sm={8}>
          <Form.Item
            name="state"
            label="State/Province"
            rules={[{ required: true, message: 'Please enter state/province' }]}
          >
            <Input
              placeholder="Enter state/province"
              size="large"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8}>
          <Form.Item
            name="city"
            label="City"
            rules={[{ required: true, message: 'Please enter city' }]}
          >
            <Input
              placeholder="Enter city"
              size="large"
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={8}>
          <Form.Item
            name="postalCode"
            label="Postal Code"
            rules={[{ required: true, message: 'Please enter postal code' }]}
          >
            <Input
              placeholder="Enter postal code"
              size="large"
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="address"
        label="Street Address"
        rules={[{ required: true, message: 'Please enter street address' }]}
      >
        <Input.TextArea
          placeholder="Enter street address (apartment, suite, etc.)"
          rows={3}
          size="large"
        />
      </Form.Item>

      <Form.Item
        name="isDefault"
        valuePropName="checked"
      >
        <Switch />
        <span style={{ marginLeft: 8 }}>Set as default address</span>
      </Form.Item>

      <Form.Item>
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            size="large"
            style={{
              background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
              border: 'none',
            }}
          >
            {address ? 'Update Address' : 'Save Address'}
          </Button>
          {onCancel && (
            <Button size="large" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
};

export default AddressForm;
