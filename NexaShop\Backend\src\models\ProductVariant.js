const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

// Product Variant Attribute (e.g., Color, Size, Material)
const VariantAttribute = sequelize.define('VariantAttribute', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  display_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('text', 'color', 'image', 'number'),
    defaultValue: 'text',
    allowNull: false
  },
  is_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'variant_attributes',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name'],
      unique: true
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Product Variant Attribute Values (e.g., Red, Blue, Small, Large)
const VariantAttributeValue = sequelize.define('VariantAttributeValue', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  attribute_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'variant_attributes',
      key: 'id'
    }
  },
  value: {
    type: DataTypes.STRING(200),
    allowNull: false
  },
  display_value: {
    type: DataTypes.STRING(200),
    allowNull: false
  },
  color_code: {
    type: DataTypes.STRING(7),
    allowNull: true,
    validate: {
      is: /^#[0-9A-F]{6}$/i
    },
    comment: 'Hex color code for color attributes'
  },
  image_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Image URL for image-type attributes'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'variant_attribute_values',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['attribute_id']
    },
    {
      fields: ['attribute_id', 'value'],
      unique: true
    },
    {
      fields: ['sort_order']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Product Variants
const ProductVariant = sequelize.define('ProductVariant', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    },
    index: true
  },
  sku: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true
  },
  barcode: {
    type: DataTypes.STRING(100),
    allowNull: true,
    unique: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Override price for this variant, null uses product price'
  },
  compare_at_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Original price for sale display'
  },
  cost_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Cost price for profit calculation'
  },
  weight: {
    type: DataTypes.DECIMAL(8, 3),
    allowNull: true,
    comment: 'Weight in kg'
  },
  dimensions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Dimensions object {length, width, height, unit}'
  },
  inventory_quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  inventory_policy: {
    type: DataTypes.ENUM('deny', 'continue'),
    defaultValue: 'deny',
    allowNull: false,
    comment: 'deny: stop selling when out of stock, continue: allow overselling'
  },
  inventory_management: {
    type: DataTypes.ENUM('shopify', 'manual', 'external'),
    defaultValue: 'manual',
    allowNull: false
  },
  fulfillment_service: {
    type: DataTypes.STRING(100),
    defaultValue: 'manual',
    allowNull: false
  },
  requires_shipping: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  taxable: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  tax_code: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  position: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Sort order within product variants'
  },
  is_default: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Default variant for the product'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  image_urls: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: [],
    comment: 'Array of image URLs specific to this variant'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'product_variants',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['product_id']
    },
    {
      fields: ['sku'],
      unique: true
    },
    {
      fields: ['barcode'],
      unique: true,
      where: {
        barcode: {
          [Op.ne]: null
        }
      }
    },
    {
      fields: ['position']
    },
    {
      fields: ['is_default']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['inventory_quantity']
    }
  ]
});

// Product Variant Attribute Values Association
const ProductVariantAttributeValue = sequelize.define('ProductVariantAttributeValue', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  variant_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'product_variants',
      key: 'id'
    }
  },
  attribute_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'variant_attributes',
      key: 'id'
    }
  },
  attribute_value_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'variant_attribute_values',
      key: 'id'
    }
  }
}, {
  tableName: 'product_variant_attribute_values',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['variant_id']
    },
    {
      fields: ['attribute_id']
    },
    {
      fields: ['attribute_value_id']
    },
    {
      fields: ['variant_id', 'attribute_id'],
      unique: true
    }
  ]
});

// Inventory Log for tracking stock changes
const InventoryLog = sequelize.define('InventoryLog', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  variant_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'product_variants',
      key: 'id'
    },
    index: true
  },
  change_type: {
    type: DataTypes.ENUM(
      'sale',
      'return',
      'restock',
      'adjustment',
      'damaged',
      'lost',
      'transfer',
      'initial'
    ),
    allowNull: false
  },
  quantity_change: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Positive for increase, negative for decrease'
  },
  quantity_before: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  quantity_after: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  reference_type: {
    type: DataTypes.ENUM('order', 'return', 'adjustment', 'transfer'),
    allowNull: true
  },
  reference_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'ID of the related order, return, etc.'
  },
  reason: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'User who made the change'
  },
  location: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: 'Warehouse or location identifier'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    defaultValue: {}
  }
}, {
  tableName: 'inventory_logs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['variant_id']
    },
    {
      fields: ['change_type']
    },
    {
      fields: ['reference_type']
    },
    {
      fields: ['reference_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = {
  VariantAttribute,
  VariantAttributeValue,
  ProductVariant,
  ProductVariantAttributeValue,
  InventoryLog
};
