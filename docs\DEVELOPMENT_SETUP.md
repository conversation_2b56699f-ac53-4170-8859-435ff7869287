# 开发环境配置指南

## 🔧 TypeScript 配置问题解决方案

### 问题描述
如果您遇到以下错误：
```
加载引用"https://json.schemastore.org/tsconfig"时出现问题: 无法从"https://json.schemastore.org/tsconfig"加载架构: getaddrinfo ENOTFOUND json.schemastore.org。
```

### 解决方案

#### 1. VS Code 设置配置
项目已配置 `.vscode/settings.json` 文件，包含以下设置：
- 禁用网络 JSON schema 下载
- 使用本地 schema 文件
- 优化 TypeScript 开发体验

#### 2. 本地 Schema 文件
- 位置：`.vscode/schemas/tsconfig.schema.json`
- 提供离线 TypeScript 配置验证
- 避免网络连接问题

#### 3. 配置验证
运行以下命令检查 TypeScript 配置：
```bash
node scripts/check-ts-config.js
```

## 🚀 开发环境启动

### 前端开发服务器
```bash
cd Frontend
npm start
```
访问：http://localhost:3001

### 后端开发服务器
```bash
cd Backend
npm run dev
```
访问：http://localhost:5000

## 📁 项目结构

```
cross-border-ecommerce/
├── Frontend/                 # React 前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   ├── pages/           # 页面组件
│   │   ├── store/           # Redux 状态管理
│   │   ├── types/           # TypeScript 类型定义
│   │   └── styles/          # 样式文件
│   ├── public/              # 静态资源
│   └── tsconfig.json        # TypeScript 配置
├── Backend/                 # Node.js 后端 API
│   ├── src/
│   │   ├── routes/          # API 路由
│   │   ├── middleware/      # 中间件
│   │   └── config/          # 配置文件
│   └── package.json
├── .vscode/                 # VS Code 配置
│   ├── settings.json        # 编辑器设置
│   └── schemas/             # 本地 JSON schemas
├── scripts/                 # 工具脚本
└── docs/                    # 项目文档
```

## 🛠 常用命令

### 开发命令
```bash
# 安装依赖
npm install

# 启动前端开发服务器
cd Frontend && npm start

# 启动后端开发服务器
cd Backend && npm run dev

# TypeScript 类型检查
cd Frontend && npx tsc --noEmit

# 配置检查
node scripts/check-ts-config.js
```

### 构建命令
```bash
# 前端生产构建
cd Frontend && npm run build

# 后端生产构建
cd Backend && npm run build
```

## 🔍 故障排除

### TypeScript 错误
1. 重启 VS Code TypeScript 服务：`Ctrl+Shift+P` → "TypeScript: Restart TS Server"
2. 清除缓存：删除 `node_modules` 和 `package-lock.json`，重新安装
3. 检查配置：运行 `node scripts/check-ts-config.js`

### 网络连接问题
- 项目已配置离线开发模式
- 所有 schema 文件都在本地
- 无需网络连接即可正常开发

### 端口冲突
- 前端默认端口：3001
- 后端默认端口：5000
- 如有冲突，系统会自动分配其他端口

## 📝 开发规范

### TypeScript
- 使用严格类型检查
- 定义清晰的接口和类型
- 避免使用 `any` 类型

### 代码风格
- 使用 Prettier 格式化代码
- 遵循 ESLint 规则
- 组件使用 PascalCase 命名

### Git 提交
- 使用语义化提交信息
- 提交前运行类型检查
- 保持提交历史清晰

## 🎯 下一步开发

参考 [开发计划文档](./DEVELOPMENT_PLAN.md) 了解详细的功能开发规划。
