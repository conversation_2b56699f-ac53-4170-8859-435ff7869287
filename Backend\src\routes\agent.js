const express = require('express');
const { body, validationResult, param, query } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const { Chat, ChatMessage, ChatParticipant, User, AgentStatus } = require('../models');
const socketService = require('../services/socketService');
const { Op } = require('sequelize');

const router = express.Router();

// Middleware to check if user is agent or admin
const requireAgent = (req, res, next) => {
  if (req.user.role !== 'agent' && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Agent or admin role required.'
    });
  }
  next();
};

// @route   GET /api/v1/agent/status
// @desc    Get agent status
// @access  Private (Agent/Admin)
router.get('/status', [
  authenticate,
  requireAgent
], async (req, res) => {
  try {
    const agentId = req.user.id;

    let agentStatus = await AgentStatus.findOne({
      where: { agent_id: agentId },
      include: [
        { model: User, as: 'agent', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ]
    });

    if (!agentStatus) {
      // Create default status for new agent
      agentStatus = await AgentStatus.create({
        agent_id: agentId,
        status: 'offline',
        availability: true
      });
    }

    res.json({
      success: true,
      data: { agentStatus }
    });

  } catch (error) {
    console.error('Get agent status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get agent status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/v1/agent/status
// @desc    Update agent status
// @access  Private (Agent/Admin)
router.put('/status', [
  authenticate,
  requireAgent,
  body('status').optional().isIn(['online', 'offline', 'busy', 'away', 'break']).withMessage('Invalid status'),
  body('availability').optional().isBoolean().withMessage('Availability must be boolean'),
  body('status_message').optional().isString().isLength({ max: 255 }).withMessage('Status message too long'),
  body('max_concurrent_chats').optional().isInt({ min: 1, max: 20 }).withMessage('Max concurrent chats must be 1-20')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const agentId = req.user.id;
    const { status, availability, status_message, max_concurrent_chats } = req.body;

    const updateData = {
      last_activity_at: new Date()
    };

    if (status !== undefined) updateData.status = status;
    if (availability !== undefined) updateData.availability = availability;
    if (status_message !== undefined) updateData.status_message = status_message;
    if (max_concurrent_chats !== undefined) updateData.max_concurrent_chats = max_concurrent_chats;

    const [agentStatus] = await AgentStatus.upsert({
      agent_id: agentId,
      ...updateData
    });

    // Notify other agents about status change
    socketService.sendToRoom('agents', 'agent_status_updated', {
      agentId,
      status: agentStatus.status,
      availability: agentStatus.availability,
      statusMessage: agentStatus.status_message,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Agent status updated successfully',
      data: { agentStatus }
    });

  } catch (error) {
    console.error('Update agent status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update agent status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/agent/pending-chats
// @desc    Get pending chats waiting for assignment
// @access  Private (Agent/Admin)
router.get('/pending-chats', [
  authenticate,
  requireAgent,
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be >= 0'),
  query('category').optional().isString().withMessage('Category must be a string'),
  query('priority').optional().isIn(['low', 'normal', 'high', 'urgent']).withMessage('Invalid priority')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { limit = 20, offset = 0, category, priority } = req.query;

    const whereClause = {
      status: 'pending',
      assigned_agent_id: null
    };

    if (category) whereClause.category = category;
    if (priority) whereClause.priority = priority;

    const pendingChats = await Chat.findAndCountAll({
      where: whereClause,
      include: [
        { model: User, as: 'customer', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] }
      ],
      order: [
        ['priority', 'DESC'],
        ['created_at', 'ASC']
      ],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        chats: pendingChats.rows,
        total: pendingChats.count,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    console.error('Get pending chats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending chats',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/agent/accept-chat/:id
// @desc    Accept a pending chat
// @access  Private (Agent/Admin)
router.post('/accept-chat/:id', [
  authenticate,
  requireAgent,
  param('id').isUUID().withMessage('Invalid chat ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const agentId = req.user.id;

    // Check if chat exists and is pending
    const chat = await Chat.findByPk(id);
    if (!chat) {
      return res.status(404).json({
        success: false,
        message: 'Chat not found'
      });
    }

    if (chat.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Chat is not pending'
      });
    }

    if (chat.assigned_agent_id) {
      return res.status(400).json({
        success: false,
        message: 'Chat is already assigned to another agent'
      });
    }

    // Check agent availability
    const agentStatus = await AgentStatus.findOne({
      where: { agent_id: agentId }
    });

    if (!agentStatus || !agentStatus.availability || agentStatus.status === 'offline') {
      return res.status(400).json({
        success: false,
        message: 'Agent is not available to accept chats'
      });
    }

    if (agentStatus.current_chat_count >= agentStatus.max_concurrent_chats) {
      return res.status(400).json({
        success: false,
        message: 'Agent has reached maximum concurrent chats limit'
      });
    }

    // Assign chat to agent
    await Chat.update(
      { 
        assigned_agent_id: agentId,
        status: 'active'
      },
      { where: { id } }
    );

    // Add agent as participant
    await ChatParticipant.create({
      chat_id: id,
      user_id: agentId,
      role: 'agent',
      permissions: {
        can_send_messages: true,
        can_send_files: true,
        can_view_history: true,
        can_close_chat: true,
        can_transfer_chat: true
      }
    });

    // Update agent status
    await AgentStatus.increment('current_chat_count', { where: { agent_id: agentId } });

    // Send system message
    await ChatMessage.create({
      chat_id: id,
      sender_id: agentId,
      sender_type: 'system',
      message_type: 'system',
      content: `${req.user.first_name} ${req.user.last_name} has joined the chat`,
      delivery_status: 'delivered'
    });

    // Load updated chat
    const updatedChat = await Chat.findByPk(id, {
      include: [
        { model: User, as: 'customer', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] },
        { model: User, as: 'assignedAgent', attributes: ['id', 'first_name', 'last_name', 'email', 'avatar'] }
      ]
    });

    // Notify customer
    socketService.sendToUser(chat.customer_id, 'chat_agent_assigned', {
      chat: updatedChat,
      agent: {
        id: req.user.id,
        name: `${req.user.first_name} ${req.user.last_name}`
      },
      timestamp: new Date().toISOString()
    });

    // Notify other agents that chat is no longer pending
    socketService.sendToRoom('agents', 'chat_assigned', {
      chatId: id,
      agentId,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Chat accepted successfully',
      data: { chat: updatedChat }
    });

  } catch (error) {
    console.error('Accept chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept chat',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
