const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate, requireCustomer } = require('../middleware/auth');
const paypalService = require('../services/paypalService');
const stripeService = require('../services/stripeService');
const { Order, Payment, User } = require('../models');
const { paymentMethods, paymentStatus } = require('../config/payment');

const router = express.Router();

// Mock payments storage (in production, use database)
const mockPayments = [];

// @route   POST /api/v1/payments/paypal/create-order
// @desc    Create PayPal order
// @access  Private
router.post('/paypal/create-order', async (req, res) => {
  try {
    const { orderId, amount, currency = 'USD' } = req.body;

    if (!orderId || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Order ID and amount are required'
      });
    }

    // Mock PayPal order creation
    const paypalOrder = {
      id: `PAYPAL_${Date.now()}`,
      status: 'CREATED',
      links: [
        {
          href: `https://api.sandbox.paypal.com/v2/checkout/orders/PAYPAL_${Date.now()}`,
          rel: 'self',
          method: 'GET'
        },
        {
          href: `https://www.sandbox.paypal.com/checkoutnow?token=PAYPAL_${Date.now()}`,
          rel: 'approve',
          method: 'GET'
        }
      ]
    };

    // Store payment record
    const payment = {
      id: String(Date.now()),
      orderId,
      amount: parseFloat(amount),
      currency,
      method: 'paypal',
      status: 'pending',
      paypalOrderId: paypalOrder.id,
      createdAt: new Date().toISOString(),
    };

    mockPayments.push(payment);

    res.json({
      success: true,
      data: {
        paypalOrderId: paypalOrder.id,
        approvalUrl: paypalOrder.links.find(link => link.rel === 'approve')?.href,
        payment
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   POST /api/v1/payments/paypal/capture-order
// @desc    Capture PayPal order
// @access  Private
router.post('/paypal/capture-order', async (req, res) => {
  try {
    const { paypalOrderId } = req.body;

    if (!paypalOrderId) {
      return res.status(400).json({
        success: false,
        message: 'PayPal order ID is required'
      });
    }

    // Find payment record
    const payment = mockPayments.find(p => p.paypalOrderId === paypalOrderId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Mock PayPal capture
    const captureResult = {
      id: paypalOrderId,
      status: 'COMPLETED',
      purchase_units: [
        {
          payments: {
            captures: [
              {
                id: `CAPTURE_${Date.now()}`,
                status: 'COMPLETED',
                amount: {
                  currency_code: payment.currency,
                  value: payment.amount.toString()
                }
              }
            ]
          }
        }
      ]
    };

    // Update payment status
    payment.status = 'completed';
    payment.transactionId = captureResult.purchase_units[0].payments.captures[0].id;
    payment.completedAt = new Date().toISOString();

    res.json({
      success: true,
      data: {
        payment,
        captureResult
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   POST /api/v1/payments/stripe/create-payment-intent
// @desc    Create Stripe payment intent
// @access  Private
router.post('/stripe/create-payment-intent', async (req, res) => {
  try {
    const { orderId, amount, currency = 'USD' } = req.body;

    if (!orderId || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Order ID and amount are required'
      });
    }

    // Mock Stripe payment intent creation
    const paymentIntent = {
      id: `pi_${Date.now()}`,
      client_secret: `pi_${Date.now()}_secret_${Math.random().toString(36).substr(2, 9)}`,
      amount: Math.round(parseFloat(amount) * 100), // Stripe uses cents
      currency: currency.toLowerCase(),
      status: 'requires_payment_method'
    };

    // Store payment record
    const payment = {
      id: String(Date.now()),
      orderId,
      amount: parseFloat(amount),
      currency,
      method: 'stripe',
      status: 'pending',
      stripePaymentIntentId: paymentIntent.id,
      createdAt: new Date().toISOString(),
    };

    mockPayments.push(payment);

    res.json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        payment
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   POST /api/v1/payments/stripe/confirm-payment
// @desc    Confirm Stripe payment
// @access  Private
router.post('/stripe/confirm-payment', async (req, res) => {
  try {
    const { paymentIntentId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        message: 'Payment intent ID is required'
      });
    }

    // Find payment record
    const payment = mockPayments.find(p => p.stripePaymentIntentId === paymentIntentId);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Mock Stripe confirmation
    const confirmResult = {
      id: paymentIntentId,
      status: 'succeeded',
      amount: Math.round(payment.amount * 100),
      currency: payment.currency.toLowerCase(),
      charges: {
        data: [
          {
            id: `ch_${Date.now()}`,
            status: 'succeeded',
            amount: Math.round(payment.amount * 100)
          }
        ]
      }
    };

    // Update payment status
    payment.status = 'completed';
    payment.transactionId = confirmResult.charges.data[0].id;
    payment.completedAt = new Date().toISOString();

    res.json({
      success: true,
      data: {
        payment,
        confirmResult
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   GET /api/v1/payments/:id
// @desc    Get payment details
// @access  Private
router.get('/:id', async (req, res) => {
  try {
    const payment = mockPayments.find(p => p.id === req.params.id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: { payment }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   GET /api/v1/payments/order/:orderId
// @desc    Get payments for an order
// @access  Private
router.get('/order/:orderId', async (req, res) => {
  try {
    const payments = mockPayments.filter(p => p.orderId === req.params.orderId);

    res.json({
      success: true,
      data: { payments }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   POST /api/v1/payments/:id/refund
// @desc    Refund payment
// @access  Private (Admin)
router.post('/:id/refund', async (req, res) => {
  try {
    const { amount, reason } = req.body;
    const payment = mockPayments.find(p => p.id === req.params.id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    if (payment.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Can only refund completed payments'
      });
    }

    const refundAmount = amount || payment.amount;

    // Mock refund
    const refund = {
      id: `refund_${Date.now()}`,
      paymentId: payment.id,
      amount: refundAmount,
      currency: payment.currency,
      reason: reason || 'requested_by_customer',
      status: 'succeeded',
      createdAt: new Date().toISOString(),
    };

    // Update payment status
    if (refundAmount >= payment.amount) {
      payment.status = 'refunded';
    } else {
      payment.status = 'partially_refunded';
    }

    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: { refund, payment }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// @route   POST /api/v1/payments/webhooks/paypal
// @desc    Handle PayPal webhooks
// @access  Public
router.post('/webhooks/paypal', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const headers = req.headers;
    const body = req.body;

    // Verify webhook signature
    const isValid = paypalService.verifyWebhookSignature(headers, body);
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook signature'
      });
    }

    const event = JSON.parse(body);
    const result = await paypalService.handleWebhookEvent(event);

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    console.error('PayPal webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed'
    });
  }
});

// @route   POST /api/v1/payments/webhooks/stripe
// @desc    Handle Stripe webhooks
// @access  Public
router.post('/webhooks/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['stripe-signature'];
    const payload = req.body;

    // Verify webhook signature
    const event = stripeService.verifyWebhookSignature(payload, signature);
    if (!event) {
      return res.status(400).json({
        success: false,
        message: 'Invalid webhook signature'
      });
    }

    const result = await stripeService.handleWebhookEvent(event);

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    console.error('Stripe webhook error:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed'
    });
  }
});

module.exports = router;
