import api from './api';

export interface CreateChatRequest {
  category?: string;
  priority?: string;
  title?: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface SendMessageRequest {
  content: string;
  message_type?: 'text' | 'image' | 'file' | 'quick_reply';
  reply_to_id?: string;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
}

export interface GetChatsParams {
  status?: 'pending' | 'active' | 'closed' | 'transferred';
  limit?: number;
  offset?: number;
}

export interface GetMessagesParams {
  limit?: number;
  offset?: number;
}

class ChatService {
  /**
   * Create a new chat session
   */
  async createChat(data: CreateChatRequest) {
    const response = await api.post('/chat/create', data);
    return response.data;
  }

  /**
   * Get user's chat sessions
   */
  async getMyChats(params?: GetChatsParams) {
    const response = await api.get('/chat/my-chats', { params });
    return response.data;
  }

  /**
   * Get chat details with messages
   */
  async getChatDetails(chatId: string, params?: GetMessagesParams) {
    const response = await api.get(`/chat/${chatId}`, { params });
    return response.data;
  }

  /**
   * Get chat messages
   */
  async getChatMessages(chatId: string, params?: GetMessagesParams) {
    const response = await api.get(`/chat/${chatId}`, { params });
    return response.data;
  }

  /**
   * Send a message in chat
   */
  async sendMessage(chatId: string, data: SendMessageRequest) {
    const response = await api.post(`/chat/${chatId}/messages`, data);
    return response.data;
  }

  /**
   * Close a chat session
   */
  async closeChat(chatId: string, feedback?: { rating?: number; comment?: string }) {
    const response = await api.post(`/chat/${chatId}/close`, feedback);
    return response.data;
  }

  /**
   * Transfer chat to another agent
   */
  async transferChat(chatId: string, data: { reason: string; notes?: string }) {
    const response = await api.post(`/chat/${chatId}/transfer`, data);
    return response.data;
  }

  /**
   * Get chat statistics (for agents/admins)
   */
  async getChatStats(params?: { 
    startDate?: string; 
    endDate?: string; 
    agentId?: string 
  }) {
    const response = await api.get('/chat/stats', { params });
    return response.data;
  }
}

// Agent-specific chat services
class AgentChatService {
  /**
   * Get agent status
   */
  async getAgentStatus() {
    const response = await api.get('/agent/status');
    return response.data;
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(data: {
    status?: 'online' | 'offline' | 'busy' | 'away' | 'break';
    availability?: boolean;
    status_message?: string;
    max_concurrent_chats?: number;
  }) {
    const response = await api.put('/agent/status', data);
    return response.data;
  }

  /**
   * Get pending chats waiting for assignment
   */
  async getPendingChats(params?: {
    limit?: number;
    offset?: number;
    category?: string;
    priority?: string;
  }) {
    const response = await api.get('/agent/pending-chats', { params });
    return response.data;
  }

  /**
   * Accept a pending chat
   */
  async acceptChat(chatId: string) {
    const response = await api.post(`/agent/accept-chat/${chatId}`);
    return response.data;
  }

  /**
   * Get agent's assigned chats
   */
  async getAssignedChats(params?: GetChatsParams) {
    const response = await api.get('/chat/my-chats', { params });
    return response.data;
  }

  /**
   * Transfer chat to another agent
   */
  async transferChatToAgent(chatId: string, data: {
    target_agent_id?: string;
    reason: string;
    notes?: string;
  }) {
    const response = await api.post(`/agent/transfer-chat/${chatId}`, data);
    return response.data;
  }

  /**
   * Get available agents for transfer
   */
  async getAvailableAgents() {
    const response = await api.get('/agent/available-agents');
    return response.data;
  }

  /**
   * Get agent performance metrics
   */
  async getAgentMetrics(params?: {
    startDate?: string;
    endDate?: string;
  }) {
    const response = await api.get('/agent/metrics', { params });
    return response.data;
  }
}

export const chatService = new ChatService();
export const agentChatService = new AgentChatService();
export default chatService;
