import React, { useState, useEffect, useRef } from 'react';
import { 
  Input, 
  Button, 
  List, 
  Avatar, 
  Typography, 
  Space, 
  Upload, 
  message,
  Spin,
  Tag,
  Divider
} from 'antd';
import { 
  SendOutlined, 
  PaperClipOutlined,
  UserOutlined,
  CustomerServiceOutlined,
  CheckOutlined,
  DoubleRightOutlined
} from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { chatService } from '../../services/chatService';
import { socketService } from '../../services/socketService';
import { formatDistanceToNow } from 'date-fns';

const { TextArea } = Input;
const { Text } = Typography;

interface ChatWindowProps {
  chat: any;
  onChatUpdate?: (chat: any) => void;
  height?: string | number;
}

interface Message {
  id: string;
  content: string;
  sender_type: 'customer' | 'agent' | 'system';
  message_type: 'text' | 'image' | 'file' | 'system';
  created_at: string;
  sender: {
    id: string;
    first_name: string;
    last_name: string;
    avatar?: string;
  };
  delivery_status: 'sent' | 'delivered' | 'read';
  file_url?: string;
  file_name?: string;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ 
  chat, 
  onChatUpdate,
  height = '500px'
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUser, setTypingUser] = useState<string>('');

  const { user } = useSelector((state: RootState) => state.auth);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (chat?.id) {
      loadMessages();
      joinChatRoom();
    }

    return () => {
      if (chat?.id) {
        leaveChatRoom();
      }
    };
  }, [chat?.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Socket event listeners
    socketService.on('new_message', (data) => {
      if (data.chat_id === chat?.id) {
        setMessages(prev => [...prev, data.message]);
        
        // Send delivery confirmation
        socketService.emit('message_delivered', {
          messageId: data.message.id,
          chatId: chat.id
        });
      }
    });

    socketService.on('user_typing', (data) => {
      if (data.chatId === chat?.id && data.userId !== user?.id) {
        setIsTyping(true);
        setTypingUser(data.userName);
      }
    });

    socketService.on('user_stopped_typing', (data) => {
      if (data.chatId === chat?.id) {
        setIsTyping(false);
        setTypingUser('');
      }
    });

    socketService.on('message_delivery_confirmed', (data) => {
      setMessages(prev => prev.map(msg => 
        msg.id === data.messageId 
          ? { ...msg, delivery_status: data.status }
          : msg
      ));
    });

    socketService.on('message_read_confirmed', (data) => {
      setMessages(prev => prev.map(msg => 
        msg.id === data.messageId 
          ? { ...msg, delivery_status: 'read' }
          : msg
      ));
    });

    return () => {
      socketService.off('new_message');
      socketService.off('user_typing');
      socketService.off('user_stopped_typing');
      socketService.off('message_delivery_confirmed');
      socketService.off('message_read_confirmed');
    };
  }, [chat?.id, user?.id]);

  const loadMessages = async () => {
    try {
      setIsLoading(true);
      const response = await chatService.getChatMessages(chat.id, { limit: 50 });
      setMessages(response.data.messages);
    } catch (error) {
      console.error('Load messages error:', error);
      message.error('Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  };

  const joinChatRoom = () => {
    socketService.emit('join_chat', { chatId: chat.id });
  };

  const leaveChatRoom = () => {
    socketService.emit('leave_chat', { chatId: chat.id });
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      setIsSending(true);
      const messageContent = newMessage.trim();
      setNewMessage('');

      // Stop typing indicator
      socketService.emit('typing_stop', { chatId: chat.id });

      const response = await chatService.sendMessage(chat.id, {
        content: messageContent,
        message_type: 'text'
      });

      // Message will be added via socket event
    } catch (error) {
      console.error('Send message error:', error);
      message.error('Failed to send message');
      setNewMessage(newMessage); // Restore message on error
    } finally {
      setIsSending(false);
    }
  };

  const handleTyping = (value: string) => {
    setNewMessage(value);

    // Send typing indicator
    if (value.trim()) {
      socketService.emit('typing_start', { chatId: chat.id });
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set timeout to stop typing indicator
      typingTimeoutRef.current = setTimeout(() => {
        socketService.emit('typing_stop', { chatId: chat.id });
      }, 2000);
    } else {
      socketService.emit('typing_stop', { chatId: chat.id });
    }
  };

  const getMessageAvatar = (message: Message) => {
    if (message.sender_type === 'system') {
      return <Avatar icon={<CustomerServiceOutlined />} size="small" />;
    }
    
    if (message.sender?.avatar) {
      return <Avatar src={message.sender.avatar} size="small" />;
    }

    return (
      <Avatar 
        icon={message.sender_type === 'agent' ? <CustomerServiceOutlined /> : <UserOutlined />}
        size="small"
        style={{
          backgroundColor: message.sender_type === 'agent' ? '#1890ff' : '#52c41a'
        }}
      />
    );
  };

  const getDeliveryIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckOutlined style={{ color: '#d9d9d9' }} />;
      case 'delivered':
        return <DoubleRightOutlined style={{ color: '#1890ff' }} />;
      case 'read':
        return <DoubleRightOutlined style={{ color: '#52c41a' }} />;
      default:
        return null;
    }
  };

  const getChatStatusTag = () => {
    const statusColors = {
      pending: 'orange',
      active: 'green',
      closed: 'red',
      transferred: 'blue'
    };

    return (
      <Tag color={statusColors[chat.status as keyof typeof statusColors]}>
        {chat.status.toUpperCase()}
      </Tag>
    );
  };

  return (
    <div style={{ 
      height, 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#fafafa'
    }}>
      {/* Chat Header */}
      <div style={{ 
        padding: '12px 16px', 
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>{chat.title || 'Customer Support'}</Text>
            {getChatStatusTag()}
          </div>
          {chat.assignedAgent ? (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Agent: {chat.assignedAgent.first_name} {chat.assignedAgent.last_name}
            </Text>
          ) : (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Waiting for agent assignment...
            </Text>
          )}
        </Space>
      </div>

      {/* Messages Area */}
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        padding: '16px',
        backgroundColor: 'white'
      }}>
        {isLoading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin />
          </div>
        ) : (
          <>
            <List
              dataSource={messages}
              renderItem={(message: Message) => (
                <List.Item
                  style={{
                    padding: '8px 0',
                    border: 'none',
                    justifyContent: message.sender?.id === user?.id ? 'flex-end' : 'flex-start'
                  }}
                >
                  <div style={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: message.sender?.id === user?.id ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: '8px'
                  }}>
                    {getMessageAvatar(message)}
                    <div style={{
                      backgroundColor: message.sender?.id === user?.id ? '#1890ff' : '#f0f0f0',
                      color: message.sender?.id === user?.id ? 'white' : 'black',
                      padding: '8px 12px',
                      borderRadius: '12px',
                      wordBreak: 'break-word'
                    }}>
                      {message.message_type === 'system' ? (
                        <Text italic style={{ color: message.sender?.id === user?.id ? 'white' : '#666' }}>
                          {message.content}
                        </Text>
                      ) : (
                        <div>
                          <div>{message.content}</div>
                          <div style={{ 
                            fontSize: '11px', 
                            opacity: 0.7, 
                            marginTop: '4px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                          }}>
                            <span>
                              {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                            </span>
                            {message.sender?.id === user?.id && getDeliveryIcon(message.delivery_status)}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </List.Item>
              )}
            />
            
            {isTyping && (
              <div style={{ padding: '8px 0', color: '#666', fontSize: '12px' }}>
                <Text italic>{typingUser} is typing...</Text>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div style={{ 
        padding: '16px', 
        borderTop: '1px solid #f0f0f0',
        backgroundColor: 'white'
      }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={newMessage}
            onChange={(e) => handleTyping(e.target.value)}
            onPressEnter={(e) => {
              if (!e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            placeholder="Type your message..."
            autoSize={{ minRows: 1, maxRows: 3 }}
            disabled={chat.status === 'closed'}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            loading={isSending}
            disabled={!newMessage.trim() || chat.status === 'closed'}
          />
        </Space.Compact>
      </div>
    </div>
  );
};

export default ChatWindow;
