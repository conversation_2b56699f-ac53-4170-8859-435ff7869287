import React from 'react';
import { <PERSON>, Typography, Button, Space } from 'antd';

const { Title } = Typography;

const PaymentPage: React.FC = () => {
  return (
    <div style={{ background: '#f5f5f5', minHeight: '100vh', padding: '24px' }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <Title level={2}>Payment</Title>
        <Card>
          <div style={{ textAlign: 'center', padding: '48px' }}>
            <Title level={4}>Payment Feature Coming Soon</Title>
            <Space>
              <Button type="primary">Process Payment</Button>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PaymentPage;
