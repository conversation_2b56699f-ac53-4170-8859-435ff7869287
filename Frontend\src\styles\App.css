/* App Layout Styles */
.app-layout {
  min-height: 100vh;
}

.app-content {
  flex: 1;
  background: #f5f5f5;
  padding: 0;
}

/* Page Container */
.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-container-full {
  width: 100%;
  padding: 0;
}

/* Common Page Styles */
.page-header {
  background: #fff;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* Card Styles */
.content-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Button Styles */
.btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* Form Styles */
.form-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 24px;
}

.form-title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #262626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .form-container {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 12px;
  }

  .page-header {
    padding: 12px;
  }

  .form-container {
    padding: 12px;
  }
}

/* Custom Carousel Dots */
.custom-carousel-dots {
  bottom: 30px !important;
}

.custom-carousel-dots li button {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
}

.custom-carousel-dots li.slick-active button {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid rgba(255, 255, 255, 1) !important;
}

/* Category Card Hover Effects */
.category-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 8px 30px rgba(0,0,0,0.15) !important;
}

/* Product Card Animations */
.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Product Card Hover Effects */
.product-card:hover .product-actions {
  opacity: 1 !important;
}

.product-card .ant-card-cover {
  overflow: hidden;
}

/* Enhanced Button Styles */
.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

/* Header responsive styles */
@media (max-width: 768px) {
  .desktop-menu {
    display: none !important;
  }
  .desktop-only {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
}

.ant-menu-horizontal {
  border-bottom: none;
}

/* Custom carousel dots */
.custom-carousel-dots .slick-dots {
  bottom: 30px;
}

.custom-carousel-dots .slick-dots li button {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.custom-carousel-dots .slick-dots li.slick-active button {
  background: white;
}

/* Category card hover effect */
.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12) !important;
}

/* Import Search Component Styles */
@import './SearchComponents.css';
