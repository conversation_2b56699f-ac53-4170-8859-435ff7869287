const { sequelize } = require('../config/database');
const { DataTypes } = require('sequelize');

// Import all models
const User = require('./User')(sequelize, DataTypes);
const Product = require('./Product')(sequelize, DataTypes);
const Category = require('./Category')(sequelize, DataTypes);
const Order = require('./Order')(sequelize, DataTypes);
const OrderItem = require('./OrderItem')(sequelize, DataTypes);
const Cart = require('./Cart')(sequelize, DataTypes);
const CartItem = require('./CartItem')(sequelize, DataTypes);
const Address = require('./Address')(sequelize, DataTypes);
const Review = require('./Review')(sequelize, DataTypes);
const Payment = require('./Payment')(sequelize, DataTypes);
const Notification = require('./Notification')(sequelize, DataTypes);
const { PageView, UserEvent, ConversionFunnel, UserSession } = require('./Analytics');
const {
  ProductTranslation,
  CategoryTranslation,
  ContentTranslation,
  EmailTemplateTranslation,
  Currency,
  ProductPrice,
  Language
} = require('./Localization');
const { Refund, RefundItem, RefundStatusHistory } = require('./Refund');
const { Coupon, CouponProduct, CouponCategory, CouponUser, CouponUsage } = require('./Coupon');
const {
  VariantAttribute,
  VariantAttributeValue,
  ProductVariant,
  ProductVariantAttributeValue,
  InventoryLog
} = require('./ProductVariant');
const {
  ShippingCarrier,
  ShippingService,
  ShippingRate,
  Shipment,
  TrackingEvent
} = require('./Logistics');

// Chat models
const Chat = require('./Chat')(sequelize, DataTypes);
const ChatMessage = require('./ChatMessage')(sequelize, DataTypes);
const ChatParticipant = require('./ChatParticipant')(sequelize, DataTypes);
const AgentStatus = require('./AgentStatus')(sequelize, DataTypes);

// Define associations
const defineAssociations = () => {
  // User associations
  User.hasMany(Order, { foreignKey: 'user_id', as: 'orders' });
  User.hasMany(Address, { foreignKey: 'user_id', as: 'addresses' });
  User.hasMany(Review, { foreignKey: 'user_id', as: 'reviews' });
  User.hasOne(Cart, { foreignKey: 'user_id', as: 'cart' });

  // Product associations
  Product.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
  Product.hasMany(OrderItem, { foreignKey: 'product_id', as: 'orderItems' });
  Product.hasMany(CartItem, { foreignKey: 'product_id', as: 'cartItems' });
  Product.hasMany(Review, { foreignKey: 'product_id', as: 'reviews' });

  // Category associations
  Category.hasMany(Product, { foreignKey: 'category_id', as: 'products' });
  Category.belongsTo(Category, { foreignKey: 'parent_id', as: 'parent' });
  Category.hasMany(Category, { foreignKey: 'parent_id', as: 'children' });

  // Order associations
  Order.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Order.belongsTo(Address, { foreignKey: 'shipping_address_id', as: 'shippingAddress' });
  Order.belongsTo(Address, { foreignKey: 'billing_address_id', as: 'billingAddress' });
  Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
  Order.hasMany(Payment, { foreignKey: 'order_id', as: 'payments' });

  // OrderItem associations
  OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  OrderItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Cart associations
  Cart.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Cart.hasMany(CartItem, { foreignKey: 'cart_id', as: 'items' });

  // CartItem associations
  CartItem.belongsTo(Cart, { foreignKey: 'cart_id', as: 'cart' });
  CartItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Address associations
  Address.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  // Review associations
  Review.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  Review.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Payment associations
  Payment.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

  // Notification associations
  Notification.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(Notification, { foreignKey: 'user_id', as: 'notifications' });

  // Analytics associations
  PageView.belongsTo(User, { foreignKey: 'user_id', as: 'pageViewUser' });
  UserEvent.belongsTo(User, { foreignKey: 'user_id', as: 'eventUser' });
  ConversionFunnel.belongsTo(User, { foreignKey: 'user_id', as: 'funnelUser' });
  UserSession.belongsTo(User, { foreignKey: 'user_id', as: 'sessionUser' });

  User.hasMany(PageView, { foreignKey: 'user_id', as: 'pageViews' });
  User.hasMany(UserEvent, { foreignKey: 'user_id', as: 'events' });
  User.hasMany(ConversionFunnel, { foreignKey: 'user_id', as: 'funnelSteps' });
  User.hasMany(UserSession, { foreignKey: 'user_id', as: 'sessions' });

  // Localization associations
  Product.hasMany(ProductTranslation, { foreignKey: 'product_id', as: 'translations' });
  Product.hasMany(ProductPrice, { foreignKey: 'product_id', as: 'prices' });
  ProductTranslation.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  ProductPrice.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  Category.hasMany(CategoryTranslation, { foreignKey: 'category_id', as: 'translations' });
  CategoryTranslation.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

  Currency.hasMany(ProductPrice, { foreignKey: 'currency_code', sourceKey: 'code', as: 'productPrices' });
  ProductPrice.belongsTo(Currency, { foreignKey: 'currency_code', targetKey: 'code', as: 'currency' });

  Language.hasMany(ProductTranslation, { foreignKey: 'language_code', sourceKey: 'code', as: 'productTranslations' });
  Language.hasMany(CategoryTranslation, { foreignKey: 'language_code', sourceKey: 'code', as: 'categoryTranslations' });
  Language.hasMany(ContentTranslation, { foreignKey: 'language_code', sourceKey: 'code', as: 'contentTranslations' });
  Language.hasMany(EmailTemplateTranslation, { foreignKey: 'language_code', sourceKey: 'code', as: 'emailTemplateTranslations' });

  ProductTranslation.belongsTo(Language, { foreignKey: 'language_code', targetKey: 'code', as: 'language' });
  CategoryTranslation.belongsTo(Language, { foreignKey: 'language_code', targetKey: 'code', as: 'language' });
  ContentTranslation.belongsTo(Language, { foreignKey: 'language_code', targetKey: 'code', as: 'language' });
  EmailTemplateTranslation.belongsTo(Language, { foreignKey: 'language_code', targetKey: 'code', as: 'language' });

  // Refund associations
  Refund.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Refund.belongsTo(Payment, { foreignKey: 'payment_id', as: 'payment' });
  Refund.hasMany(RefundItem, { foreignKey: 'refund_id', as: 'items' });
  Refund.hasMany(RefundStatusHistory, { foreignKey: 'refund_id', as: 'statusHistory' });

  RefundItem.belongsTo(Refund, { foreignKey: 'refund_id', as: 'refund' });
  RefundItem.belongsTo(OrderItem, { foreignKey: 'order_item_id', as: 'orderItem' });

  RefundStatusHistory.belongsTo(Refund, { foreignKey: 'refund_id', as: 'refund' });

  // Coupon associations
  Coupon.hasMany(CouponProduct, { foreignKey: 'coupon_id', as: 'products' });
  Coupon.hasMany(CouponCategory, { foreignKey: 'coupon_id', as: 'categories' });
  Coupon.hasMany(CouponUser, { foreignKey: 'coupon_id', as: 'users' });
  Coupon.hasMany(CouponUsage, { foreignKey: 'coupon_id', as: 'usages' });

  CouponProduct.belongsTo(Coupon, { foreignKey: 'coupon_id', as: 'coupon' });
  CouponProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  CouponCategory.belongsTo(Coupon, { foreignKey: 'coupon_id', as: 'coupon' });
  CouponCategory.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });

  CouponUser.belongsTo(Coupon, { foreignKey: 'coupon_id', as: 'coupon' });
  CouponUser.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

  CouponUsage.belongsTo(Coupon, { foreignKey: 'coupon_id', as: 'coupon' });
  CouponUsage.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  CouponUsage.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });

  // Product Variant associations
  VariantAttribute.hasMany(VariantAttributeValue, { foreignKey: 'attribute_id', as: 'values' });
  VariantAttributeValue.belongsTo(VariantAttribute, { foreignKey: 'attribute_id', as: 'attribute' });

  ProductVariant.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  ProductVariant.hasMany(ProductVariantAttributeValue, { foreignKey: 'variant_id', as: 'attributeValues' });
  ProductVariant.hasMany(InventoryLog, { foreignKey: 'variant_id', as: 'inventoryLogs' });

  ProductVariantAttributeValue.belongsTo(ProductVariant, { foreignKey: 'variant_id', as: 'variant' });
  ProductVariantAttributeValue.belongsTo(VariantAttributeValue, { foreignKey: 'attribute_value_id', as: 'attributeValue' });

  InventoryLog.belongsTo(ProductVariant, { foreignKey: 'variant_id', as: 'variant' });

  // Logistics associations
  ShippingCarrier.hasMany(ShippingService, { foreignKey: 'carrier_id', as: 'services' });
  ShippingCarrier.hasMany(Shipment, { foreignKey: 'carrier_id', as: 'shipments' });

  ShippingService.belongsTo(ShippingCarrier, { foreignKey: 'carrier_id', as: 'carrier' });
  ShippingService.hasMany(ShippingRate, { foreignKey: 'service_id', as: 'rates' });
  ShippingService.hasMany(Shipment, { foreignKey: 'service_id', as: 'shipments' });

  ShippingRate.belongsTo(ShippingService, { foreignKey: 'service_id', as: 'service' });

  Shipment.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Shipment.belongsTo(ShippingCarrier, { foreignKey: 'carrier_id', as: 'carrier' });
  Shipment.belongsTo(ShippingService, { foreignKey: 'service_id', as: 'service' });
  Shipment.hasMany(TrackingEvent, { foreignKey: 'shipment_id', as: 'trackingEvents' });

  TrackingEvent.belongsTo(Shipment, { foreignKey: 'shipment_id', as: 'shipment' });

  // Chat associations
  Chat.belongsTo(User, { foreignKey: 'customer_id', as: 'customer' });
  Chat.belongsTo(User, { foreignKey: 'assigned_agent_id', as: 'assignedAgent' });
  Chat.belongsTo(User, { foreignKey: 'closed_by', as: 'closedBy' });
  Chat.hasMany(ChatMessage, { foreignKey: 'chat_id', as: 'messages' });
  Chat.hasMany(ChatParticipant, { foreignKey: 'chat_id', as: 'participants' });

  ChatMessage.belongsTo(Chat, { foreignKey: 'chat_id', as: 'chat' });
  ChatMessage.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
  ChatMessage.belongsTo(ChatMessage, { foreignKey: 'reply_to_id', as: 'replyTo' });
  ChatMessage.hasMany(ChatMessage, { foreignKey: 'reply_to_id', as: 'replies' });

  ChatParticipant.belongsTo(Chat, { foreignKey: 'chat_id', as: 'chat' });
  ChatParticipant.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  ChatParticipant.belongsTo(ChatMessage, { foreignKey: 'last_read_message_id', as: 'lastReadMessage' });

  AgentStatus.belongsTo(User, { foreignKey: 'agent_id', as: 'agent' });

  // User chat associations
  User.hasMany(Chat, { foreignKey: 'customer_id', as: 'customerChats' });
  User.hasMany(Chat, { foreignKey: 'assigned_agent_id', as: 'assignedChats' });
  User.hasMany(ChatMessage, { foreignKey: 'sender_id', as: 'sentMessages' });
  User.hasMany(ChatParticipant, { foreignKey: 'user_id', as: 'chatParticipations' });
  User.hasOne(AgentStatus, { foreignKey: 'agent_id', as: 'agentStatus' });
};

// Initialize associations
defineAssociations();

module.exports = {
  sequelize,
  User,
  Product,
  Category,
  Order,
  OrderItem,
  Cart,
  CartItem,
  Address,
  Review,
  Payment,
  Notification,
  PageView,
  UserEvent,
  ConversionFunnel,
  UserSession,
  ProductTranslation,
  CategoryTranslation,
  ContentTranslation,
  EmailTemplateTranslation,
  Currency,
  ProductPrice,
  Language,
  // Refund models
  Refund,
  RefundItem,
  RefundStatusHistory,
  // Coupon models
  Coupon,
  CouponProduct,
  CouponCategory,
  CouponUser,
  CouponUsage,
  // Product Variant models
  VariantAttribute,
  VariantAttributeValue,
  ProductVariant,
  ProductVariantAttributeValue,
  InventoryLog,
  // Logistics models
  ShippingCarrier,
  ShippingService,
  ShippingRate,
  Shipment,
  TrackingEvent,
  // Chat models
  Chat,
  ChatMessage,
  ChatParticipant,
  AgentStatus
};
