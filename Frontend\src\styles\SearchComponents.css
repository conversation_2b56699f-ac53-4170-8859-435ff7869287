/* Search Bar Styles */
.search-bar {
  width: 100%;
}

.search-bar .ant-input-search {
  border-radius: 8px;
  overflow: hidden;
}

.search-bar .ant-input-search .ant-input {
  border-radius: 8px 0 0 8px;
  border-right: none;
  padding: 12px 16px;
  font-size: 16px;
}

.search-bar .ant-input-search .ant-input-search-button {
  border-radius: 0 8px 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-bar .ant-input-search .ant-input-search-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.header-search .search-bar .ant-input-search .ant-input {
  padding: 8px 16px;
  font-size: 14px;
}

.header-search .search-bar .ant-input-search .ant-input-search-button {
  height: 40px;
  width: 40px;
}

/* Product Filters Styles */
.product-filters {
  position: sticky;
  top: 24px;
}

.product-filters .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.product-filters .ant-card-body {
  padding: 0;
}

.product-filters .ant-collapse {
  border: none;
  background: transparent;
}

.product-filters .ant-collapse-item {
  border-bottom: 1px solid #f0f0f0;
}

.product-filters .ant-collapse-item:last-child {
  border-bottom: none;
}

.product-filters .ant-collapse-header {
  padding: 16px 24px !important;
  font-weight: 500;
  color: #262626;
}

.product-filters .ant-collapse-content-box {
  padding: 0 24px 16px 24px;
}

.product-filters .ant-checkbox-wrapper {
  display: flex;
  align-items: center;
  padding: 4px 0;
  width: 100%;
}

.product-filters .ant-checkbox-wrapper:hover {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
}

.product-filters .ant-slider {
  margin: 16px 0;
}

.product-filters .ant-rate {
  color: #faad14;
}

.product-filters .ant-input-number {
  border-radius: 4px;
}

/* Filter Tags */
.filter-tags {
  margin-bottom: 16px;
}

.filter-tags .ant-tag {
  margin: 4px 8px 4px 0;
  padding: 4px 12px;
  border-radius: 16px;
  border: 1px solid #d9d9d9;
  background: #fafafa;
  color: #595959;
  font-size: 12px;
}

.filter-tags .ant-tag-blue {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.filter-tags .ant-tag-green {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

/* Search Suggestions */
.ant-select-dropdown .ant-select-item {
  padding: 8px 12px;
}

.ant-select-dropdown .ant-select-item-option-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ant-select-dropdown .ant-select-item:hover {
  background-color: #f5f5f5;
}

/* Product Grid Responsive */
@media (max-width: 768px) {
  .product-filters {
    position: static;
  }
  
  .search-bar .ant-input-search .ant-input {
    font-size: 14px;
    padding: 10px 12px;
  }
  
  .search-bar .ant-input-search .ant-input-search-button {
    height: 42px;
    width: 42px;
  }
}

/* Loading States */
.search-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.search-empty {
  text-align: center;
  padding: 40px;
}

.search-empty .ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* Sort and Filter Buttons */
.sort-filter-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.sort-filter-buttons .ant-btn {
  border-radius: 6px;
  height: 36px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sort-filter-buttons .ant-dropdown-trigger {
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #595959;
}

.sort-filter-buttons .ant-dropdown-trigger:hover {
  border-color: #40a9ff;
  color: #1890ff;
}

/* Search Results Header */
.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.search-results-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-results-count {
  color: #8c8c8c;
  font-size: 14px;
}

/* Mobile Responsive */
@media (max-width: 576px) {
  .search-results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .search-results-info {
    width: 100%;
  }
  
  .sort-filter-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Animation */
.search-bar .ant-input-search-button {
  transition: all 0.3s ease;
}

.product-filters .ant-collapse-item {
  transition: all 0.3s ease;
}

.filter-tags .ant-tag {
  transition: all 0.2s ease;
}

.filter-tags .ant-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
