# NexaShop High Priority Features API Documentation

## Overview

This document describes the API endpoints for the four high-priority features implemented in NexaShop:

1. **Refund System** - Complete refund management with Stripe/PayPal integration
2. **Coupon System** - Flexible coupon creation and validation system
3. **Product Variants** - Product variant management with inventory tracking
4. **Logistics System** - Multi-carrier shipping and tracking integration

## Base URL
```
http://localhost:3000/api/v1
```

## Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## 1. Refund System API

### Get All Refunds
```http
GET /refunds
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status
- `order_id` (optional): Filter by order ID

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "refund_id": "REF-123456",
      "order_id": "uuid",
      "amount": 99.99,
      "currency": "USD",
      "reason": "customer_request",
      "status": "succeeded",
      "refund_type": "full",
      "processed_at": "2024-12-29T10:00:00Z",
      "items": [...],
      "statusHistory": [...]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Refund
```http
POST /refunds
```

**Request Body:**
```json
{
  "order_id": "uuid",
  "amount": 99.99,
  "reason": "customer_request",
  "refund_type": "full",
  "items": [
    {
      "order_item_id": "uuid",
      "quantity": 1,
      "amount": 99.99,
      "reason": "Defective product"
    }
  ],
  "notes": "Customer reported defective item"
}
```

### Get Refund by ID
```http
GET /refunds/:id
```

### Update Refund Status
```http
PUT /refunds/:id/status
```

**Request Body:**
```json
{
  "status": "processing",
  "notes": "Processing refund with payment provider"
}
```

### Process Refund
```http
POST /refunds/:id/process
```

---

## 2. Coupon System API

### Get All Coupons
```http
GET /coupons
```

**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `status` (optional): active, inactive, expired
- `type` (optional): percentage, fixed_amount, free_shipping, buy_x_get_y

### Create Coupon
```http
POST /coupons
```

**Request Body:**
```json
{
  "code": "SAVE20",
  "name": "20% Off Summer Sale",
  "description": "Get 20% off on all summer items",
  "type": "percentage",
  "value": 20,
  "minimum_order_amount": 50.00,
  "maximum_discount_amount": 100.00,
  "usage_limit": 1000,
  "usage_limit_per_customer": 1,
  "valid_from": "2024-06-01T00:00:00Z",
  "valid_until": "2024-08-31T23:59:59Z",
  "is_active": true,
  "applicable_to": "all",
  "categories": [],
  "products": [],
  "users": []
}
```

### Validate Coupon
```http
POST /coupons/validate
```

**Request Body:**
```json
{
  "code": "SAVE20",
  "user_id": "uuid",
  "cart_total": 150.00,
  "items": [
    {
      "product_id": "uuid",
      "quantity": 2,
      "price": 75.00
    }
  ]
}
```

### Apply Coupon
```http
POST /coupons/apply
```

---

## 3. Product Variants API

### Get Variant Attributes
```http
GET /variant-attributes
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Color",
      "display_name": "Color",
      "type": "select",
      "is_required": true,
      "sort_order": 1,
      "values": [
        {
          "id": "uuid",
          "value": "Red",
          "display_value": "Red",
          "color_code": "#FF0000",
          "sort_order": 1
        }
      ]
    }
  ]
}
```

### Create Variant Attribute
```http
POST /variant-attributes
```

### Get Product Variants
```http
GET /product-variants
```

**Query Parameters:**
- `product_id` (optional): Filter by product
- `sku` (optional): Filter by SKU
- `in_stock` (optional): Filter by stock status

### Create Product Variant
```http
POST /product-variants
```

**Request Body:**
```json
{
  "product_id": "uuid",
  "sku": "SHIRT-RED-L",
  "price": 29.99,
  "compare_at_price": 39.99,
  "cost_price": 15.00,
  "inventory_quantity": 100,
  "weight": 0.5,
  "requires_shipping": true,
  "is_active": true,
  "attributes": [
    {
      "attribute_id": "uuid",
      "value_id": "uuid"
    }
  ]
}
```

---

## 4. Logistics System API

### Get Shipping Rates
```http
POST /shipping/rates
```

**Request Body:**
```json
{
  "origin": {
    "country": "US",
    "state": "CA",
    "city": "Los Angeles",
    "postal_code": "90210"
  },
  "destination": {
    "country": "US",
    "state": "NY",
    "city": "New York",
    "postal_code": "10001"
  },
  "packages": [
    {
      "weight": 2.5,
      "length": 10,
      "width": 8,
      "height": 6,
      "value": 100.00
    }
  ]
}
```

### Create Shipment
```http
POST /logistics/shipments
```

### Track Shipment
```http
GET /logistics/shipments/:id/tracking
```

### Get Carriers
```http
GET /logistics/carriers
```

---

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "message": "Invalid email format"
    }
  }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to:
- **Authenticated users**: 1000 requests per hour
- **Anonymous users**: 100 requests per hour

## Webhooks

### Refund Status Updates
```http
POST /webhooks/refunds
```

### Shipment Tracking Updates
```http
POST /webhooks/tracking
```

## Testing

Use the following test data for development:

### Test Coupons
- `TEST20` - 20% off, no restrictions
- `FREESHIP` - Free shipping over $50
- `NEWUSER` - $10 off for new users

### Test Tracking Numbers
- `1Z999AA1234567890` - UPS test tracking
- `123456789012` - FedEx test tracking

## Support

For API support, contact:
- **Email**: <EMAIL>
- **Documentation**: https://docs.nexashop.com
- **Status Page**: https://status.nexashop.com
