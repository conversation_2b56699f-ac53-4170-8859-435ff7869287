import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import cartSlice from './slices/cartSlice';
import productSlice from './slices/productSlice';
import uiSlice from './slices/uiSlice';
import userSlice from './slices/userSlice';
import orderSlice from './slices/orderSlice';
import addressSlice from './slices/addressSlice';
import paymentSlice from './slices/paymentSlice';
import reviewSlice from './slices/reviewSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    cart: cartSlice,
    products: productSlice,
    ui: uiSlice,
    user: userSlice,
    orders: orderSlice,
    addresses: addressSlice,
    payments: paymentSlice,
    reviews: reviewSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
