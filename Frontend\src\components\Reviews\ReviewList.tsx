import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  List,
  Rate,
  Avatar,
  Button,
  Space,
  Typography,
  Tag,
  Image,
  Select,
  Row,
  Col,
  Progress,
  Empty,
  message,
  Modal,
} from 'antd';
import {
  LikeOutlined,
  LikeFilled,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { AppDispatch, RootState } from '../../store';
import { 
  fetchProductReviews, 
  markReviewHelpful, 
  deleteReview,
  setFilters 
} from '../../store/slices/reviewSlice';
import { Review, ReviewStatistics } from '../../store/slices/reviewSlice';
import ReviewForm from './ReviewForm';

const { Text, Title } = Typography;
const { Option } = Select;
const { confirm } = Modal;

interface ReviewListProps {
  productId: string;
  showAddReview?: boolean;
}

const ReviewList: React.FC<ReviewListProps> = ({ 
  productId, 
  showAddReview = true 
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { 
    productReviews, 
    statistics, 
    loading, 
    pagination, 
    filters 
  } = useSelector((state: RootState) => state.reviews);
  const { user } = useSelector((state: RootState) => state.auth);

  const [showReviewForm, setShowReviewForm] = useState(false);
  const [editingReview, setEditingReview] = useState<Review | null>(null);

  useEffect(() => {
    dispatch(fetchProductReviews({ 
      productId, 
      page: pagination.currentPage,
      limit: pagination.itemsPerPage,
      sort: filters.sort 
    }));
  }, [dispatch, productId, pagination.currentPage, pagination.itemsPerPage, filters.sort]);

  const handleSortChange = (sort: string) => {
    dispatch(setFilters({ sort }));
  };

  const handleMarkHelpful = async (reviewId: string) => {
    try {
      await dispatch(markReviewHelpful(reviewId)).unwrap();
      message.success('Thank you for your feedback!');
    } catch (error: any) {
      message.error(error.message || 'Failed to mark review as helpful');
    }
  };

  const handleDeleteReview = (review: Review) => {
    confirm({
      title: 'Delete Review',
      content: 'Are you sure you want to delete this review?',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await dispatch(deleteReview(review.id)).unwrap();
          message.success('Review deleted successfully');
        } catch (error: any) {
          message.error(error.message || 'Failed to delete review');
        }
      },
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const renderRatingDistribution = (stats: ReviewStatistics) => {
    const total = stats.totalReviews;
    
    return (
      <div style={{ marginBottom: '24px' }}>
        <Row gutter={24}>
          <Col xs={24} md={8}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#1890ff' }}>
                {stats.averageRating}
              </div>
              <Rate disabled value={stats.averageRating} allowHalf />
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary">{total} review{total !== 1 ? 's' : ''}</Text>
              </div>
            </div>
          </Col>
          <Col xs={24} md={16}>
            <div>
              {[5, 4, 3, 2, 1].map(rating => {
                const count = stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution];
                const percentage = total > 0 ? (count / total) * 100 : 0;
                
                return (
                  <Row key={rating} align="middle" style={{ marginBottom: '8px' }}>
                    <Col span={3}>
                      <Text>{rating} star</Text>
                    </Col>
                    <Col span={16}>
                      <Progress 
                        percent={percentage} 
                        showInfo={false}
                        strokeColor="#faad14"
                      />
                    </Col>
                    <Col span={5} style={{ textAlign: 'right' }}>
                      <Text type="secondary">{count}</Text>
                    </Col>
                  </Row>
                );
              })}
            </div>
          </Col>
        </Row>
      </div>
    );
  };

  const renderReviewItem = (review: Review) => {
    const isOwnReview = user?.id === review.userId;
    
    return (
      <List.Item
        key={review.id}
        actions={[
          <Button
            type="text"
            icon={<LikeOutlined />}
            onClick={() => handleMarkHelpful(review.id)}
          >
            Helpful ({review.helpful})
          </Button>,
          ...(isOwnReview ? [
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => setEditingReview(review)}
            >
              Edit
            </Button>,
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteReview(review)}
            >
              Delete
            </Button>
          ] : [])
        ]}
      >
        <List.Item.Meta
          avatar={
            <Avatar 
              src={review.userAvatar} 
              icon={<UserOutlined />}
              size={48}
            />
          }
          title={
            <div>
              <Space>
                <Text strong>{review.userName}</Text>
                {review.verified && (
                  <Tag color="green" icon={<CheckCircleOutlined />}>
                    Verified Purchase
                  </Tag>
                )}
              </Space>
              <div style={{ marginTop: '4px' }}>
                <Rate disabled value={review.rating} style={{ fontSize: '14px' }} />
                <Text strong style={{ marginLeft: '8px', fontSize: '16px' }}>
                  {review.title}
                </Text>
              </div>
            </div>
          }
          description={
            <div>
              <Text>{review.comment}</Text>
              {review.images && review.images.length > 0 && (
                <div style={{ marginTop: '12px' }}>
                  <Image.PreviewGroup>
                    {review.images.map((image, index) => (
                      <Image
                        key={index}
                        src={image}
                        alt={`Review image ${index + 1}`}
                        width={80}
                        height={80}
                        style={{ 
                          marginRight: '8px', 
                          objectFit: 'cover',
                          borderRadius: '4px'
                        }}
                      />
                    ))}
                  </Image.PreviewGroup>
                </div>
              )}
              <div style={{ marginTop: '12px' }}>
                <Space>
                  <CalendarOutlined />
                  <Text type="secondary">{formatDate(review.createdAt)}</Text>
                  {review.updatedAt !== review.createdAt && (
                    <Text type="secondary">(edited)</Text>
                  )}
                </Space>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <Card>
      <div style={{ marginBottom: '24px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4}>Customer Reviews</Title>
          </Col>
          <Col>
            <Space>
              <Text>Sort by:</Text>
              <Select
                value={filters.sort}
                onChange={handleSortChange}
                style={{ width: 120 }}
              >
                <Option value="newest">Newest</Option>
                <Option value="oldest">Oldest</Option>
                <Option value="highest">Highest Rated</Option>
                <Option value="lowest">Lowest Rated</Option>
                <Option value="helpful">Most Helpful</Option>
              </Select>
              {showAddReview && user && (
                <Button
                  type="primary"
                  onClick={() => setShowReviewForm(true)}
                  style={{
                    background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                    border: 'none',
                  }}
                >
                  Write Review
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </div>

      {statistics && renderRatingDistribution(statistics)}

      {productReviews.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="No reviews yet"
        >
          {showAddReview && user && (
            <Button
              type="primary"
              onClick={() => setShowReviewForm(true)}
            >
              Be the first to review
            </Button>
          )}
        </Empty>
      ) : (
        <List
          dataSource={productReviews}
          renderItem={renderReviewItem}
          loading={loading}
          pagination={{
            current: pagination.currentPage,
            total: pagination.totalItems,
            pageSize: pagination.itemsPerPage,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} reviews`,
          }}
        />
      )}

      {/* Review Form Modal */}
      <Modal
        title={editingReview ? 'Edit Review' : 'Write Review'}
        open={showReviewForm || !!editingReview}
        onCancel={() => {
          setShowReviewForm(false);
          setEditingReview(null);
        }}
        footer={null}
        width={600}
      >
        <ReviewForm
          productId={productId}
          review={editingReview}
          onSuccess={() => {
            setShowReviewForm(false);
            setEditingReview(null);
          }}
          onCancel={() => {
            setShowReviewForm(false);
            setEditingReview(null);
          }}
        />
      </Modal>
    </Card>
  );
};

export default ReviewList;
