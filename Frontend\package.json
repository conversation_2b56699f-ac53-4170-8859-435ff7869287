{"name": "cross-border-ecommerce", "version": "1.0.0", "description": "A cross-border e-commerce platform.", "main": "src/main.tsx", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@ant-design/plots": "^2.5.0", "@reduxjs/toolkit": "^1.9.3", "@types/lodash": "^4.17.19", "antd": "^5.3.0", "axios": "^1.3.4", "date-fns": "^4.1.0", "dayjs": "^1.11.7", "i18next": "^22.4.13", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^12.2.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^8.0.5", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "redux": "^4.2.1", "styled-components": "^5.3.9", "swiper": "^9.1.1"}, "devDependencies": {"@types/node": "^18.19.112", "@types/prop-types": "^15.7.15", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.26", "typescript": "^4.9.5"}, "keywords": ["ecommerce", "cross-border", "react", "redux", "typescript"], "author": "Your Name", "license": "MIT", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}