import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  /**
   * Connect to the socket server
   */
  connect(authToken: string) {
    if (this.socket?.connected) {
      return;
    }

    this.token = authToken;
    
    this.socket = io(process.env.REACT_APP_API_URL || 'http://localhost:5007', {
      auth: {
        token: authToken
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventListeners();
  }

  /**
   * Disconnect from the socket server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.token = null;
    this.reconnectAttempts = 0;
  }

  /**
   * Check if socket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Emit an event to the server
   */
  emit(event: string, data?: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket not connected. Cannot emit event:', event);
    }
  }

  /**
   * Listen for an event from the server
   */
  on(event: string, callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event: string, callback?: (data: any) => void) {
    if (this.socket) {
      if (callback) {
        this.socket.off(event, callback);
      } else {
        this.socket.off(event);
      }
    }
  }

  /**
   * Join a chat room
   */
  joinChat(chatId: string) {
    this.emit('join_chat', { chatId });
  }

  /**
   * Leave a chat room
   */
  leaveChat(chatId: string) {
    this.emit('leave_chat', { chatId });
  }

  /**
   * Send typing indicator
   */
  startTyping(chatId: string) {
    this.emit('typing_start', { chatId });
  }

  /**
   * Stop typing indicator
   */
  stopTyping(chatId: string) {
    this.emit('typing_stop', { chatId });
  }

  /**
   * Confirm message delivery
   */
  confirmMessageDelivery(messageId: string, chatId: string) {
    this.emit('message_delivered', { messageId, chatId });
  }

  /**
   * Confirm message read
   */
  confirmMessageRead(messageId: string, chatId: string) {
    this.emit('message_read', { messageId, chatId });
  }

  /**
   * Update agent status (for agents only)
   */
  updateAgentStatus(status: {
    status?: 'online' | 'offline' | 'busy' | 'away' | 'break';
    availability?: boolean;
    statusMessage?: string;
  }) {
    this.emit('update_agent_status', status);
  }

  /**
   * Send heartbeat ping
   */
  ping() {
    this.emit('ping');
  }

  /**
   * Setup socket event listeners
   */
  private setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connect', () => {
      console.log('✅ Socket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      
      // Attempt to reconnect if disconnection was not intentional
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect
        return;
      }
      
      this.attemptReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.attemptReconnect();
    });

    // Authentication events
    this.socket.on('authenticated', (data) => {
      console.log('✅ Socket authenticated:', data);
    });

    this.socket.on('authentication_error', (error) => {
      console.error('❌ Socket authentication error:', error);
    });

    // Chat events
    this.socket.on('connected', (data) => {
      console.log('✅ Connected to chat service:', data);
    });

    this.socket.on('joined_chat', (data) => {
      console.log('✅ Joined chat:', data.chatId);
    });

    this.socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Heartbeat
    this.socket.on('pong', (data) => {
      // Handle pong response if needed
    });

    // Setup periodic ping
    setInterval(() => {
      if (this.socket?.connected) {
        this.ping();
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Attempt to reconnect to the socket server
   */
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff

    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      if (this.token) {
        this.connect(this.token);
      }
    }, delay);
  }

  /**
   * Get socket instance (for advanced usage)
   */
  getSocket(): Socket | null {
    return this.socket;
  }
}

export const socketService = new SocketService();
export default socketService;
