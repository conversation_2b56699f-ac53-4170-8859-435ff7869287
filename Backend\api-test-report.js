const fetch = require('node-fetch');

class APITester {
  constructor(baseUrl = 'http://localhost:5007') {
    this.baseUrl = baseUrl;
    this.results = [];
  }

  async test(name, url, options = {}) {
    try {
      console.log(`🧪 Testing: ${name}`);
      const response = await fetch(`${this.baseUrl}${url}`, options);
      const data = await response.json();
      
      const result = {
        name,
        url,
        status: response.status,
        success: response.ok,
        data: data
      };
      
      this.results.push(result);
      
      if (response.ok) {
        console.log(`✅ ${name}: SUCCESS (${response.status})`);
      } else {
        console.log(`❌ ${name}: FAILED (${response.status}) - ${data.message || 'Unknown error'}`);
      }
      
      return result;
    } catch (error) {
      const result = {
        name,
        url,
        status: 0,
        success: false,
        error: error.message
      };
      
      this.results.push(result);
      console.log(`❌ ${name}: ERROR - ${error.message}`);
      return result;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting NexaShop API Comprehensive Testing...\n');

    // 1. 基础健康检查
    console.log('📋 1. Health Checks');
    await this.test('Upload Health Check', '/api/v1/upload/health');
    
    // 2. 产品相关API
    console.log('\n📦 2. Product APIs');
    await this.test('Get Products', '/api/v1/products');
    await this.test('Get Categories', '/api/v1/categories');
    
    // 3. 支付系统测试
    console.log('\n💳 3. Payment System');
    await this.test('PayPal Create Order', '/api/v1/payments/paypal/create-order', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        orderId: 'test-order-' + Date.now(),
        amount: 99.99,
        currency: 'USD'
      })
    });
    
    await this.test('Stripe Create Payment Intent', '/api/v1/payments/stripe/create-payment-intent', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        orderId: 'test-order-' + Date.now(),
        amount: 149.99,
        currency: 'USD'
      })
    });
    
    // 4. 物流系统
    console.log('\n🚚 4. Logistics System');
    await this.test('Get Carriers', '/api/v1/logistics/carriers');
    await this.test('Calculate Shipping Rates', '/api/v1/logistics/rates', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        originAddress: { country: 'US', state: 'CA', city: 'Los Angeles', zipCode: '90210' },
        destinationAddress: { country: 'US', state: 'NY', city: 'New York', zipCode: '10001' },
        packages: [{ weight: 1.5, length: 10, width: 8, height: 6 }],
        orderValue: 100
      })
    });
    
    // 5. 产品变体
    console.log('\n🎨 5. Product Variants');
    await this.test('Get Variant Attributes', '/api/v1/product-variants/attributes');
    
    // 6. 需要认证的API（预期失败）
    console.log('\n🔐 6. Protected APIs (Expected to require authentication)');
    await this.test('Get Orders (Protected)', '/api/v1/orders');
    await this.test('Get Coupons (Protected)', '/api/v1/coupons');
    await this.test('Get Refunds (Protected)', '/api/v1/refunds');
    await this.test('Upload Image (Protected)', '/api/v1/upload/image');
    
    // 7. 用户认证测试
    console.log('\n👤 7. Authentication Tests');
    await this.test('Register Existing User', '/api/v1/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '123456',
        first_name: 'Test',
        last_name: 'User'
      })
    });
    
    // 8. 国际化
    console.log('\n🌍 8. Internationalization');
    await this.test('Get Languages', '/api/v1/i18n/languages');
    await this.test('Get Currencies', '/api/v1/i18n/currencies');
    
    this.generateReport();
  }

  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 API TESTING SUMMARY REPORT');
    console.log('='.repeat(80));
    
    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const total = this.results.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((successful / total) * 100).toFixed(1)}%`);
    
    console.log('\n📋 Detailed Results:');
    console.log('-'.repeat(80));
    
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const statusCode = result.status || 'ERR';
      console.log(`${status} ${result.name.padEnd(35)} | ${statusCode} | ${result.url}`);
    });
    
    console.log('\n🔍 Analysis:');
    console.log('-'.repeat(80));
    
    const publicAPIs = this.results.filter(r => 
      !r.url.includes('/orders') && 
      !r.url.includes('/coupons') && 
      !r.url.includes('/refunds') && 
      !r.url.includes('/upload/image') &&
      !r.name.includes('Register Existing User')
    );
    
    const protectedAPIs = this.results.filter(r => 
      r.url.includes('/orders') || 
      r.url.includes('/coupons') || 
      r.url.includes('/refunds') || 
      r.url.includes('/upload/image')
    );
    
    const publicSuccess = publicAPIs.filter(r => r.success).length;
    const protectedCorrectlyBlocked = protectedAPIs.filter(r => !r.success && r.status === 401).length;
    
    console.log(`🌐 Public APIs Working: ${publicSuccess}/${publicAPIs.length}`);
    console.log(`🔒 Protected APIs Correctly Secured: ${protectedCorrectlyBlocked}/${protectedAPIs.length}`);
    
    if (publicSuccess === publicAPIs.length && protectedCorrectlyBlocked === protectedAPIs.length) {
      console.log('\n🎉 SYSTEM STATUS: EXCELLENT - All core functionality working correctly!');
    } else if (publicSuccess >= publicAPIs.length * 0.8) {
      console.log('\n✅ SYSTEM STATUS: GOOD - Most functionality working correctly');
    } else {
      console.log('\n⚠️ SYSTEM STATUS: NEEDS ATTENTION - Some core functionality issues detected');
    }
    
    console.log('='.repeat(80));
  }
}

// 运行测试
const tester = new APITester();
tester.runAllTests().catch(console.error);
