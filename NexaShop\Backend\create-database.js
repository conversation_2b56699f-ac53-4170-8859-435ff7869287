const { Client } = require('pg');

async function createDatabase() {
  // Connect to PostgreSQL server (not to a specific database)
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    user: 'postgres',
    password: 'wasd080980!',
    database: 'postgres' // Connect to default postgres database
  });

  try {
    await client.connect();
    console.log('📋 Connected to PostgreSQL server');

    // Check if database exists
    const checkDbQuery = `
      SELECT 1 FROM pg_database WHERE datname = 'nexashop'
    `;
    
    const result = await client.query(checkDbQuery);
    
    if (result.rows.length === 0) {
      // Database doesn't exist, create it
      console.log('🔄 Creating nexashop database...');
      await client.query('CREATE DATABASE nexashop');
      console.log('✅ Database nexashop created successfully');
    } else {
      console.log('📋 Database nexashop already exists');
    }

    // Also create test database if it doesn't exist
    const checkTestDbQuery = `
      SELECT 1 FROM pg_database WHERE datname = 'nexashop_test'
    `;
    
    const testResult = await client.query(checkTestDbQuery);
    
    if (testResult.rows.length === 0) {
      console.log('🔄 Creating nexashop_test database...');
      await client.query('CREATE DATABASE nexashop_test');
      console.log('✅ Database nexashop_test created successfully');
    } else {
      console.log('📋 Database nexashop_test already exists');
    }

  } catch (error) {
    console.error('❌ Error creating database:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// Run if this file is executed directly
if (require.main === module) {
  createDatabase()
    .then(() => {
      console.log('🎉 Database setup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database setup failed:', error.message);
      process.exit(1);
    });
}

module.exports = createDatabase;
