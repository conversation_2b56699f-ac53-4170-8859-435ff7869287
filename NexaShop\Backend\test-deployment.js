const axios = require('axios');

const BASE_URL = 'http://localhost:5007/api/v1';

async function testDeployment() {
  console.log('🚀 开始测试NexaShop高优先级功能部署...\n');

  try {
    // 测试服务器是否运行
    console.log('1. 测试服务器连接...');
    const healthCheck = await axios.get('http://localhost:5007/').catch(() => null);
    if (!healthCheck) {
      console.log('❌ 服务器未运行，请先启动服务器');
      return;
    }
    console.log('✅ 服务器连接正常\n');

    // 测试退款系统API
    console.log('2. 测试退款系统API...');
    try {
      const refundsResponse = await axios.get(`${BASE_URL}/refunds`);
      console.log('✅ 退款列表API正常');
    } catch (error) {
      console.log('❌ 退款API错误:', error.response?.status || error.message);
    }

    // 测试优惠券系统API
    console.log('3. 测试优惠券系统API...');
    try {
      const couponsResponse = await axios.get(`${BASE_URL}/coupons`);
      console.log('✅ 优惠券列表API正常');
    } catch (error) {
      console.log('❌ 优惠券API错误:', error.response?.status || error.message);
    }

    // 测试商品变体API
    console.log('4. 测试商品变体API...');
    try {
      const variantsResponse = await axios.get(`${BASE_URL}/variant-attributes`);
      console.log('✅ 变体属性API正常');
    } catch (error) {
      console.log('❌ 变体API错误:', error.response?.status || error.message);
    }

    // 测试物流系统API
    console.log('5. 测试物流系统API...');
    try {
      const carriersResponse = await axios.get(`${BASE_URL}/shipping/carriers`);
      console.log('✅ 物流载体API正常');
    } catch (error) {
      console.log('❌ 物流API错误:', error.response?.status || error.message);
    }

    console.log('\n🎉 部署测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testDeployment();
