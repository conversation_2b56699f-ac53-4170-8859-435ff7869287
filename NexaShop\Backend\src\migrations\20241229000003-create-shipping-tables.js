'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Create remaining Logistics tables
      await queryInterface.createTable('ShippingRates', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        service_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_services',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        origin_country: {
          type: Sequelize.STRING(2),
          allowNull: false
        },
        destination_country: {
          type: Sequelize.STRING(2),
          allowNull: false
        },
        origin_zone: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        destination_zone: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        weight_min: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: false,
          defaultValue: 0
        },
        weight_max: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: true
        },
        rate: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        currency: {
          type: Sequelize.STRING(3),
          allowNull: false,
          defaultValue: 'USD'
        },
        effective_date: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        expiry_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('Shipments', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        shipment_number: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true
        },
        order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        carrier_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_carriers',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        service_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_services',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        tracking_number: {
          type: Sequelize.STRING(100),
          allowNull: true,
          index: true
        },
        status: {
          type: Sequelize.ENUM('created', 'label_generated', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned', 'cancelled', 'exception'),
          allowNull: false,
          defaultValue: 'created'
        },
        origin_address: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        destination_address: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        package_details: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        shipping_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        insurance_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false,
          defaultValue: 0
        },
        total_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        currency: {
          type: Sequelize.STRING(3),
          allowNull: false,
          defaultValue: 'USD'
        },
        estimated_delivery_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        actual_delivery_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        label_url: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        carrier_response: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: {}
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('TrackingEvents', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        shipment_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipments',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        event_type: {
          type: Sequelize.ENUM('created', 'label_created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned', 'exception', 'customs_clearance'),
          allowNull: false
        },
        status: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        location: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        event_time: {
          type: Sequelize.DATE,
          allowNull: false
        },
        carrier_event_code: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        carrier_raw_data: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        is_delivered: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        is_exception: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // Create indexes for new tables only
      await queryInterface.addIndex('shipments', ['order_id'], { transaction });
      await queryInterface.addIndex('shipments', ['tracking_number'], { transaction });
      await queryInterface.addIndex('shipments', ['status'], { transaction });

      await queryInterface.addIndex('tracking_events', ['shipment_id'], { transaction });
      await queryInterface.addIndex('tracking_events', ['event_time'], { transaction });

      // Add unique constraints for new tables only
      // (Other constraints are already created in previous migrations)

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Drop tables in reverse order
      await queryInterface.dropTable('tracking_events', { transaction });
      await queryInterface.dropTable('shipments', { transaction });
      await queryInterface.dropTable('shipping_rates', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
