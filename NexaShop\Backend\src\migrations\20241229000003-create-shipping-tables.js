'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Create remaining Logistics tables
      await queryInterface.createTable('ShippingRates', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        service_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_services',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        origin_country: {
          type: Sequelize.STRING(2),
          allowNull: false
        },
        destination_country: {
          type: Sequelize.STRING(2),
          allowNull: false
        },
        origin_zone: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        destination_zone: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        weight_min: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: false,
          defaultValue: 0
        },
        weight_max: {
          type: Sequelize.DECIMAL(8, 3),
          allowNull: true
        },
        rate: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        currency: {
          type: Sequelize.STRING(3),
          allowNull: false,
          defaultValue: 'USD'
        },
        effective_date: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        expiry_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('Shipments', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        shipment_number: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true
        },
        order_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'orders',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        carrier_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_carriers',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        service_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipping_services',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'RESTRICT'
        },
        tracking_number: {
          type: Sequelize.STRING(100),
          allowNull: true,
          index: true
        },
        status: {
          type: Sequelize.ENUM('created', 'label_generated', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned', 'cancelled', 'exception'),
          allowNull: false,
          defaultValue: 'created'
        },
        origin_address: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        destination_address: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        package_details: {
          type: Sequelize.JSONB,
          allowNull: false
        },
        shipping_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        insurance_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false,
          defaultValue: 0
        },
        total_cost: {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: false
        },
        currency: {
          type: Sequelize.STRING(3),
          allowNull: false,
          defaultValue: 'USD'
        },
        estimated_delivery_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        actual_delivery_date: {
          type: Sequelize.DATE,
          allowNull: true
        },
        label_url: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        carrier_response: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        notes: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        metadata: {
          type: Sequelize.JSONB,
          allowNull: true,
          defaultValue: {}
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      await queryInterface.createTable('TrackingEvents', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true
        },
        shipment_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'shipments',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE'
        },
        event_type: {
          type: Sequelize.ENUM('created', 'label_created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'failed_delivery', 'returned', 'exception', 'customs_clearance'),
          allowNull: false
        },
        status: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        location: {
          type: Sequelize.STRING(500),
          allowNull: true
        },
        event_time: {
          type: Sequelize.DATE,
          allowNull: false
        },
        carrier_event_code: {
          type: Sequelize.STRING(50),
          allowNull: true
        },
        carrier_raw_data: {
          type: Sequelize.JSONB,
          allowNull: true
        },
        is_delivered: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        is_exception: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.NOW
        }
      }, { transaction });

      // Create indexes for better performance
      await queryInterface.addIndex('Refunds', ['order_id'], { transaction });
      await queryInterface.addIndex('Refunds', ['status'], { transaction });
      await queryInterface.addIndex('Refunds', ['refund_id'], { transaction });
      
      await queryInterface.addIndex('Coupons', ['code'], { transaction });
      await queryInterface.addIndex('Coupons', ['is_active'], { transaction });
      await queryInterface.addIndex('Coupons', ['starts_at', 'expires_at'], { transaction });
      
      await queryInterface.addIndex('ProductVariants', ['product_id'], { transaction });
      await queryInterface.addIndex('ProductVariants', ['sku'], { transaction });
      await queryInterface.addIndex('ProductVariants', ['is_active'], { transaction });
      
      await queryInterface.addIndex('Shipments', ['order_id'], { transaction });
      await queryInterface.addIndex('Shipments', ['tracking_number'], { transaction });
      await queryInterface.addIndex('Shipments', ['status'], { transaction });
      
      await queryInterface.addIndex('TrackingEvents', ['shipment_id'], { transaction });
      await queryInterface.addIndex('TrackingEvents', ['event_time'], { transaction });

      // Add unique constraints
      await queryInterface.addConstraint('CouponProducts', {
        fields: ['coupon_id', 'product_id'],
        type: 'unique',
        name: 'unique_coupon_product',
        transaction
      });

      await queryInterface.addConstraint('CouponCategories', {
        fields: ['coupon_id', 'category_id'],
        type: 'unique',
        name: 'unique_coupon_category',
        transaction
      });

      await queryInterface.addConstraint('CouponUsers', {
        fields: ['coupon_id', 'user_id'],
        type: 'unique',
        name: 'unique_coupon_user',
        transaction
      });

      await queryInterface.addConstraint('ProductVariantAttributeValues', {
        fields: ['variant_id', 'attribute_value_id'],
        type: 'unique',
        name: 'unique_variant_attribute_value',
        transaction
      });

      await queryInterface.addConstraint('VariantAttributes', {
        fields: ['name'],
        type: 'unique',
        name: 'unique_variant_attribute_name',
        transaction
      });

      await queryInterface.addConstraint('ShippingCarriers', {
        fields: ['code'],
        type: 'unique',
        name: 'unique_carrier_code',
        transaction
      });

      await queryInterface.addConstraint('ShippingServices', {
        fields: ['carrier_id', 'code'],
        type: 'unique',
        name: 'unique_service_code_per_carrier',
        transaction
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // Drop tables in reverse order
      await queryInterface.dropTable('TrackingEvents', { transaction });
      await queryInterface.dropTable('Shipments', { transaction });
      await queryInterface.dropTable('ShippingRates', { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
