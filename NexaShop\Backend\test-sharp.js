// 测试Sharp包是否可用
console.log('开始测试Sharp包...');

try {
  const sharp = require('sharp');
  console.log('✅ Sharp包加载成功');
  console.log('Sharp版本:', sharp.versions);
  
  // 测试基本功能
  const testBuffer = Buffer.alloc(100);
  sharp(testBuffer)
    .resize(50, 50)
    .jpeg()
    .toBuffer()
    .then(() => {
      console.log('✅ Sharp基本功能测试成功');
    })
    .catch(err => {
      console.log('❌ Sharp功能测试失败:', err.message);
    });
    
} catch (error) {
  console.log('❌ Sharp包加载失败:', error.message);
  console.log('错误详情:', error);
  
  // 检查是否存在sharp目录
  const fs = require('fs');
  const path = require('path');
  
  const sharpPath = path.join(__dirname, 'node_modules', 'sharp');
  if (fs.existsSync(sharpPath)) {
    console.log('Sharp目录存在:', sharpPath);
    const files = fs.readdirSync(sharpPath);
    console.log('Sharp目录内容:', files);
  } else {
    console.log('Sharp目录不存在:', sharpPath);
  }
}

console.log('测试完成');
