import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Upload,
  Row,
  Col,
  Typography,
  Divider,
  message,
  Space,
  Tag,
  Alert,
  List,
  Modal
} from 'antd';
import {
  SettingOutlined,
  GlobalOutlined,
  DollarOutlined,
  MailOutlined,
  BellOutlined,
  SecurityScanOutlined,
  CloudUploadOutlined,
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface SystemSettings {
  general: {
    siteName: string;
    siteDescription: string;
    siteUrl: string;
    logo: string;
    favicon: string;
    timezone: string;
    language: string;
    currency: string;
    maintenanceMode: boolean;
  };
  email: {
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpPassword: string;
    smtpSecure: boolean;
    fromEmail: string;
    fromName: string;
  };
  payment: {
    stripeEnabled: boolean;
    stripePublishableKey: string;
    stripeSecretKey: string;
    paypalEnabled: boolean;
    paypalClientId: string;
    paypalClientSecret: string;
    paypalSandbox: boolean;
  };
  shipping: {
    freeShippingThreshold: number;
    defaultShippingRate: number;
    shippingZones: ShippingZone[];
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    orderNotifications: boolean;
    marketingEmails: boolean;
  };
  security: {
    twoFactorAuth: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireStrongPassword: boolean;
  };
}

interface ShippingZone {
  id: string;
  name: string;
  countries: string[];
  rate: number;
}

const SystemSettings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const [generalForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [shippingForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [securityForm] = Form.useForm();

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/v1/admin/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.data.success) {
        const settingsData = response.data.data.settings;
        setSettings(settingsData);
        
        // Populate forms with current settings
        generalForm.setFieldsValue(settingsData.general);
        emailForm.setFieldsValue(settingsData.email);
        paymentForm.setFieldsValue(settingsData.payment);
        shippingForm.setFieldsValue(settingsData.shipping);
        notificationForm.setFieldsValue(settingsData.notifications);
        securityForm.setFieldsValue(settingsData.security);
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
      message.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async (section: string, values: any) => {
    try {
      setLoading(true);
      await axios.put(`/api/v1/admin/settings/${section}`, values, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      message.success(`${section} settings saved successfully`);
      fetchSettings();
    } catch (error) {
      console.error(`Failed to save ${section} settings:`, error);
      message.error(`Failed to save ${section} settings`);
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    try {
      await axios.post('/api/v1/admin/settings/test-email', {}, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      message.success('Test email sent successfully');
    } catch (error) {
      console.error('Failed to send test email:', error);
      message.error('Failed to send test email');
    }
  };

  const renderGeneralSettings = () => (
    <Card title="General Settings" extra={
      <Button 
        type="primary" 
        icon={<SaveOutlined />}
        onClick={() => generalForm.submit()}
        loading={loading}
      >
        Save Changes
      </Button>
    }>
      <Form
        form={generalForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('general', values)}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="siteName"
              label="Site Name"
              rules={[{ required: true, message: 'Please enter site name' }]}
            >
              <Input placeholder="Enter site name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="siteUrl"
              label="Site URL"
              rules={[{ required: true, message: 'Please enter site URL' }]}
            >
              <Input placeholder="https://example.com" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="siteDescription"
          label="Site Description"
        >
          <TextArea rows={3} placeholder="Enter site description" />
        </Form.Item>

        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              name="timezone"
              label="Timezone"
            >
              <Select placeholder="Select timezone">
                <Option value="UTC">UTC</Option>
                <Option value="America/New_York">Eastern Time</Option>
                <Option value="America/Chicago">Central Time</Option>
                <Option value="America/Denver">Mountain Time</Option>
                <Option value="America/Los_Angeles">Pacific Time</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="language"
              label="Default Language"
            >
              <Select placeholder="Select language">
                <Option value="en">English</Option>
                <Option value="es">Spanish</Option>
                <Option value="fr">French</Option>
                <Option value="de">German</Option>
                <Option value="zh">Chinese</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="currency"
              label="Default Currency"
            >
              <Select placeholder="Select currency">
                <Option value="USD">USD - US Dollar</Option>
                <Option value="EUR">EUR - Euro</Option>
                <Option value="GBP">GBP - British Pound</Option>
                <Option value="JPY">JPY - Japanese Yen</Option>
                <Option value="CNY">CNY - Chinese Yuan</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="logo"
              label="Site Logo"
            >
              <Upload
                listType="picture-card"
                maxCount={1}
                beforeUpload={() => false}
              >
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>Upload Logo</div>
                </div>
              </Upload>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="favicon"
              label="Favicon"
            >
              <Upload
                listType="picture-card"
                maxCount={1}
                beforeUpload={() => false}
              >
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>Upload Favicon</div>
                </div>
              </Upload>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="maintenanceMode"
          label="Maintenance Mode"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        {settings?.general.maintenanceMode && (
          <Alert
            message="Maintenance Mode Active"
            description="Your site is currently in maintenance mode. Visitors will see a maintenance page."
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Form>
    </Card>
  );

  const renderEmailSettings = () => (
    <Card title="Email Settings" extra={
      <Space>
        <Button onClick={handleTestEmail}>
          Test Email
        </Button>
        <Button 
          type="primary" 
          icon={<SaveOutlined />}
          onClick={() => emailForm.submit()}
          loading={loading}
        >
          Save Changes
        </Button>
      </Space>
    }>
      <Form
        form={emailForm}
        layout="vertical"
        onFinish={(values) => handleSaveSettings('email', values)}
      >
        <Alert
          message="SMTP Configuration"
          description="Configure your SMTP settings to send emails from your application."
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="smtpHost"
              label="SMTP Host"
              rules={[{ required: true, message: 'Please enter SMTP host' }]}
            >
              <Input placeholder="smtp.gmail.com" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtpPort"
              label="SMTP Port"
              rules={[{ required: true, message: 'Please enter SMTP port' }]}
            >
              <InputNumber placeholder="587" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="smtpUsername"
              label="SMTP Username"
              rules={[{ required: true, message: 'Please enter SMTP username' }]}
            >
              <Input placeholder="<EMAIL>" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="smtpPassword"
              label="SMTP Password"
              rules={[{ required: true, message: 'Please enter SMTP password' }]}
            >
              <Input.Password placeholder="Your app password" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="fromEmail"
              label="From Email"
              rules={[
                { required: true, message: 'Please enter from email' },
                { type: 'email', message: 'Please enter valid email' }
              ]}
            >
              <Input placeholder="<EMAIL>" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="fromName"
              label="From Name"
              rules={[{ required: true, message: 'Please enter from name' }]}
            >
              <Input placeholder="Your Site Name" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="smtpSecure"
          label="Use SSL/TLS"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Form>
    </Card>
  );

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={3} style={{ margin: 0 }}>
          ⚙️ System Settings
        </Title>
        <Text type="secondary">
          Configure your system settings and preferences
        </Text>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        type="card"
      >
        <TabPane 
          tab={<span><GlobalOutlined />General</span>} 
          key="general"
        >
          {renderGeneralSettings()}
        </TabPane>
        
        <TabPane 
          tab={<span><MailOutlined />Email</span>} 
          key="email"
        >
          {renderEmailSettings()}
        </TabPane>
        
        <TabPane 
          tab={<span><DollarOutlined />Payment</span>} 
          key="payment"
        >
          <Card title="Payment Settings">
            <Text>Payment settings configuration will be implemented here.</Text>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><BellOutlined />Notifications</span>} 
          key="notifications"
        >
          <Card title="Notification Settings">
            <Text>Notification settings configuration will be implemented here.</Text>
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><SecurityScanOutlined />Security</span>} 
          key="security"
        >
          <Card title="Security Settings">
            <Text>Security settings configuration will be implemented here.</Text>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SystemSettings;
