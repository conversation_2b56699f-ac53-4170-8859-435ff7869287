const {
  ShippingCarrier,
  ShippingService,
  ShippingRate,
  Shipment,
  TrackingEvent
} = require('../models/Logistics');
const { Order } = require('../models');
const { Op } = require('sequelize');
const axios = require('axios');

class LogisticsService {
  /**
   * Calculate shipping rates for an order
   * @param {Object} rateRequest - Rate calculation request
   * @returns {Object} Available shipping rates
   */
  async calculateShippingRates(rateRequest) {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        orderValue = 0
      } = rateRequest;

      const rates = [];
      
      // Get active carriers and their services
      const carriers = await ShippingCarrier.findAll({
        where: { is_active: true },
        include: [
          {
            model: ShippingService,
            as: 'services',
            where: { is_active: true },
            required: false
          }
        ],
        order: [['sort_order', 'ASC']]
      });

      for (const carrier of carriers) {
        for (const service of carrier.services) {
          try {
            // Calculate rate based on service configuration
            const rate = await this.calculateServiceRate(
              carrier,
              service,
              originAddress,
              destinationAddress,
              packages,
              orderValue
            );

            if (rate) {
              rates.push({
                carrier: {
                  id: carrier.id,
                  name: carrier.name,
                  code: carrier.code,
                  displayName: carrier.display_name,
                  logoUrl: carrier.logo_url
                },
                service: {
                  id: service.id,
                  name: service.name,
                  code: service.code,
                  displayName: service.display_name,
                  serviceType: service.service_type,
                  deliveryTimeMin: service.delivery_time_min,
                  deliveryTimeMax: service.delivery_time_max,
                  isTracked: service.is_tracked,
                  isInsured: service.is_insured
                },
                rate: rate.amount,
                currency: rate.currency,
                estimatedDeliveryDate: rate.estimatedDeliveryDate,
                transitTime: rate.transitTime,
                metadata: rate.metadata || {}
              });
            }
          } catch (error) {
            console.error(`Rate calculation error for ${carrier.code}/${service.code}:`, error);
            // Continue with other services
          }
        }
      }

      return {
        success: true,
        rates: rates.sort((a, b) => a.rate - b.rate) // Sort by price
      };
    } catch (error) {
      console.error('Calculate shipping rates error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Calculate rate for a specific service
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @param {number} orderValue - Order value for insurance
   * @returns {Object} Rate calculation result
   */
  async calculateServiceRate(carrier, service, originAddress, destinationAddress, packages, orderValue) {
    try {
      // Check if service supports the destination country
      if (carrier.supported_countries.length > 0 && 
          !carrier.supported_countries.includes(destinationAddress.country)) {
        return null;
      }

      // Calculate total weight and dimensions
      const totalWeight = packages.reduce((sum, pkg) => sum + (pkg.weight || 0), 0);
      const maxDimensions = packages.reduce((max, pkg) => {
        const dims = pkg.dimensions || {};
        return {
          length: Math.max(max.length || 0, dims.length || 0),
          width: Math.max(max.width || 0, dims.width || 0),
          height: Math.max(max.height || 0, dims.height || 0)
        };
      }, {});

      // Check weight and dimension limits
      if (service.max_weight && totalWeight > service.max_weight) {
        return null;
      }

      if (service.max_dimensions) {
        const maxDims = service.max_dimensions;
        if ((maxDims.length && maxDimensions.length > maxDims.length) ||
            (maxDims.width && maxDimensions.width > maxDims.width) ||
            (maxDims.height && maxDimensions.height > maxDims.height)) {
          return null;
        }
      }

      let rateAmount = 0;
      let estimatedDeliveryDate = null;
      let transitTime = null;

      switch (service.rate_calculation) {
        case 'flat':
          rateAmount = service.base_rate || 0;
          break;

        case 'weight_based':
          rateAmount = await this.calculateWeightBasedRate(service, totalWeight, originAddress, destinationAddress);
          break;

        case 'dimension_based':
          rateAmount = await this.calculateDimensionBasedRate(service, packages, originAddress, destinationAddress);
          break;

        case 'api_calculated':
          const apiRate = await this.getCarrierAPIRate(carrier, service, originAddress, destinationAddress, packages);
          if (!apiRate) return null;
          rateAmount = apiRate.amount;
          estimatedDeliveryDate = apiRate.estimatedDeliveryDate;
          transitTime = apiRate.transitTime;
          break;

        default:
          rateAmount = service.base_rate || 0;
      }

      // Calculate delivery time if not provided by API
      if (!estimatedDeliveryDate && (service.delivery_time_min || service.delivery_time_max)) {
        const deliveryDays = service.delivery_time_max || service.delivery_time_min || 7;
        estimatedDeliveryDate = new Date();
        estimatedDeliveryDate.setDate(estimatedDeliveryDate.getDate() + deliveryDays);
        transitTime = `${service.delivery_time_min || deliveryDays}-${service.delivery_time_max || deliveryDays} business days`;
      }

      return {
        amount: Math.round(rateAmount * 100) / 100, // Round to 2 decimal places
        currency: 'USD',
        estimatedDeliveryDate,
        transitTime,
        metadata: {
          totalWeight,
          packageCount: packages.length,
          calculationMethod: service.rate_calculation
        }
      };
    } catch (error) {
      console.error('Calculate service rate error:', error);
      return null;
    }
  }

  /**
   * Get rate from carrier API
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @returns {Object} API rate response
   */
  async getCarrierAPIRate(carrier, service, originAddress, destinationAddress, packages) {
    try {
      switch (carrier.code) {
        case 'fedex':
          return await this.getFedExRate(carrier, service, originAddress, destinationAddress, packages);
        case 'ups':
          return await this.getUPSRate(carrier, service, originAddress, destinationAddress, packages);
        case 'dhl':
          return await this.getDHLRate(carrier, service, originAddress, destinationAddress, packages);
        case 'usps':
          return await this.getUSPSRate(carrier, service, originAddress, destinationAddress, packages);
        default:
          console.warn(`No API integration for carrier: ${carrier.code}`);
          return null;
      }
    } catch (error) {
      console.error(`Carrier API rate error for ${carrier.code}:`, error);
      return null;
    }
  }

  /**
   * Create shipment
   * @param {Object} shipmentData - Shipment data
   * @returns {Object} Created shipment
   */
  async createShipment(shipmentData) {
    try {
      const {
        orderId,
        carrierId,
        serviceId,
        originAddress,
        destinationAddress,
        packages,
        shippingCost,
        insuranceCost = 0,
        notes,
        metadata = {}
      } = shipmentData;

      // Validate order exists
      const order = await Order.findByPk(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // Generate shipment number
      const shipmentNumber = await this.generateShipmentNumber();

      // Create shipment record
      const shipment = await Shipment.create({
        shipment_number: shipmentNumber,
        order_id: orderId,
        carrier_id: carrierId,
        service_id: serviceId,
        origin_address: originAddress,
        destination_address: destinationAddress,
        package_details: packages,
        shipping_cost: shippingCost,
        insurance_cost: insuranceCost,
        total_cost: parseFloat(shippingCost) + parseFloat(insuranceCost),
        notes,
        metadata
      });

      return {
        success: true,
        shipment
      };
    } catch (error) {
      console.error('Create shipment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate unique shipment number
   * @returns {string} Shipment number
   */
  async generateShipmentNumber() {
    const prefix = 'SHP';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Generate shipping label
   * @param {string} shipmentId - Shipment ID
   * @returns {Object} Label generation result
   */
  async generateShippingLabel(shipmentId) {
    try {
      const shipment = await Shipment.findByPk(shipmentId, {
        include: [
          { model: ShippingCarrier, as: 'carrier' },
          { model: ShippingService, as: 'service' }
        ]
      });

      if (!shipment) {
        throw new Error('Shipment not found');
      }

      // Generate label based on carrier
      const labelResult = await this.generateCarrierLabel(shipment);

      if (labelResult.success) {
        // Update shipment with tracking number and label URL
        await shipment.update({
          tracking_number: labelResult.trackingNumber,
          label_url: labelResult.labelUrl,
          status: 'label_generated',
          carrier_response: labelResult.carrierResponse
        });

        // Create tracking event
        await this.createTrackingEvent({
          shipmentId: shipment.id,
          eventType: 'label_created',
          status: 'Label Created',
          description: 'Shipping label has been generated',
          eventTime: new Date()
        });
      }

      return labelResult;
    } catch (error) {
      console.error('Generate shipping label error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Track shipment
   * @param {string} trackingNumber - Tracking number
   * @param {string} carrierCode - Carrier code
   * @returns {Object} Tracking information
   */
  async trackShipment(trackingNumber, carrierCode = null) {
    try {
      let shipment;

      if (carrierCode) {
        // Find shipment by tracking number and carrier
        shipment = await Shipment.findOne({
          where: { tracking_number: trackingNumber },
          include: [
            {
              model: ShippingCarrier,
              as: 'carrier',
              where: { code: carrierCode }
            },
            { model: ShippingService, as: 'service' }
          ]
        });
      } else {
        // Find shipment by tracking number only
        shipment = await Shipment.findOne({
          where: { tracking_number: trackingNumber },
          include: [
            { model: ShippingCarrier, as: 'carrier' },
            { model: ShippingService, as: 'service' }
          ]
        });
      }

      if (!shipment) {
        throw new Error('Shipment not found');
      }

      // Get tracking information from carrier API
      const trackingInfo = await this.getCarrierTrackingInfo(shipment);

      // Update shipment status if needed
      if (trackingInfo.success && trackingInfo.status !== shipment.status) {
        await shipment.update({
          status: trackingInfo.status,
          actual_delivery_date: trackingInfo.deliveredAt
        });
      }

      return {
        success: true,
        shipment: {
          id: shipment.id,
          shipmentNumber: shipment.shipment_number,
          trackingNumber: shipment.tracking_number,
          status: trackingInfo.status || shipment.status,
          estimatedDeliveryDate: shipment.estimated_delivery_date,
          actualDeliveryDate: trackingInfo.deliveredAt || shipment.actual_delivery_date
        },
        tracking: trackingInfo
      };
    } catch (error) {
      console.error('Track shipment error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create tracking event
   * @param {Object} eventData - Event data
   * @returns {Object} Created event
   */
  async createTrackingEvent(eventData) {
    try {
      const {
        shipmentId,
        eventType,
        status,
        description,
        location,
        eventTime,
        carrierEventCode,
        carrierRawData,
        isDelivered = false,
        isException = false
      } = eventData;

      const event = await TrackingEvent.create({
        shipment_id: shipmentId,
        event_type: eventType,
        status,
        description,
        location,
        event_time: eventTime,
        carrier_event_code: carrierEventCode,
        carrier_raw_data: carrierRawData,
        is_delivered: isDelivered,
        is_exception: isException
      });

      return {
        success: true,
        event
      };
    } catch (error) {
      console.error('Create tracking event error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get shipment tracking events
   * @param {string} shipmentId - Shipment ID
   * @returns {Array} Tracking events
   */
  async getShipmentTrackingEvents(shipmentId) {
    return await TrackingEvent.findAll({
      where: { shipment_id: shipmentId },
      order: [['event_time', 'DESC']]
    });
  }

  /**
   * Calculate weight-based rate
   * @param {Object} service - Service object
   * @param {number} weight - Total weight
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @returns {number} Calculated rate
   */
  async calculateWeightBasedRate(service, weight, originAddress, destinationAddress) {
    // Find applicable rate based on weight and location
    const rate = await ShippingRate.findOne({
      where: {
        service_id: service.id,
        origin_country: originAddress.country,
        destination_country: destinationAddress.country,
        weight_min: { [Op.lte]: weight },
        [Op.or]: [
          { weight_max: { [Op.gte]: weight } },
          { weight_max: null }
        ],
        is_active: true,
        effective_date: { [Op.lte]: new Date() },
        [Op.or]: [
          { expiry_date: { [Op.gte]: new Date() } },
          { expiry_date: null }
        ]
      },
      order: [['weight_min', 'DESC']]
    });

    return rate ? rate.rate : service.base_rate || 0;
  }

  /**
   * FedEx API integration (placeholder)
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @returns {Object} Rate response
   */
  async getFedExRate(carrier, service, originAddress, destinationAddress, packages) {
    // Placeholder for FedEx API integration
    // In production, this would make actual API calls to FedEx
    return {
      amount: 15.99,
      currency: 'USD',
      estimatedDeliveryDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      transitTime: '2-3 business days'
    };
  }

  /**
   * UPS API integration (placeholder)
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @returns {Object} Rate response
   */
  async getUPSRate(carrier, service, originAddress, destinationAddress, packages) {
    // Placeholder for UPS API integration
    return {
      amount: 18.50,
      currency: 'USD',
      estimatedDeliveryDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
      transitTime: '3-4 business days'
    };
  }

  /**
   * DHL API integration (placeholder)
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @returns {Object} Rate response
   */
  async getDHLRate(carrier, service, originAddress, destinationAddress, packages) {
    // Placeholder for DHL API integration
    return {
      amount: 22.75,
      currency: 'USD',
      estimatedDeliveryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      transitTime: '1-2 business days'
    };
  }

  /**
   * USPS API integration (placeholder)
   * @param {Object} carrier - Carrier object
   * @param {Object} service - Service object
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @param {Array} packages - Package details
   * @returns {Object} Rate response
   */
  async getUSPSRate(carrier, service, originAddress, destinationAddress, packages) {
    // Placeholder for USPS API integration
    return {
      amount: 12.50,
      currency: 'USD',
      estimatedDeliveryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
      transitTime: '3-5 business days'
    };
  }

  /**
   * Calculate dimension-based rate
   * @param {Object} service - Service object
   * @param {Array} packages - Package details
   * @param {Object} originAddress - Origin address
   * @param {Object} destinationAddress - Destination address
   * @returns {number} Calculated rate
   */
  async calculateDimensionBasedRate(service, packages, originAddress, destinationAddress) {
    // Calculate dimensional weight
    const dimensionalWeight = packages.reduce((sum, pkg) => {
      const dims = pkg.dimensions || {};
      const dimWeight = (dims.length * dims.width * dims.height) / 166; // Standard divisor
      return sum + Math.max(pkg.weight || 0, dimWeight);
    }, 0);

    // Find applicable rate based on dimensional weight
    const rate = await ShippingRate.findOne({
      where: {
        service_id: service.id,
        origin_country: originAddress.country,
        destination_country: destinationAddress.country,
        weight_min: { [Op.lte]: dimensionalWeight },
        [Op.or]: [
          { weight_max: { [Op.gte]: dimensionalWeight } },
          { weight_max: null }
        ],
        is_active: true,
        effective_date: { [Op.lte]: new Date() },
        [Op.or]: [
          { expiry_date: { [Op.gte]: new Date() } },
          { expiry_date: null }
        ]
      },
      order: [['weight_min', 'DESC']]
    });

    return rate ? rate.rate : service.base_rate || 0;
  }

  /**
   * Generate carrier label (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Label generation result
   */
  async generateCarrierLabel(shipment) {
    try {
      const carrier = shipment.carrier;

      switch (carrier.code) {
        case 'fedex':
          return await this.generateFedExLabel(shipment);
        case 'ups':
          return await this.generateUPSLabel(shipment);
        case 'dhl':
          return await this.generateDHLLabel(shipment);
        case 'usps':
          return await this.generateUSPSLabel(shipment);
        default:
          // Generic label generation
          return {
            success: true,
            trackingNumber: `${carrier.code.toUpperCase()}${Date.now()}${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
            labelUrl: `https://api.example.com/labels/${shipment.id}.pdf`,
            carrierResponse: {
              message: 'Label generated successfully',
              timestamp: new Date().toISOString()
            }
          };
      }
    } catch (error) {
      console.error('Generate carrier label error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get carrier tracking information
   * @param {Object} shipment - Shipment object
   * @returns {Object} Tracking information
   */
  async getCarrierTrackingInfo(shipment) {
    try {
      const carrier = shipment.carrier;

      switch (carrier.code) {
        case 'fedex':
          return await this.getFedExTracking(shipment);
        case 'ups':
          return await this.getUPSTracking(shipment);
        case 'dhl':
          return await this.getDHLTracking(shipment);
        case 'usps':
          return await this.getUSPSTracking(shipment);
        default:
          // Return current shipment status
          return {
            success: true,
            status: shipment.status,
            events: await this.getShipmentTrackingEvents(shipment.id),
            deliveredAt: shipment.actual_delivery_date
          };
      }
    } catch (error) {
      console.error('Get carrier tracking info error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * FedEx label generation (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Label result
   */
  async generateFedExLabel(shipment) {
    // Placeholder for FedEx label API
    return {
      success: true,
      trackingNumber: `FX${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      labelUrl: `https://fedex.com/labels/${shipment.id}.pdf`,
      carrierResponse: { fedexResponse: 'Label created' }
    };
  }

  /**
   * UPS label generation (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Label result
   */
  async generateUPSLabel(shipment) {
    // Placeholder for UPS label API
    return {
      success: true,
      trackingNumber: `1Z${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      labelUrl: `https://ups.com/labels/${shipment.id}.pdf`,
      carrierResponse: { upsResponse: 'Label created' }
    };
  }

  /**
   * DHL label generation (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Label result
   */
  async generateDHLLabel(shipment) {
    // Placeholder for DHL label API
    return {
      success: true,
      trackingNumber: `DHL${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      labelUrl: `https://dhl.com/labels/${shipment.id}.pdf`,
      carrierResponse: { dhlResponse: 'Label created' }
    };
  }

  /**
   * USPS label generation (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Label result
   */
  async generateUSPSLabel(shipment) {
    // Placeholder for USPS label API
    return {
      success: true,
      trackingNumber: `USPS${Date.now()}${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      labelUrl: `https://usps.com/labels/${shipment.id}.pdf`,
      carrierResponse: { uspsResponse: 'Label created' }
    };
  }

  /**
   * FedEx tracking (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Tracking result
   */
  async getFedExTracking(shipment) {
    // Placeholder for FedEx tracking API
    return {
      success: true,
      status: 'in_transit',
      events: [
        {
          status: 'picked_up',
          description: 'Package picked up',
          location: 'Origin facility',
          eventTime: new Date(Date.now() - 24 * 60 * 60 * 1000)
        },
        {
          status: 'in_transit',
          description: 'In transit',
          location: 'Sorting facility',
          eventTime: new Date()
        }
      ]
    };
  }

  /**
   * UPS tracking (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Tracking result
   */
  async getUPSTracking(shipment) {
    // Placeholder for UPS tracking API
    return {
      success: true,
      status: 'in_transit',
      events: [
        {
          status: 'picked_up',
          description: 'Package picked up',
          location: 'Origin facility',
          eventTime: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      ]
    };
  }

  /**
   * DHL tracking (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Tracking result
   */
  async getDHLTracking(shipment) {
    // Placeholder for DHL tracking API
    return {
      success: true,
      status: 'in_transit',
      events: [
        {
          status: 'picked_up',
          description: 'Package picked up',
          location: 'Origin facility',
          eventTime: new Date(Date.now() - 12 * 60 * 60 * 1000)
        }
      ]
    };
  }

  /**
   * USPS tracking (placeholder)
   * @param {Object} shipment - Shipment object
   * @returns {Object} Tracking result
   */
  async getUSPSTracking(shipment) {
    // Placeholder for USPS tracking API
    return {
      success: true,
      status: 'in_transit',
      events: [
        {
          status: 'picked_up',
          description: 'Package picked up',
          location: 'Post office',
          eventTime: new Date(Date.now() - 48 * 60 * 60 * 1000)
        }
      ]
    };
  }
}

module.exports = new LogisticsService();
