const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function testFileUpload() {
  try {
    console.log('🧪 Testing file upload functionality...');
    
    // Create a simple test image
    const testImagePath = path.join(__dirname, 'test-image.png');
    
    // Check if test image exists
    if (!fs.existsSync(testImagePath)) {
      console.log('❌ Test image not found');
      return;
    }
    
    // Create form data
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath));
    form.append('category', 'products');
    
    // Upload the file
    const response = await fetch('http://localhost:5007/api/v1/upload/image', {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ File upload successful:', result);
    } else {
      console.log('❌ File upload failed:', result);
    }
    
  } catch (error) {
    console.error('❌ Upload test error:', error.message);
  }
}

testFileUpload();
