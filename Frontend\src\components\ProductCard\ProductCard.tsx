import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Card,
  Button,
  Typography,
  Space,
  Rate,
  Tag,
  Badge,
  message,
} from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  EyeOutlined,
  HeartFilled,
} from '@ant-design/icons';
import { AppDispatch } from '../../store';
import { addToCart } from '../../store/slices/cartSlice';
import { addToWishlist, removeFromWishlist } from '../../store/slices/userSlice';
import { Product } from '../../store/slices/productSlice';

const { Text } = Typography;
const { Meta } = Card;

interface ProductCardProps {
  product: Product;
  loading?: boolean;
  isInWishlist?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  loading = false, 
  isInWishlist = false 
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    dispatch(addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.images[0],
      shipping: {
        cost: product.shipping.cost,
        estimatedDays: product.shipping.estimatedDays,
        carrier: product.shipping.carriers[0] || 'Standard',
      },
    }));
    
    message.success('Added to cart!');
  };

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isInWishlist) {
      dispatch(removeFromWishlist(product.id));
      message.info('Removed from wishlist');
    } else {
      dispatch(addToWishlist(product.id));
      message.success('Added to wishlist!');
    }
  };

  const discountPrice = product.originalPrice 
    ? product.originalPrice - product.price 
    : 0;
  
  const discountPercentage = product.originalPrice 
    ? Math.round((discountPrice / product.originalPrice) * 100)
    : 0;

  return (
    <Link to={`/products/${product.id}`} style={{ textDecoration: 'none' }}>
      <Card
        hoverable
        loading={loading}
        className="product-card"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          borderRadius: '16px',
          border: 'none',
          boxShadow: isHovered 
            ? '0 12px 40px rgba(0,0,0,0.15)' 
            : '0 4px 20px rgba(0,0,0,0.08)',
          overflow: 'hidden',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',
        }}
        cover={
          <div style={{ 
            position: 'relative', 
            overflow: 'hidden',
            height: '240px',
            background: '#f5f5f5',
          }}>
            <img
              alt={product.name}
              src={product.images?.[0] || 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'}
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'cover',
                transition: 'transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: isHovered ? 'scale(1.1)' : 'scale(1)',
                opacity: imageLoaded ? 1 : 0,
              }}
              onLoad={() => setImageLoaded(true)}
            />
            
            {/* Discount Badge */}
            {product.discount && (
              <Badge.Ribbon
                text={`-${product.discount.percentage}%`}
                color="red"
                style={{
                  fontSize: '12px',
                  fontWeight: 'bold',
                }}
              />
            )}
            
            {/* Quick Actions */}
            <div
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                opacity: isHovered ? 1 : 0,
                transition: 'opacity 0.3s ease',
              }}
            >
              <Button
                type="text"
                shape="circle"
                size="large"
                icon={isInWishlist ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
                style={{ 
                  background: 'rgba(255,255,255,0.95)',
                  backdropFilter: 'blur(10px)',
                  border: 'none',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  width: '44px',
                  height: '44px',
                }}
                onClick={handleWishlistToggle}
              />
              <Button
                type="text"
                shape="circle"
                size="large"
                icon={<EyeOutlined />}
                style={{ 
                  background: 'rgba(255,255,255,0.95)',
                  backdropFilter: 'blur(10px)',
                  border: 'none',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  width: '44px',
                  height: '44px',
                }}
              />
            </div>

            {/* Stock Status */}
            {!product.inStock && (
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'rgba(0,0,0,0.6)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '18px',
                  fontWeight: 'bold',
                }}
              >
                Out of Stock
              </div>
            )}
          </div>
        }
        actions={[
          <Button
            type="primary"
            icon={<ShoppingCartOutlined />}
            onClick={handleAddToCart}
            disabled={!product.inStock}
            block
            size="large"
            style={{
              height: '48px',
              borderRadius: '12px',
              fontWeight: '600',
              fontSize: '14px',
              background: product.inStock 
                ? 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)' 
                : undefined,
              border: 'none',
              boxShadow: product.inStock 
                ? '0 4px 12px rgba(24, 144, 255, 0.3)' 
                : undefined,
            }}
          >
            {product.inStock ? 'Add to Cart' : 'Out of Stock'}
          </Button>,
        ]}
      >
        <Meta
          title={
            <Text
              strong
              style={{
                fontSize: '16px',
                color: '#262626',
                marginBottom: '8px',
                lineHeight: '1.4',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              } as React.CSSProperties}
            >
              {product.name}
            </Text>
          }
          description={
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {/* Rating */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Rate 
                  disabled 
                  defaultValue={product.rating} 
                  style={{ fontSize: '14px' }} 
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  ({product.reviewCount})
                </Text>
              </div>
              
              {/* Price */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Text 
                  strong 
                  style={{ 
                    fontSize: '20px', 
                    color: '#1890ff',
                    fontWeight: '700',
                  }}
                >
                  ${product.price}
                </Text>
                {product.originalPrice && (
                  <Text 
                    delete 
                    type="secondary" 
                    style={{ fontSize: '14px' }}
                  >
                    ${product.originalPrice}
                  </Text>
                )}
                {discountPercentage > 0 && (
                  <Tag color="red" style={{ margin: 0, fontSize: '11px' }}>
                    -{discountPercentage}%
                  </Tag>
                )}
              </div>
              
              {/* Features */}
              <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
                <Tag color="green" style={{ fontSize: '11px', padding: '0 4px' }}>
                  Free Shipping
                </Tag>
                {product.featured && (
                  <Tag color="gold" style={{ fontSize: '11px', padding: '0 4px' }}>
                    Featured
                  </Tag>
                )}
                {product.inStock && (
                  <Tag color="blue" style={{ fontSize: '11px', padding: '0 4px' }}>
                    In Stock
                  </Tag>
                )}
              </div>
            </Space>
          }
        />
      </Card>
    </Link>
  );
};

export default ProductCard;
