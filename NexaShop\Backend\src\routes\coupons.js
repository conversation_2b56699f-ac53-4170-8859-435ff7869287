const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const { authenticate, requireAdmin } = require('../middleware/auth');
const couponService = require('../services/couponService');
const { Coupon, CouponUsage } = require('../models/Coupon');

const router = express.Router();

// @route   POST /api/v1/coupons
// @desc    Create a new coupon (Admin only)
// @access  Private (Admin)
router.post('/', [
  authenticate,
  requireAdmin,
  body('code').isString().isLength({ min: 3, max: 50 }).withMessage('Code must be 3-50 characters'),
  body('name').isString().isLength({ min: 1, max: 200 }).withMessage('Name is required'),
  body('type').isIn(['percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y']).withMessage('Invalid coupon type'),
  body('value').isFloat({ min: 0 }).withMessage('Value must be positive'),
  body('startDate').isISO8601().withMessage('Valid start date is required'),
  body('endDate').optional().isISO8601().withMessage('Valid end date required'),
  body('applicableTo').optional().isIn(['all', 'specific_products', 'specific_categories', 'specific_users']).withMessage('Invalid applicable_to value'),
  body('minimumAmount').optional().isFloat({ min: 0 }).withMessage('Minimum amount must be positive'),
  body('maximumDiscount').optional().isFloat({ min: 0 }).withMessage('Maximum discount must be positive'),
  body('usageLimit').optional().isInt({ min: 1 }).withMessage('Usage limit must be positive'),
  body('usageLimitPerUser').optional().isInt({ min: 1 }).withMessage('Usage limit per user must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const result = await couponService.createCoupon({
      ...req.body,
      createdBy: req.user.id
    });

    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'Coupon created successfully',
        data: { coupon: result.coupon }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Create coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/coupons
// @desc    Get all coupons (Admin only)
// @access  Private (Admin)
router.get('/', [
  authenticate,
  requireAdmin,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be positive'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100'),
  query('status').optional().isString().withMessage('Status must be string'),
  query('type').optional().isString().withMessage('Type must be string'),
  query('search').optional().isString().withMessage('Search must be string')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      type,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (status) whereClause.status = status;
    if (type) whereClause.type = type;
    if (search) {
      whereClause[Op.or] = [
        { code: { [Op.iLike]: `%${search}%` } },
        { name: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const { count, rows: coupons } = await Coupon.findAndCountAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        coupons,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get coupons error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/coupons/available
// @desc    Get available coupons for current user
// @access  Private
router.get('/available', authenticate, async (req, res) => {
  try {
    const coupons = await couponService.getAvailableCouponsForUser(req.user.id);

    res.json({
      success: true,
      data: { coupons }
    });
  } catch (error) {
    console.error('Get available coupons error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/coupons/:id
// @desc    Get coupon by ID
// @access  Private (Admin)
router.get('/:id', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid coupon ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const coupon = await couponService.getCouponById(req.params.id);

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    res.json({
      success: true,
      data: { coupon }
    });
  } catch (error) {
    console.error('Get coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/v1/coupons/validate
// @desc    Validate coupon code for order
// @access  Private
router.post('/validate', [
  authenticate,
  body('code').isString().withMessage('Coupon code is required'),
  body('orderData').isObject().withMessage('Order data is required'),
  body('orderData.subtotal').isFloat({ min: 0 }).withMessage('Valid subtotal required'),
  body('orderData.items').isArray().withMessage('Order items required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { code, orderData } = req.body;

    const result = await couponService.validateAndApplyCoupon(
      code,
      orderData,
      req.user.id
    );

    if (result.valid) {
      res.json({
        success: true,
        message: 'Coupon is valid',
        data: {
          coupon: result.coupon,
          discount: result.discount
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Validate coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   PUT /api/v1/coupons/:id
// @desc    Update coupon (Admin only)
// @access  Private (Admin)
router.put('/:id', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid coupon ID required'),
  body('name').optional().isString().isLength({ min: 1, max: 200 }).withMessage('Name must be 1-200 characters'),
  body('description').optional().isString().withMessage('Description must be string'),
  body('status').optional().isIn(['active', 'inactive', 'expired', 'used_up']).withMessage('Invalid status'),
  body('endDate').optional().isISO8601().withMessage('Valid end date required'),
  body('usageLimit').optional().isInt({ min: 1 }).withMessage('Usage limit must be positive'),
  body('usageLimitPerUser').optional().isInt({ min: 1 }).withMessage('Usage limit per user must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const coupon = await Coupon.findByPk(req.params.id);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    await coupon.update(req.body);

    const updatedCoupon = await couponService.getCouponById(req.params.id);

    res.json({
      success: true,
      message: 'Coupon updated successfully',
      data: { coupon: updatedCoupon }
    });
  } catch (error) {
    console.error('Update coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   DELETE /api/v1/coupons/:id
// @desc    Delete coupon (Admin only)
// @access  Private (Admin)
router.delete('/:id', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid coupon ID required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const coupon = await Coupon.findByPk(req.params.id);
    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: 'Coupon not found'
      });
    }

    // Check if coupon has been used
    const usageCount = await CouponUsage.count({
      where: { coupon_id: req.params.id }
    });

    if (usageCount > 0) {
      // Don't delete, just deactivate
      await coupon.update({ status: 'inactive' });
      res.json({
        success: true,
        message: 'Coupon deactivated (has usage history)'
      });
    } else {
      await coupon.destroy();
      res.json({
        success: true,
        message: 'Coupon deleted successfully'
      });
    }
  } catch (error) {
    console.error('Delete coupon error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/v1/coupons/:id/usage
// @desc    Get coupon usage history (Admin only)
// @access  Private (Admin)
router.get('/:id/usage', [
  authenticate,
  requireAdmin,
  param('id').isUUID().withMessage('Valid coupon ID required'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be positive'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be 1-100')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20
    } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows: usage } = await CouponUsage.findAndCountAll({
      where: { coupon_id: req.params.id },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_number', 'status']
        }
      ],
      order: [['used_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        usage,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get coupon usage error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
