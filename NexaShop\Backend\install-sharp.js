// 手动安装Sharp包的脚本
const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('开始手动安装Sharp包...');

// 检查Node.js版本
console.log('Node.js版本:', process.version);
console.log('平台:', process.platform);
console.log('架构:', process.arch);

// 尝试使用child_process执行npm install
try {
  console.log('尝试使用child_process执行npm install sharp...');
  const result = execSync('npm install sharp', { 
    cwd: __dirname,
    stdio: 'pipe',
    encoding: 'utf8'
  });
  console.log('✅ Sharp安装成功');
  console.log('输出:', result);
} catch (error) {
  console.log('❌ npm install失败:', error.message);
  console.log('错误输出:', error.stdout);
  console.log('错误信息:', error.stderr);
  
  // 尝试替代方案
  console.log('尝试替代方案...');
  
  // 检查是否有yarn
  try {
    execSync('yarn --version', { stdio: 'pipe' });
    console.log('发现yarn，尝试使用yarn安装...');
    const yarnResult = execSync('yarn add sharp', {
      cwd: __dirname,
      stdio: 'pipe',
      encoding: 'utf8'
    });
    console.log('✅ 使用yarn安装Sharp成功');
    console.log('输出:', yarnResult);
  } catch (yarnError) {
    console.log('❌ yarn也不可用:', yarnError.message);
    
    // 最后的手动方案
    console.log('所有包管理器都不可用，需要手动解决');
    console.log('建议解决方案:');
    console.log('1. 检查Node.js是否正确安装');
    console.log('2. 检查PATH环境变量');
    console.log('3. 重新安装Node.js');
    console.log('4. 使用管理员权限运行');
  }
}

// 验证安装结果
try {
  const sharp = require('sharp');
  console.log('✅ Sharp验证成功，版本:', sharp.versions);
} catch (verifyError) {
  console.log('❌ Sharp验证失败:', verifyError.message);
}
